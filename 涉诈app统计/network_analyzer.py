"""
涉案APP监控系统 - 网络关系分析器
负责分析APP间的通联关系、可疑网络识别、关联模式发现
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging
from collections import defaultdict, Counter
import networkx as nx
from itertools import combinations

from config import config
from data_processor import DataProcessor
from analyzers import BaseAnalyzer, AnalysisResult

logger = logging.getLogger(__name__)


@dataclass
class NetworkNode:
    """网络节点"""
    node_id: str
    node_type: str  # 'app', 'phone', 'account', 'area'
    attributes: Dict[str, Any]
    connections: Set[str] = None

    def __post_init__(self):
        if self.connections is None:
            self.connections = set()


@dataclass
class NetworkEdge:
    """网络边"""
    source: str
    target: str
    edge_type: str
    weight: float
    attributes: Dict[str, Any] = None

    def __post_init__(self):
        if self.attributes is None:
            self.attributes = {}


@dataclass
class SuspiciousNetwork:
    """可疑网络"""
    network_id: str
    network_type: str
    risk_level: str
    nodes: List[str]
    description: str
    evidence: Dict[str, Any]
    confidence_score: float


class NetworkAnalyzer(BaseAnalyzer):
    """网络关系分析器"""

    def __init__(self):
        super().__init__()
        self.graph = nx.Graph()
        self.suspicious_networks = []
        self.data_processor = DataProcessor()  # 初始化 data_processor

    def analyze(self, df: pd.DataFrame) -> AnalysisResult:
        """分析网络关系"""
        self.logger.info("开始网络关系分析")

        if df is None or df.empty:
            return AnalysisResult(
                analysis_type="network",
                timestamp=datetime.now(),
                data_summary={},
                results={"error": "数据为空"}
            )

        # 清理和准备数据
        network_df = self.data_processor.prepare_for_analysis(df, 'network')

        results = {}
        recommendations = []

        # 1. APP集群分析
        results['app_clusters'] = self._analyze_app_clusters(network_df)

        # 2. 可疑网络识别
        results['suspicious_networks'] = self._identify_suspicious_networks(network_df)

        # 3. 跨APP受害人分析
        results['cross_app_victims'] = self._analyze_cross_app_victims(network_df)

        # 4. 地理网络分析
        results['geographic_networks'] = self._analyze_geographic_networks(network_df)

        # 5. 时间网络分析
        results['temporal_networks'] = self._analyze_temporal_networks(network_df)

        # 6. 金额关联分析
        results['amount_correlation'] = self._analyze_amount_correlation(network_df)

        # 7. 手机号关联分析
        results['phone_relationships'] = self._analyze_phone_relationships(network_df)

        # 8. 账号集群分析
        results['account_clusters'] = self._analyze_account_clusters(network_df)

        # 生成建议
        recommendations = self._generate_network_recommendations(results)

        return AnalysisResult(
            analysis_type="network",
            timestamp=datetime.now(),
            data_summary={
                "total_records": len(network_df),
                "unique_apps": network_df['final_app_name'].nunique() if 'final_app_name' in network_df.columns else 0,
                "suspicious_networks_found": len(results.get('suspicious_networks', [])),
                "date_range": self._get_date_range(network_df)
            },
            results=results,
            recommendations=recommendations
        )

    def _analyze_app_clusters(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析APP集群"""
        self.logger.info("🔍 分析APP集群...")

        if 'final_app_name' not in df.columns:
            return {"error": "缺少APP名称列"}

        clusters = {}

        # 1. 基于共同受害人的APP关联
        clusters['victim_based'] = self._find_victim_based_clusters(df)

        # 2. 基于时间相关性的APP集群
        clusters['temporal_based'] = self._find_temporal_clusters(df)

        # 3. 基于金额相似性的APP集群
        if 'involved_amount' in df.columns:
            clusters['amount_based'] = self._find_amount_similarity_clusters(df)

        # 4. 基于地理位置的APP集群
        if 'occurrence_area' in df.columns:
            clusters['geographic_based'] = self._find_geographic_clusters(df)

        self.logger.info(f"   发现 {len([c for c in clusters.values() if c])} 类APP集群")
        return clusters

    def _find_victim_based_clusters(self, df: pd.DataFrame) -> Dict[str, Any]:
        """基于共同受害人发现APP集群"""
        clusters = []

        # 如果有受害人标识符（这里用时间和金额的相似性来推断）
        if 'involved_amount' in df.columns and 'insert_day' in df.columns:
            # 寻找相似案件（可能是同一受害人）
            similar_cases = self._find_similar_cases(df)

            # 构建APP共现矩阵
            app_cooccurrence = defaultdict(lambda: defaultdict(int))

            for case_group in similar_cases:
                apps_in_group = case_group['app_name'].unique()
                for app1, app2 in combinations(apps_in_group, 2):
                    app_cooccurrence[app1][app2] += 1
                    app_cooccurrence[app2][app1] += 1

            # 识别强关联的APP对
            for app1, related_apps in app_cooccurrence.items():
                for app2, count in related_apps.items():
                    if count >= 3:  # 至少3次共现
                        clusters.append({
                            'apps': [app1, app2],
                            'cooccurrence_count': count,
                            'cluster_type': '受害人关联'
                        })

        return {"clusters": clusters[:10]}  # 返回前10个集群

    def _find_temporal_clusters(self, df: pd.DataFrame) -> Dict[str, Any]:
        """基于时间相关性发现APP集群"""
        if 'insert_day' not in df.columns:
            return {}

        clusters = []

        # 按日期统计APP活跃度
        daily_app_counts = df.groupby([df['insert_day'].dt.date, 'app_name']).size().unstack(fill_value=0)

        if daily_app_counts.empty:
            return {"clusters": []}

        # 计算APP间的时间相关性
        app_correlation = daily_app_counts.corr()

        # 找出高相关性的APP对
        high_correlation_pairs = []
        for i in range(len(app_correlation.columns)):
            for j in range(i + 1, len(app_correlation.columns)):
                app1 = app_correlation.columns[i]
                app2 = app_correlation.columns[j]
                correlation = app_correlation.iloc[i, j]

                if correlation > 0.7 and not pd.isna(correlation):  # 高相关性阈值
                    high_correlation_pairs.append({
                        'apps': [app1, app2],
                        'correlation': round(correlation, 3),
                        'cluster_type': '时间同步'
                    })

        # 按相关性排序
        high_correlation_pairs.sort(key=lambda x: x['correlation'], reverse=True)

        return {"clusters": high_correlation_pairs[:10]}

    def _find_amount_similarity_clusters(self, df: pd.DataFrame) -> Dict[str, Any]:
        """基于金额相似性发现APP集群"""
        clusters = []

        # 计算每个APP的金额统计
        app_amount_stats = df.groupby('app_name')['involved_amount'].agg(['mean', 'std', 'count'])
        app_amount_stats = app_amount_stats[app_amount_stats['count'] >= 5]  # 至少5个案例

        if len(app_amount_stats) < 2:
            return {"clusters": []}

        # 找出金额分布相似的APP对
        for i, (app1, stats1) in enumerate(app_amount_stats.iterrows()):
            for j, (app2, stats2) in enumerate(app_amount_stats.iterrows()):
                if i < j:
                    # 计算金额分布相似度
                    mean_similarity = 1 - abs(stats1['mean'] - stats2['mean']) / max(stats1['mean'], stats2['mean'])

                    if mean_similarity > 0.8:  # 相似度阈值
                        clusters.append({
                            'apps': [app1, app2],
                            'similarity_score': round(mean_similarity, 3),
                            'avg_amounts': {
                                app1: round(stats1['mean'], 2),
                                app2: round(stats2['mean'], 2)
                            },
                            'cluster_type': '金额相似'
                        })

        # 按相似度排序
        clusters.sort(key=lambda x: x['similarity_score'], reverse=True)

        return {"clusters": clusters[:10]}

    def _find_geographic_clusters(self, df: pd.DataFrame) -> Dict[str, Any]:
        """基于地理位置发现APP集群"""
        clusters = []

        # 分析地区-APP关联
        area_app_matrix = df.groupby(['occurrence_area', 'app_name']).size().unstack(fill_value=0)

        if area_app_matrix.empty:
            return {"clusters": []}

        # 找出在相同地区高度活跃的APP组合
        for area in area_app_matrix.index:
            area_data = area_app_matrix.loc[area]
            active_apps = area_data[area_data > 0].sort_values(ascending=False)

            if len(active_apps) >= 3:  # 至少3个APP在同一地区活跃
                clusters.append({
                    'area': area,
                    'apps': active_apps.head(5).index.tolist(),
                    'case_counts': active_apps.head(5).to_dict(),
                    'cluster_type': '地理集中'
                })

        # 按案件总数排序
        clusters.sort(key=lambda x: sum(x['case_counts'].values()), reverse=True)

        return {"clusters": clusters[:10]}

    def _identify_suspicious_networks(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """识别可疑网络"""
        self.logger.info("🚨 识别可疑网络...")

        suspicious_networks = []

        # 1. 时间窗口内的APP爆发网络
        time_burst_networks = self._detect_time_burst_networks(df)
        suspicious_networks.extend(time_burst_networks)

        # 2. 金额异常关联网络
        if 'involved_amount' in df.columns:
            amount_anomaly_networks = self._detect_amount_anomaly_networks(df)
            suspicious_networks.extend(amount_anomaly_networks)

        # 3. 地理集中网络
        if 'occurrence_area' in df.columns:
            geo_concentration_networks = self._detect_geographic_concentration_networks(df)
            suspicious_networks.extend(geo_concentration_networks)

        # 4. 新APP快速传播网络
        if 'insert_day' in df.columns:
            rapid_spread_networks = self._detect_rapid_spread_networks(df)
            suspicious_networks.extend(rapid_spread_networks)

        # 5. 异常高额网络
        if 'involved_amount' in df.columns:
            high_amount_networks = self._detect_high_amount_networks(df)
            suspicious_networks.extend(high_amount_networks)

        self.logger.info(f"   发现 {len(suspicious_networks)} 个可疑网络")
        return suspicious_networks

    def _detect_time_burst_networks(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """检测时间爆发网络"""
        networks = []

        if 'insert_day' not in df.columns or 'app_name' not in df.columns:
            return networks

        # 按日期统计APP案件数
        daily_counts = df.groupby([df['insert_day'].dt.date, 'app_name']).size().unstack(fill_value=0)

        if daily_counts.empty:
            return networks

        # 检测同时爆发的APP组合
        for date in daily_counts.index:
            day_data = daily_counts.loc[date]
            if day_data.sum() > 0:
                threshold = day_data.quantile(0.9)
                high_activity_apps = day_data[day_data > threshold].index.tolist()

                if len(high_activity_apps) >= 2:
                    networks.append({
                        'type': '时间爆发网络',
                        'date': date.strftime('%Y-%m-%d'),
                        'apps': high_activity_apps,
                        'case_counts': {app: int(day_data[app]) for app in high_activity_apps},
                        'risk_level': '高' if len(high_activity_apps) >= 3 else '中',
                        'description': f'{date.strftime("%Y-%m-%d")}发现{len(high_activity_apps)}个APP同时高活跃',
                        'confidence_score': min(0.9, len(high_activity_apps) * 0.2)
                    })

        return networks

    def _detect_amount_anomaly_networks(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """检测金额异常关联网络"""
        networks = []

        # 按APP统计金额分布
        app_amounts = df.dropna(subset=['involved_amount', 'app_name']).groupby('app_name')[
            'involved_amount'].agg(['mean', 'std', 'count'])
        app_amounts = app_amounts[app_amounts['count'] >= 5]

        if len(app_amounts) < 2:
            return networks

        # 检测金额分布异常相似的APP
        similar_pairs = []
        for i, (app1, stats1) in enumerate(app_amounts.iterrows()):
            for j, (app2, stats2) in enumerate(app_amounts.iterrows()):
                if i < j:
                    mean_diff = abs(stats1['mean'] - stats2['mean']) / max(stats1['mean'], stats2['mean'])
                    if mean_diff < 0.15:  # 平均金额相似度高
                        similar_pairs.append({
                            'apps': [app1, app2],
                            'similarity': 1 - mean_diff,
                            'amounts': {
                                app1: round(stats1['mean'], 2),
                                app2: round(stats2['mean'], 2)
                            }
                        })

        # 构建相似APP网络
        if similar_pairs:
            # 按相似度分组
            similar_pairs.sort(key=lambda x: x['similarity'], reverse=True)

            for pair in similar_pairs[:5]:  # 取前5个最相似的对
                networks.append({
                    'type': '金额异常关联',
                    'apps': pair['apps'],
                    'similarity_score': round(pair['similarity'], 3),
                    'average_amounts': pair['amounts'],
                    'risk_level': '中',
                    'description': f'{pair["apps"][0]}和{pair["apps"][1]}的平均涉案金额高度相似',
                    'confidence_score': pair['similarity']
                })

        return networks

    def _detect_geographic_concentration_networks(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """检测地理集中网络"""
        networks = []

        # 分析最近一周的地区集中度
        recent_week = datetime.now() - timedelta(days=7)
        recent_data = df[df['insert_day'] >= recent_week] if 'insert_day' in df.columns else df

        if recent_data.empty:
            return networks

        area_app_counts = recent_data.groupby(['occurrence_area', 'app_name']).size().unstack(fill_value=0)

        # 检测特定地区的APP集中爆发
        for area in area_app_counts.index:
            area_data = area_app_counts.loc[area]
            active_apps = area_data[area_data > 0].index.tolist()
            total_cases = area_data.sum()

            if len(active_apps) >= 3 and total_cases >= 10:
                networks.append({
                    'type': '地理集中网络',
                    'area': area,
                    'apps': active_apps,
                    'case_counts': {app: int(area_data[app]) for app in active_apps if area_data[app] > 0},
                    'total_cases': int(total_cases),
                    'risk_level': '高' if total_cases >= 20 else '中',
                    'description': f'{area}最近7天发现{len(active_apps)}个APP集中活跃，共{total_cases}起案件',
                    'confidence_score': min(0.9, total_cases / 50)
                })

        return networks

    def _detect_rapid_spread_networks(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """检测快速传播网络"""
        networks = []

        # 检测新APP的快速传播
        df_time = df.copy()
        df_time['date'] = pd.to_datetime(df_time['insert_day']).dt.date

        # 获取每个APP首次出现的日期
        app_first_seen = df_time.groupby('app_name')['date'].min()

        # 检测最近7天首次出现但案件数快速增长的APP
        recent_date = datetime.now().date() - timedelta(days=7)
        new_apps = app_first_seen[app_first_seen >= recent_date]

        for app_name in new_apps.index:
            app_data = df_time[df_time['app_name'] == app_name]
            daily_counts = app_data.groupby('date').size()

            if len(daily_counts) >= 3 and daily_counts.max() >= 5:
                # 检查是否呈快速增长趋势
                growth_rate = (daily_counts.iloc[-1] - daily_counts.iloc[0]) / max(daily_counts.iloc[0], 1)

                if growth_rate > 2:  # 增长超过200%
                    networks.append({
                        'type': '快速传播网络',
                        'apps': [app_name],
                        'first_seen': new_apps[app_name].strftime('%Y-%m-%d'),
                        'growth_rate': round(growth_rate, 2),
                        'daily_progression': daily_counts.to_dict(),
                        'risk_level': '高',
                        'description': f'{app_name}在{len(daily_counts)}天内快速传播，增长率{growth_rate:.1f}倍',
                        'confidence_score': min(0.9, growth_rate / 5)
                    })

        return networks

    def _detect_high_amount_networks(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """检测异常高额网络"""
        networks = []

        # 检测涉案金额异常高的APP组合
        high_amount_threshold = df['involved_amount'].quantile(0.95)
        high_amount_cases = df[df['involved_amount'] >= high_amount_threshold]

        if high_amount_cases.empty:
            return networks

        # 按时间窗口分组检测
        high_amount_cases['date'] = pd.to_datetime(high_amount_cases['insert_day']).dt.date

        for date in high_amount_cases['date'].unique():
            day_cases = high_amount_cases[high_amount_cases['date'] == date]
            apps_involved = day_cases['final_app_name'].unique()

            if len(apps_involved) >= 2:
                total_amount = day_cases['involved_amount'].sum()
                networks.append({
                    'type': '异常高额网络',
                    'date': date.strftime('%Y-%m-%d'),
                    'apps': apps_involved.tolist(),
                    'total_amount': float(total_amount),
                    'case_count': len(day_cases),
                    'avg_amount': float(day_cases['involved_amount'].mean()),
                    'risk_level': '极高',
                    'description': f'{date.strftime("%Y-%m-%d")}发现{len(apps_involved)}个APP涉及异常高额案件，总金额{total_amount:,.0f}元',
                    'confidence_score': min(0.95, total_amount / 1000000)
                })

        return networks

    def _analyze_cross_app_victims(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析跨APP受害人模式"""
        self.logger.info("🔗 分析跨APP受害人模式...")

        cross_app_patterns = {}

        try:
            if 'involved_amount' in df.columns and 'insert_day' in df.columns:
                similar_cases = self._find_similar_cases(df)

                if similar_cases:
                    cross_app_patterns['potential_cases'] = len(similar_cases)
                    cross_app_patterns['patterns'] = []

                    for i, case_group in enumerate(similar_cases[:5]):  # 取前5个模式
                        apps_involved = case_group['final_app_name'].unique()
                        if len(apps_involved) > 1:
                            pattern = {
                                'pattern_id': f'pattern_{i + 1}',
                                'apps': apps_involved.tolist(),
                                'case_count': len(case_group),
                                'amount_range': {
                                    'min': float(case_group['involved_amount'].min()),
                                    'max': float(case_group['involved_amount'].max()),
                                    'avg': float(case_group['involved_amount'].mean())
                                },
                                'time_span': {
                                    'start': case_group['insert_day'].min().strftime('%Y-%m-%d'),
                                    'end': case_group['insert_day'].max().strftime('%Y-%m-%d')
                                }
                            }
                            cross_app_patterns['patterns'].append(pattern)

                self.logger.info(f"   发现 {len(cross_app_patterns.get('patterns', []))} 个跨APP受害模式")

        except Exception as e:
            self.logger.error(f"跨APP受害人分析失败: {e}")
            cross_app_patterns['error'] = str(e)

        return cross_app_patterns

    def _find_similar_cases(self, df: pd.DataFrame) -> List[pd.DataFrame]:
        """寻找相似案件（可能是同一受害人）"""
        similar_case_groups = []
        amount_tolerance = 0.05  # 5%的金额容差
        time_tolerance = timedelta(hours=24)  # 24小时时间容差

        df_clean = df.dropna(subset=['involved_amount', 'insert_day', 'final_app_name'])
        processed_indices = set()

        for i, row1 in df_clean.iterrows():
            if i in processed_indices:
                continue

            similar_indices = [i]

            for j, row2 in df_clean.iterrows():
                if i != j and j not in processed_indices:
                    # 检查金额相似性
                    amount_diff = abs(row1['involved_amount'] - row2['involved_amount']) / max(row1['involved_amount'],
                                                                                               1)
                    # 检查时间相似性
                    time_diff = abs(row1['insert_day'] - row2['insert_day'])

                    if amount_diff <= amount_tolerance and time_diff <= time_tolerance:
                        similar_indices.append(j)

            if len(similar_indices) > 1:
                similar_group = df_clean.loc[similar_indices]
                similar_case_groups.append(similar_group)
                processed_indices.update(similar_indices)

        return similar_case_groups

    def _analyze_geographic_networks(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析地理网络"""
        self.logger.info("🗺️ 分析地理网络...")

        if 'occurrence_area' not in df.columns:
            return {"error": "缺少地区信息"}

        geo_analysis = {}

        # 地区APP分布
        area_app_dist = df.groupby(['occurrence_area', 'final_app_name']).size().unstack(fill_value=0)

        # 找出多地区活跃的APP
        app_area_counts = (area_app_dist > 0).sum(axis=0)
        multi_area_apps = app_area_counts[app_area_counts > 1].sort_values(ascending=False)

        if not multi_area_apps.empty:
            geo_analysis['multi_area_apps'] = multi_area_apps.head(10).to_dict()

            # 详细分析跨地区APP
            cross_region_details = {}
            for app in multi_area_apps.head(5).index:
                app_areas = area_app_dist[app][area_app_dist[app] > 0]
                cross_region_details[app] = {
                    'active_areas': app_areas.index.tolist(),
                    'area_case_counts': app_areas.to_dict(),
                    'total_areas': len(app_areas)
                }

            geo_analysis['cross_region_details'] = cross_region_details
            self.logger.info(f"   发现 {len(multi_area_apps)} 个跨地区活跃APP")

        # 地区集中度分析
        area_totals = area_app_dist.sum(axis=1).sort_values(ascending=False)
        geo_analysis['area_concentration'] = {
            'top_areas': area_totals.head(10).to_dict(),
            'concentration_ratio': float(area_totals.head(5).sum() / area_totals.sum() * 100)
        }

        return geo_analysis

    def _analyze_temporal_networks(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析时间网络"""
        self.logger.info("⏰ 分析时间网络...")

        if 'insert_day' not in df.columns:
            return {"error": "缺少时间信息"}

        temporal_analysis = {}

        # 时间模式分析
        df_time = df.copy()
        df_time['hour'] = pd.to_datetime(df_time['insert_day']).dt.hour
        df_time['weekday'] = pd.to_datetime(df_time['insert_day']).dt.dayofweek
        df_time['date'] = pd.to_datetime(df_time['insert_day']).dt.date

        # APP活跃时间模式
        app_time_patterns = {}
        for app in df_time['final_app_name'].unique():
            app_data = df_time[df_time['final_app_name'] == app]

            app_time_patterns[app] = {
                'peak_hours': app_data['hour'].value_counts().head(3).index.tolist(),
                'peak_weekdays': app_data['weekday'].value_counts().head(3).index.tolist(),
                'activity_distribution': {
                    'hourly': app_data['hour'].value_counts().to_dict(),
                    'weekly': app_data['weekday'].value_counts().to_dict()
                }
            }

        temporal_analysis['app_time_patterns'] = app_time_patterns

        # 时间同步性分析
        daily_app_activity = df_time.groupby(['date', 'final_app_name']).size().unstack(fill_value=0)
        if not daily_app_activity.empty:
            app_correlation = daily_app_activity.corr()

            # 找出时间高度同步的APP对
            synchronized_pairs = []
            for i in range(len(app_correlation.columns)):
                for j in range(i + 1, len(app_correlation.columns)):
                    app1 = app_correlation.columns[i]
                    app2 = app_correlation.columns[j]
                    correlation = app_correlation.iloc[i, j]

                    if correlation > 0.7 and not pd.isna(correlation):
                        synchronized_pairs.append({
                            'apps': [app1, app2],
                            'correlation': round(correlation, 3)
                        })

            synchronized_pairs.sort(key=lambda x: x['correlation'], reverse=True)
            temporal_analysis['synchronized_apps'] = synchronized_pairs[:10]

        return temporal_analysis

    def _analyze_amount_correlation(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析金额关联"""
        self.logger.info("💰 分析金额关联...")

        if 'involved_amount' not in df.columns:
            return {"error": "缺少金额信息"}

        amount_analysis = {}

        # APP间金额相关性
        app_amounts = df.groupby('final_app_name')['involved_amount'].agg(['mean', 'std', 'count'])
        app_amounts = app_amounts[app_amounts['count'] >= 3]  # 至少3个案例

        if len(app_amounts) >= 2:
            # 计算金额相似度矩阵
            similarity_matrix = {}
            for app1 in app_amounts.index:
                similarity_matrix[app1] = {}
                for app2 in app_amounts.index:
                    if app1 != app2:
                        mean1, mean2 = app_amounts.loc[app1, 'mean'], app_amounts.loc[app2, 'mean']
                        similarity = 1 - abs(mean1 - mean2) / max(mean1, mean2)
                        similarity_matrix[app1][app2] = round(similarity, 3)

            amount_analysis['similarity_matrix'] = similarity_matrix

            # 找出金额高度相似的APP组
            similar_amount_groups = []
            processed_apps = set()

            for app1 in app_amounts.index:
                if app1 in processed_apps:
                    continue

                similar_apps = [app1]
                for app2 in app_amounts.index:
                    if app1 != app2 and app2 not in processed_apps:
                        if similarity_matrix[app1][app2] > 0.8:
                            similar_apps.append(app2)

                if len(similar_apps) > 1:
                    group_amounts = {app: round(app_amounts.loc[app, 'mean'], 2) for app in similar_apps}
                    similar_amount_groups.append({
                        'apps': similar_apps,
                        'average_amounts': group_amounts,
                        'similarity_score': min([similarity_matrix[similar_apps[0]][app] for app in similar_apps[1:]])
                    })
                    processed_apps.update(similar_apps)

            amount_analysis['similar_amount_groups'] = similar_amount_groups

        return amount_analysis

    def _analyze_phone_relationships(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析手机号关联关系"""
        self.logger.info("📱 分析手机号关联...")

        phone_analysis = {}
        phone_columns = ['suspect_phone_number', 'victim_phone', 'phone_number']

        # 找到存在的手机号列
        available_phone_col = None
        for col in phone_columns:
            if col in df.columns:
                available_phone_col = col
                break

        if not available_phone_col:
            return {"error": "未找到手机号信息"}

        phone_app_data = df.dropna(subset=[available_phone_col, 'final_app_name'])

        if phone_app_data.empty:
            return {"error": "无有效手机号-APP关联数据"}

        # 统计每个手机号涉及的APP数量
        phone_app_counts = phone_app_data.groupby(available_phone_col)['final_app_name'].nunique()
        multi_app_phones = phone_app_counts[phone_app_counts > 1].sort_values(ascending=False)

        if not multi_app_phones.empty:
            phone_analysis['multi_app_phones'] = {
                'count': len(multi_app_phones),
                'max_apps_per_phone': int(multi_app_phones.max()),
                'avg_apps_per_phone': round(multi_app_phones.mean(), 2)
            }

            # 详细分析涉及多APP的手机号
            multi_app_details = {}
            for phone in multi_app_phones.head(10).index:
                phone_data = phone_app_data[phone_app_data[available_phone_col] == phone]
                apps = phone_data['final_app_name'].unique()

                multi_app_details[phone] = {
                    'apps': apps.tolist(),
                    'app_count': len(apps),
                    'total_cases': len(phone_data),
                    'total_amount': float(
                        phone_data['involved_amount'].sum()) if 'involved_amount' in phone_data.columns else 0
                }

            phone_analysis['multi_app_details'] = multi_app_details
            self.logger.info(f"   发现 {len(multi_app_phones)} 个手机号涉及多个APP")

        return phone_analysis

    def _analyze_account_clusters(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析账号集群"""
        self.logger.info("💳 分析账号集群...")

        account_columns = ['suspect_account_number', 'account_number', 'bank_account']

        # 找到存在的账号列
        available_account_col = None
        for col in account_columns:
            if col in df.columns:
                available_account_col = col
                break

        if not available_account_col:
            return {"error": "未找到账号信息"}

        account_data = df.dropna(subset=[available_account_col])

        if account_data.empty:
            return {"error": "无有效账号数据"}

        # 统计账号出现频次
        account_counts = account_data[available_account_col].value_counts()
        frequent_accounts = account_counts[account_counts > 1]

        account_analysis = {}

        if not frequent_accounts.empty:
            account_analysis['frequent_accounts'] = {
                'count': len(frequent_accounts),
                'max_cases_per_account': int(frequent_accounts.max()),
                'top_accounts': frequent_accounts.head(10).to_dict()
            }

            # 分析高频账号的APP分布
            high_freq_account_apps = {}
            for account in frequent_accounts.head(5).index:
                account_cases = account_data[account_data[available_account_col] == account]
                apps = account_cases['final_app_name'].value_counts()

                high_freq_account_apps[account] = {
                    'apps': apps.to_dict(),
                    'unique_apps': len(apps),
                    'total_cases': len(account_cases),
                    'total_amount': float(
                        account_cases['involved_amount'].sum()) if 'involved_amount' in account_cases.columns else 0
                }

            account_analysis['high_frequency_details'] = high_freq_account_apps
            self.logger.info(f"   发现 {len(frequent_accounts)} 个重复出现的可疑账号")

        return account_analysis

    def _get_date_range(self, df: pd.DataFrame) -> str:
        """获取数据的时间范围"""
        if 'insert_day' in df.columns:
            min_date = df['insert_day'].min().strftime('%Y-%m-%d')
            max_date = df['insert_day'].max().strftime('%Y-%m-%d')
            return f"{min_date} 至 {max_date}"
        return "未知"

    def _generate_network_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """生成网络分析建议"""
        recommendations = []

        # 基于可疑网络的建议
        suspicious_networks = results.get('suspicious_networks', [])
        if suspicious_networks:
            high_risk_networks = [n for n in suspicious_networks if
                                  n.get('risk_level') == '高' or n.get('risk_level') == '极高']
            if high_risk_networks:
                recommendations.append(f"发现{len(high_risk_networks)}个高风险网络，建议立即加强监控和调查")

            time_burst_count = len([n for n in suspicious_networks if n.get('type') == '时间爆发网络'])
            if time_burst_count > 0:
                recommendations.append(f"检测到{time_burst_count}个时间爆发网络，建议关注APP间的协同作案模式")

        # 基于地理网络的建议
        geo_networks = results.get('geographic_networks', {})
        multi_area_apps = geo_networks.get('multi_area_apps', {})
        if len(multi_area_apps) > 5:
            recommendations.append("发现多个跨地区活跃APP，建议建立跨区域协作机制")

        # 基于手机号关联的建议
        phone_relationships = results.get('phone_relationships', {})
        multi_app_phones = phone_relationships.get('multi_app_phones', {})
        if multi_app_phones.get('count', 0) > 10:
            recommendations.append("发现多个手机号涉及多个APP，建议建立手机号黑名单机制")

        # 基于账号集群的建议
        account_clusters = results.get('account_clusters', {})
        frequent_accounts = account_clusters.get('frequent_accounts', {})
        if frequent_accounts.get('count', 0) > 5:
            recommendations.append("发现多个高频出现的可疑账号，建议加强账号监控")

        return recommendations