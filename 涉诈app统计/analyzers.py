"""
涉案APP监控系统 - 分析器模块
包含各种专业分析器：资金分析、APP分析、受害人分析、诈骗手段分析
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging

from config import config
from data_processor import DataProcessor

logger = logging.getLogger(__name__)


@dataclass
class AnalysisResult:
    """分析结果基类"""
    analysis_type: str
    timestamp: datetime
    data_summary: Dict[str, Any]
    results: Dict[str, Any]
    recommendations: List[str] = None

    def __post_init__(self):
        if self.recommendations is None:
            self.recommendations = []


class BaseAnalyzer:
    """分析器基类"""

    def __init__(self):
        self.config = config
        self.data_processor = DataProcessor()
        self.logger = logging.getLogger(self.__class__.__name__)

    def analyze(self, df: pd.DataFrame) -> AnalysisResult:
        """分析接口，子类需要实现"""
        raise NotImplementedError("子类必须实现analyze方法")

    def _validate_data(self, df: pd.DataFrame, required_columns: List[str]) -> bool:
        """验证数据是否包含必需列"""
        if df is None or df.empty:
            self.logger.warning("数据为空")
            return False

        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            self.logger.warning(f"缺少必需列: {missing_columns}")
            return False

        return True

    def _get_date_range(self, df: pd.DataFrame) -> Dict[str, str]:
        """获取数据日期范围"""
        if 'insert_day' in df.columns:
            dates = pd.to_datetime(df['insert_day'])
            return {
                "start_date": dates.min().strftime('%Y-%m-%d'),
                "end_date": dates.max().strftime('%Y-%m-%d')
            }
        return {}


class FinancialAnalyzer(BaseAnalyzer):
    """资金影响分析器"""

    def analyze(self, df: pd.DataFrame) -> AnalysisResult:
        """分析资金影响"""
        self.logger.info("开始资金影响分析")

        if not self._validate_data(df, ['involved_amount']):
            return AnalysisResult(
                analysis_type="financial",
                timestamp=datetime.now(),
                data_summary={},
                results={"error": "数据验证失败"}
            )

        # 准备数据
        financial_df = self.data_processor.prepare_for_analysis(df, 'financial')

        results = {}
        recommendations = []

        # 1. 基础统计
        results['basic_stats'] = self._calculate_basic_stats(financial_df)

        # 2. 金额分布分析
        results['amount_distribution'] = self._analyze_amount_distribution(financial_df)

        # 3. 时间趋势分析
        if 'insert_day' in financial_df.columns:
            results['temporal_trends'] = self._analyze_temporal_trends(financial_df)

        # 4. APP金额排名
        if 'final_app_name' in financial_df.columns:
            results['app_rankings'] = self._analyze_app_financial_impact(financial_df)

        # 5. 异常金额检测
        results['anomalies'] = self._detect_amount_anomalies(financial_df)

        # 生成建议
        recommendations = self._generate_financial_recommendations(results)

        return AnalysisResult(
            analysis_type="financial",
            timestamp=datetime.now(),
            data_summary={
                "total_records": len(financial_df),
                "total_amount": financial_df['involved_amount'].sum(),
                "date_range": self._get_date_range(financial_df)
            },
            results=results,
            recommendations=recommendations
        )

    def _calculate_basic_stats(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算基础统计信息"""
        amounts = df['involved_amount']

        return {
            "total_amount": float(amounts.sum()),
            "average_amount": float(amounts.mean()),
            "median_amount": float(amounts.median()),
            "max_amount": float(amounts.max()),
            "min_amount": float(amounts.min()),
            "std_amount": float(amounts.std()),
            "case_count": len(df)
        }

    def _analyze_amount_distribution(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析金额分布"""
        amounts = df['involved_amount']
        thresholds = self.config.analysis.amount_thresholds

        distribution = {}
        for level, threshold in thresholds.items():
            if level == 'low':
                count = (amounts < threshold).sum()
            else:
                prev_threshold = list(thresholds.values())[list(thresholds.keys()).index(level) - 1]
                count = ((amounts >= prev_threshold) & (amounts < threshold)).sum()

            distribution[level] = {
                "count": int(count),
                "percentage": float(count / len(df) * 100),
                "total_amount": float(amounts[(amounts >= (prev_threshold if level != 'low' else 0)) &
                                              (amounts < threshold)].sum())
            }

        # 极高金额（超过最高阈值）
        extreme_count = (amounts >= thresholds['extreme']).sum()
        distribution['extreme'] = {
            "count": int(extreme_count),
            "percentage": float(extreme_count / len(df) * 100),
            "total_amount": float(amounts[amounts >= thresholds['extreme']].sum())
        }

        return distribution

    def _analyze_temporal_trends(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析时间趋势"""
        df_time = df.copy()
        df_time['date'] = pd.to_datetime(df_time['insert_day']).dt.date

        daily_stats = df_time.groupby('date').agg({
            'involved_amount': ['sum', 'mean', 'count']
        }).round(2)

        daily_stats.columns = ['daily_total', 'daily_average', 'daily_count']

        # 计算趋势
        recent_7_days = daily_stats.tail(7)
        previous_7_days = daily_stats.tail(14).head(7)

        trends = {}
        if len(previous_7_days) > 0:
            trends = {
                "amount_trend": float((recent_7_days['daily_total'].mean() -
                                       previous_7_days['daily_total'].mean()) /
                                      previous_7_days['daily_total'].mean() * 100),
                "case_trend": float((recent_7_days['daily_count'].mean() -
                                     previous_7_days['daily_count'].mean()) /
                                    previous_7_days['daily_count'].mean() * 100)
            }

        return {
            "daily_stats": daily_stats.to_dict('index'),
            "trends": trends,
            "peak_day": daily_stats['daily_total'].idxmax().strftime('%Y-%m-%d'),
            "peak_amount": float(daily_stats['daily_total'].max())
        }

    def _analyze_app_financial_impact(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析APP财务影响"""
        app_stats = df.groupby('final_app_name').agg({
            'involved_amount': ['sum', 'mean', 'count', 'max']
        }).round(2)

        app_stats.columns = ['total_amount', 'avg_amount', 'case_count', 'max_amount']
        app_stats = app_stats.sort_values('total_amount', ascending=False)

        # 计算占比
        total_amount = df['involved_amount'].sum()
        app_stats['percentage'] = (app_stats['total_amount'] / total_amount * 100).round(2)

        # 风险评分
        app_stats['risk_score'] = (
                app_stats['total_amount'] / 10000 * 0.4 +
                app_stats['case_count'] * 0.3 +
                app_stats['avg_amount'] / 1000 * 0.3
        ).round(2)

        return {
            "top_apps": app_stats.head(20).to_dict('index'),
            "high_risk_apps": app_stats[app_stats['risk_score'] > 50].to_dict('index'),
            "concentration": {
                "top_5_percentage": float(app_stats.head(5)['percentage'].sum()),
                "top_10_percentage": float(app_stats.head(10)['percentage'].sum())
            }
        }

    def _detect_amount_anomalies(self, df: pd.DataFrame) -> Dict[str, Any]:
        """检测金额异常"""
        amounts = df['involved_amount']

        # 使用IQR方法检测异常值
        Q1 = amounts.quantile(0.25)
        Q3 = amounts.quantile(0.75)
        IQR = Q3 - Q1

        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR

        anomalies = df[(amounts < lower_bound) | (amounts > upper_bound)]

        return {
            "anomaly_count": len(anomalies),
            "anomaly_percentage": float(len(anomalies) / len(df) * 100),
            "high_anomalies": anomalies[anomalies['involved_amount'] > upper_bound].nlargest(10, 'involved_amount')[
                ['final_app_name', 'involved_amount', 'insert_day']].to_dict('records'),
            "thresholds": {
                "lower_bound": float(lower_bound),
                "upper_bound": float(upper_bound)
            }
        }

    def _generate_financial_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """生成财务分析建议"""
        recommendations = []

        # 基于基础统计的建议
        basic_stats = results.get('basic_stats', {})
        avg_amount = basic_stats.get('average_amount', 0)

        if avg_amount > 50000:
            recommendations.append("平均涉案金额较高，建议加强高额案件预警机制")

        # 基于分布的建议
        distribution = results.get('amount_distribution', {})
        extreme_percentage = distribution.get('extreme', {}).get('percentage', 0)

        if extreme_percentage > 5:
            recommendations.append("极高金额案件占比较高，需要重点关注大额诈骗案件")

        # 基于趋势的建议
        trends = results.get('temporal_trends', {}).get('trends', {})
        amount_trend = trends.get('amount_trend', 0)

        if amount_trend > 20:
            recommendations.append("涉案金额呈上升趋势，建议加强监控和预防措施")
        elif amount_trend < -20:
            recommendations.append("涉案金额呈下降趋势，当前防控措施效果良好")

        # 基于异常检测的建议
        anomalies = results.get('anomalies', {})
        anomaly_percentage = anomalies.get('anomaly_percentage', 0)

        if anomaly_percentage > 10:
            recommendations.append("存在较多异常金额案件，建议深入调查异常模式")

        return recommendations


class AppAnalyzer(BaseAnalyzer):
    """APP风险分析器"""

    def analyze(self, df: pd.DataFrame) -> AnalysisResult:
        """分析APP风险"""
        self.logger.info("开始APP风险分析")

        if not self._validate_data(df, ['final_app_name']):
            return AnalysisResult(
                analysis_type="app",
                timestamp=datetime.now(),
                data_summary={},
                results={"error": "数据验证失败"}
            )

        # 准备数据
        app_df = self.data_processor.prepare_for_analysis(df, 'app')

        results = {}
        recommendations = []

        # 1. APP基础统计
        results['basic_stats'] = self._calculate_app_basic_stats(app_df)

        # 2. APP排名分析
        results['rankings'] = self._analyze_app_rankings(app_df)

        # 3. 新APP检测
        if 'insert_day' in app_df.columns:
            results['new_apps'] = self._detect_new_apps(app_df)

        # 4. APP风险评估
        results['risk_assessment'] = self._assess_app_risks(app_df)

        # 5. APP集群分析
        results['clusters'] = self._analyze_app_clusters(app_df)

        # 生成建议
        recommendations = self._generate_app_recommendations(results)

        return AnalysisResult(
            analysis_type="app",
            timestamp=datetime.now(),
            data_summary={
                "total_apps": app_df['final_app_name'].nunique(),
                "total_cases": len(app_df),
                "date_range": self._get_date_range(app_df)
            },
            results=results,
            recommendations=recommendations
        )

    def _calculate_app_basic_stats(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算APP基础统计"""
        app_stats = df.groupby('final_app_name').agg({
            'involved_amount': ['count', 'sum', 'mean'] if 'involved_amount' in df.columns else ['count']
        })

        if 'involved_amount' in df.columns:
            app_stats.columns = ['case_count', 'total_amount', 'avg_amount']
        else:
            app_stats.columns = ['case_count']

        return {
            "unique_apps": len(app_stats),
            "total_cases": int(app_stats['case_count'].sum()),
            "avg_cases_per_app": float(app_stats['case_count'].mean()),
            "max_cases_per_app": int(app_stats['case_count'].max()),
            "apps_with_single_case": int((app_stats['case_count'] == 1).sum())
        }

    def _analyze_app_rankings(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析APP排名"""
        # 按案件数排名
        case_ranking = df['final_app_name'].value_counts().head(20)

        rankings = {
            "by_cases": case_ranking.to_dict()
        }

        # 按金额排名（如果有金额数据）
        if 'involved_amount' in df.columns:
            amount_ranking = df.groupby('final_app_name')['involved_amount'].sum().sort_values(ascending=False).head(20)
            rankings["by_amount"] = amount_ranking.to_dict()

            # 按平均金额排名
            avg_amount_ranking = df.groupby('final_app_name')['involved_amount'].mean().sort_values(
                ascending=False).head(20)
            rankings["by_avg_amount"] = avg_amount_ranking.to_dict()

        return rankings

    def _detect_new_apps(self, df: pd.DataFrame) -> Dict[str, Any]:
        """检测新出现的APP"""
        df_time = df.copy()
        df_time['date'] = pd.to_datetime(df_time['insert_day']).dt.date

        # 获取每个APP首次出现的日期
        app_first_seen = df_time.groupby('final_app_name')['date'].min()

        # 定义"新APP"：最近7天首次出现
        recent_date = datetime.now().date() - timedelta(days=7)
        new_apps = app_first_seen[app_first_seen >= recent_date]

        new_app_details = []
        for app_name in new_apps.index:
            app_data = df_time[df_time['final_app_name'] == app_name]

            detail = {
                "app_name": app_name,
                "first_seen": new_apps[app_name].strftime('%Y-%m-%d'),
                "case_count": len(app_data),
                "total_amount": float(
                    app_data['involved_amount'].sum()) if 'involved_amount' in app_data.columns else 0,
                "avg_amount": float(app_data['involved_amount'].mean()) if 'involved_amount' in app_data.columns else 0,
                "risk_level": self._calculate_app_risk_level(app_data)
            }
            new_app_details.append(detail)

        # 按风险等级和案件数排序
        new_app_details.sort(key=lambda x: (x['risk_level'], x['case_count']), reverse=True)

        return {
            "count": len(new_apps),
            "apps": new_app_details[:10]  # 返回前10个新APP
        }

    def _assess_app_risks(self, df: pd.DataFrame) -> Dict[str, Any]:
        """评估APP风险"""
        app_stats = df.groupby('final_app_name').agg({
            'involved_amount': ['count', 'sum', 'mean', 'std'] if 'involved_amount' in df.columns else ['count']
        })

        if 'involved_amount' in df.columns:
            app_stats.columns = ['case_count', 'total_amount', 'avg_amount', 'amount_std']
        else:
            app_stats.columns = ['case_count']
            app_stats['total_amount'] = 0
            app_stats['avg_amount'] = 0
            app_stats['amount_std'] = 0

        # 计算风险评分
        app_stats['risk_score'] = (
                app_stats['case_count'] * 0.3 +
                (app_stats['total_amount'] / 10000) * 0.4 +
                (app_stats['avg_amount'] / 1000) * 0.2 +
                (app_stats['amount_std'] / 1000) * 0.1
        ).fillna(0)

        # 风险等级分类
        app_stats['risk_level'] = pd.cut(
            app_stats['risk_score'],
            bins=[0, 10, 25, 50, 100, float('inf')],
            labels=['低风险', '中低风险', '中风险', '高风险', '极高风险']
        )

        risk_distribution = app_stats['risk_level'].value_counts().to_dict()
        high_risk_apps = app_stats[app_stats['risk_level'].isin(['高风险', '极高风险'])].sort_values('risk_score',
                                                                                                     ascending=False)

        return {
            "risk_distribution": risk_distribution,
            "high_risk_apps": high_risk_apps.head(10).to_dict('index'),
            "avg_risk_score": float(app_stats['risk_score'].mean())
        }

    def _analyze_app_clusters(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析APP集群"""
        clusters = {}

        # 基于案件数聚类
        case_counts = df['final_app_name'].value_counts()

        clusters['by_case_volume'] = {
            "high_volume": case_counts[case_counts >= 20].to_dict(),
            "medium_volume": case_counts[(case_counts >= 5) & (case_counts < 20)].to_dict(),
            "low_volume": case_counts[case_counts < 5].to_dict()
        }

        # 基于金额聚类（如果有金额数据）
        if 'involved_amount' in df.columns:
            amount_stats = df.groupby('final_app_name')['involved_amount'].mean()

            clusters['by_amount_level'] = {
                "high_amount": amount_stats[amount_stats >= 50000].to_dict(),
                "medium_amount": amount_stats[(amount_stats >= 10000) & (amount_stats < 50000)].to_dict(),
                "low_amount": amount_stats[amount_stats < 10000].to_dict()
            }

        return clusters

    def _calculate_app_risk_level(self, app_data: pd.DataFrame) -> str:
        """计算单个APP的风险等级"""
        case_count = len(app_data)

        if 'involved_amount' in app_data.columns:
            avg_amount = app_data['involved_amount'].mean()
            total_amount = app_data['involved_amount'].sum()

            risk_score = case_count * 0.4 + (avg_amount / 10000) * 0.3 + (total_amount / 100000) * 0.3
        else:
            risk_score = case_count * 0.5

        if risk_score >= 50:
            return "极高风险"
        elif risk_score >= 25:
            return "高风险"
        elif risk_score >= 10:
            return "中风险"
        elif risk_score >= 5:
            return "中低风险"
        else:
            return "低风险"

    def _generate_app_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """生成APP分析建议"""
        recommendations = []

        # 基于新APP检测的建议
        new_apps = results.get('new_apps', {})
        if new_apps.get('count', 0) > 5:
            recommendations.append(f"发现{new_apps['count']}个新APP，建议加强对新出现APP的监控")

        # 基于风险评估的建议
        risk_assessment = results.get('risk_assessment', {})
        high_risk_count = len(risk_assessment.get('high_risk_apps', {}))
        if high_risk_count > 0:
            recommendations.append(f"发现{high_risk_count}个高风险APP，建议重点关注和防控")

        # 基于集群分析的建议
        clusters = results.get('clusters', {})
        high_volume_apps = clusters.get('by_case_volume', {}).get('high_volume', {})
        if len(high_volume_apps) > 10:
            recommendations.append("高案件量APP较多，建议建立APP黑名单机制")

        return recommendations


class VictimAnalyzer(BaseAnalyzer):
    """受害人画像分析器"""

    def analyze(self, df: pd.DataFrame) -> AnalysisResult:
        """分析受害人画像"""
        self.logger.info("开始受害人画像分析")

        results = {}
        recommendations = []

        # 1. 年龄分析
        if 'victim_age' in df.columns:
            results['age_analysis'] = self._analyze_victim_age(df)

        # 2. 性别分析
        if 'victim_gender' in df.columns:
            results['gender_analysis'] = self._analyze_victim_gender(df)

        # 3. 地域分析
        area_columns = ['occurrence_area', 'phone_attribution_province', 'phone_attribution_city']
        available_area_col = None
        for col in area_columns:
            if col in df.columns:
                available_area_col = col
                break
                
        if available_area_col:
            results['geographic_analysis'] = self._analyze_victim_geography(df, available_area_col)

        # 4. APP与受害人关联分析
        if 'final_app_name' in df.columns and 'victim_age' in df.columns:
            results['app_victim_correlation'] = self._analyze_app_victim_correlation(df)

        # 生成建议
        recommendations = self._generate_victim_recommendations(results)

        return AnalysisResult(
            analysis_type="victim",
            timestamp=datetime.now(),
            data_summary={
                "total_victims": len(df),
                "date_range": self._get_date_range(df)
            },
            results=results,
            recommendations=recommendations
        )

    def _analyze_victim_age(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析受害人年龄"""
        age_data = df['victim_age'].dropna()

        if age_data.empty:
            return {"error": "无有效年龄数据"}

        # 年龄段分布
        age_groups = pd.cut(age_data,
                            bins=[0, 18, 25, 35, 45, 55, 65, 100],
                            labels=['未成年', '18-25', '26-35', '36-45', '46-55', '56-65', '65+'])

        age_distribution = age_groups.value_counts().to_dict()

        return {
            "basic_stats": {
                "mean_age": float(age_data.mean()),
                "median_age": float(age_data.median()),
                "min_age": int(age_data.min()),
                "max_age": int(age_data.max()),
                "std_age": float(age_data.std())
            },
            "age_distribution": age_distribution,
            "high_risk_groups": self._identify_high_risk_age_groups(age_distribution)
        }

    def _analyze_victim_gender(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析受害人性别"""
        gender_data = df['victim_gender'].dropna()

        if gender_data.empty:
            return {"error": "无有效性别数据"}

        gender_distribution = gender_data.value_counts().to_dict()
        gender_percentage = (gender_data.value_counts(normalize=True) * 100).round(2).to_dict()

        return {
            "distribution": gender_distribution,
            "percentage": gender_percentage
        }

    def _analyze_victim_geography(self, df: pd.DataFrame, area_column: str) -> Dict[str, Any]:
        """分析受害人地域分布"""
        geo_data = df[area_column].dropna()

        if geo_data.empty:
            return {"error": "无有效地域数据"}

        # 标准化地区名称
        geo_data_clean = geo_data.apply(self._standardize_area_name)
        geo_distribution = geo_data_clean.value_counts().head(20).to_dict()

        return {
            "top_areas": geo_distribution,
            "total_areas": geo_data_clean.nunique(),
            "concentration": self._calculate_geographic_concentration(geo_data_clean)
        }

    def _analyze_app_victim_correlation(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析APP与受害人关联"""
        correlation_data = df.dropna(subset=['final_app_name', 'victim_age'])

        if correlation_data.empty:
            return {"error": "无有效关联数据"}

        # APP与年龄关联
        app_age_stats = correlation_data.groupby('final_app_name')['victim_age'].agg(['mean', 'count']).round(1)
        app_age_stats = app_age_stats[app_age_stats['count'] >= 5]  # 至少5个案例

        return {
            "app_age_correlation": app_age_stats.to_dict('index'),
            "young_victim_apps": app_age_stats[app_age_stats['mean'] < 30].sort_values('mean').to_dict('index'),
            "elderly_victim_apps": app_age_stats[app_age_stats['mean'] > 50].sort_values('mean',
                                                                                         ascending=False).to_dict(
                'index')
        }

    def _standardize_area_name(self, area: str) -> str:
        """标准化地区名称"""
        if pd.isna(area):
            return "未知"

        area = str(area).strip()

        # 移除常见后缀
        suffixes = ['市', '区', '县', '省', '自治区', '特别行政区']
        for suffix in suffixes:
            if area.endswith(suffix):
                area = area[:-len(suffix)]
                break

        return area if area else "未知"

    def _identify_high_risk_age_groups(self, age_distribution: Dict[str, int]) -> List[str]:
        """识别高风险年龄组"""
        total_cases = sum(age_distribution.values())
        high_risk_groups = []

        for age_group, count in age_distribution.items():
            percentage = count / total_cases * 100
            if percentage > 20:  # 超过20%的案件
                high_risk_groups.append(age_group)

        return high_risk_groups

    def _calculate_geographic_concentration(self, geo_data: pd.Series) -> Dict[str, Any]:
        """计算地理集中度"""
        total_cases = len(geo_data)
        top_5_count = geo_data.value_counts().head(5).sum()
        top_10_count = geo_data.value_counts().head(10).sum()

        return {
            "top_5_concentration": float(top_5_count / total_cases * 100),
            "top_10_concentration": float(top_10_count / total_cases * 100)
        }

    def _generate_victim_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """生成受害人分析建议"""
        recommendations = []

        # 基于年龄分析的建议
        age_analysis = results.get('age_analysis', {})
        high_risk_groups = age_analysis.get('high_risk_groups', [])
        if high_risk_groups:
            recommendations.append(f"重点关注{', '.join(high_risk_groups)}年龄段的防诈宣传")

        # 基于地域分析的建议
        geo_analysis = results.get('geographic_analysis', {})
        concentration = geo_analysis.get('concentration', {})
        if concentration.get('top_5_concentration', 0) > 50:
            recommendations.append("案件地域集中度较高，建议在重点地区加强防控")

        return recommendations


class FraudMethodAnalyzer(BaseAnalyzer):
    """诈骗手段分析器"""

    def analyze(self, df: pd.DataFrame) -> AnalysisResult:
        """分析诈骗手段"""
        self.logger.info("开始诈骗手段分析")

        results = {}
        recommendations = []

        # 1. 案件类型分析
        if 'case_main_type' in df.columns:
            results['case_type_analysis'] = self._analyze_case_types(df)

        # 2. 诈骗方式分析
        if 'fraud_method' in df.columns:
            results['method_analysis'] = self._analyze_fraud_methods(df)

        # 3. 转账方式分析
        if 'transfer_method' in df.columns:
            results['transfer_analysis'] = self._analyze_transfer_methods(df)

        # 4. 时间模式分析
        if 'insert_day' in df.columns:
            results['temporal_patterns'] = self._analyze_temporal_patterns(df)

        # 生成建议
        recommendations = self._generate_fraud_method_recommendations(results)

        return AnalysisResult(
            analysis_type="fraud_method",
            timestamp=datetime.now(),
            data_summary={
                "total_cases": len(df),
                "date_range": self._get_date_range(df)
            },
            results=results,
            recommendations=recommendations
        )

    def _analyze_case_types(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析案件类型"""
        case_types = df['case_main_type'].value_counts().head(10)

        return {
            "distribution": case_types.to_dict(),
            "percentage": (case_types / len(df) * 100).round(2).to_dict()
        }

    def _analyze_fraud_methods(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析诈骗方式"""
        methods = df['fraud_method'].value_counts().head(15)

        return {
            "distribution": methods.to_dict(),
            "percentage": (methods / len(df) * 100).round(2).to_dict()
        }

    def _analyze_transfer_methods(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析转账方式"""
        transfer_methods = df['transfer_method'].value_counts().head(10)

        return {
            "distribution": transfer_methods.to_dict(),
            "percentage": (transfer_methods / len(df) * 100).round(2).to_dict()
        }

    def _analyze_temporal_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析时间模式"""
        df_time = df.copy()
        df_time['hour'] = pd.to_datetime(df_time['insert_day']).dt.hour
        df_time['weekday'] = pd.to_datetime(df_time['insert_day']).dt.dayofweek

        return {
            "hourly_distribution": df_time['hour'].value_counts().sort_index().to_dict(),
            "weekday_distribution": df_time['weekday'].value_counts().sort_index().to_dict(),
            "peak_hours": df_time['hour'].value_counts().head(3).index.tolist(),
            "peak_weekdays": df_time['weekday'].value_counts().head(3).index.tolist()
        }

    def _generate_fraud_method_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """生成诈骗手段分析建议"""
        recommendations = []

        # 基于案件类型的建议
        case_analysis = results.get('case_type_analysis', {})
        if case_analysis:
            top_case_type = max(case_analysis.get('distribution', {}), key=case_analysis.get('distribution', {}).get,
                                default=None)
            if top_case_type:
                recommendations.append(f"'{top_case_type}'类案件最多，建议针对性加强防控")

        # 基于时间模式的建议
        temporal_patterns = results.get('temporal_patterns', {})
        peak_hours = temporal_patterns.get('peak_hours', [])
        if peak_hours:
            recommendations.append(f"案件高发时段为{peak_hours}点，建议在此时段加强监控")

        return recommendations