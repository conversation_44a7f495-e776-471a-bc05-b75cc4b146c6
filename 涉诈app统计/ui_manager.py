"""
用户界面管理模块
负责处理用户交互、菜单显示、进度条、状态管理等UI相关功能
"""

import os
import sys
import time
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable, Tuple
import logging
from dataclasses import dataclass
from enum import Enum
import pandas as pd

# 导入项目模块
from config import config
from database import get_db
from data_processor import DataProcessor
from analyzers import AppAnalyzer, FinancialAnalyzer, VictimAnalyzer, FraudMethodAnalyzer
from visualizer import ChartVisualizer
from report_generator import HTMLReportGenerator
from pattern_detector import PatternDetector
from network_analyzer import NetworkAnalyzer


class MenuType(Enum):
    """菜单类型枚举"""
    MAIN = "main"
    ANALYSIS = "analysis"
    VISUALIZATION = "visualization"
    REPORT = "report"
    DETECTION = "detection"
    NETWORK = "network"
    SETTINGS = "settings"


@dataclass
class MenuItem:
    """菜单项"""
    key: str
    title: str
    description: str
    action: Callable
    icon: str = ""
    enabled: bool = True
    submenu: Optional[List['MenuItem']] = None


@dataclass
class UIState:
    """UI状态"""
    current_menu: MenuType = MenuType.MAIN
    last_data: Optional[pd.DataFrame] = None
    last_analysis: Optional[Dict[str, Any]] = None
    is_processing: bool = False
    progress: float = 0.0
    status_message: str = ""


class ProgressBar:
    """进度条显示器"""

    def __init__(self, width: int = 50, show_percentage: bool = True):
        self.width = width
        self.show_percentage = show_percentage
        self.current = 0
        self.total = 100
        self.start_time = None
        self.lock = threading.Lock()

    def start(self, total: int = 100, message: str = "处理中"):
        """开始进度条"""
        with self.lock:
            self.total = total
            self.current = 0
            self.start_time = time.time()
            print(f"\n{message}...")
            self._display()

    def update(self, current: int, message: str = None):
        """更新进度"""
        with self.lock:
            self.current = min(current, self.total)
            if message:
                print(f"\r{' ' * (self.width + 20)}\r{message}...")
            self._display()

    def increment(self, step: int = 1, message: str = None):
        """增加进度"""
        self.update(self.current + step, message)

    def finish(self, message: str = "完成"):
        """完成进度条"""
        with self.lock:
            self.current = self.total
            self._display()
            elapsed = time.time() - self.start_time if self.start_time else 0
            print(f"\n✅ {message} (耗时: {elapsed:.2f}秒)")

    def _display(self):
        """显示进度条"""
        percentage = (self.current / self.total) * 100 if self.total > 0 else 0
        filled = int(self.width * self.current / self.total) if self.total > 0 else 0
        bar = '█' * filled + '░' * (self.width - filled)

        if self.show_percentage:
            print(f"\r[{bar}] {percentage:.1f}%", end='', flush=True)
        else:
            print(f"\r[{bar}]", end='', flush=True)


class UIManager:
    """用户界面管理器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.state = UIState()
        self.progress_bar = ProgressBar(width=config.ui.progress_bar_width)

        # 初始化组件
        self.db = get_db()
        self.data_processor = DataProcessor()
        self.visualizer = ChartVisualizer()
        self.report_generator = HTMLReportGenerator()
        self.pattern_detector = PatternDetector()
        self.network_analyzer = NetworkAnalyzer()

        # 分析器
        self.analyzers = {
            'app': AppAnalyzer(),
            'financial': FinancialAnalyzer(),
            'victim': VictimAnalyzer(),
            'fraud_method': FraudMethodAnalyzer()
        }

        # 菜单配置
        self.menus = self._initialize_menus()

        # 输出配置
        self.output_dir = Path(config.paths.export_dir)
        self.output_dir.mkdir(exist_ok=True)

    def _initialize_menus(self) -> Dict[MenuType, List[MenuItem]]:
        """初始化菜单"""
        symbols = config.ui.menu_symbols

        return {
            MenuType.MAIN: [
                MenuItem("1", "数据分析", "执行综合数据分析", self._menu_analysis, symbols['analysis']),
                MenuItem("2", "数据可视化", "生成图表和可视化", self._menu_visualization, symbols['visualization']),
                MenuItem("3", "报告生成", "生成分析报告", self._menu_report, symbols['report']),
                MenuItem("4", "模式检测", "检测可疑模式", self._menu_detection, symbols['detection']),
                MenuItem("5", "网络分析", "分析关联网络", self._menu_network, symbols['network']),
                MenuItem("6", "系统设置", "配置系统参数", self._menu_settings, symbols['settings']),
                MenuItem("7", "清除缓存", "清理系统缓存", self._clear_cache, symbols['cache']),
                MenuItem("0", "退出系统", "退出程序", self._exit_system, symbols['exit'])
            ],

            MenuType.ANALYSIS: [
                MenuItem("1", "APP风险分析", "分析APP相关风险", self._analyze_apps),
                MenuItem("2", "财务影响分析", "分析资金流向", self._analyze_financial),
                MenuItem("3", "受害人画像", "分析受害人特征", self._analyze_victims),
                MenuItem("4", "诈骗手段分析", "分析诈骗方式", self._analyze_fraud_methods),
                MenuItem("5", "综合分析", "执行全面分析", self._analyze_comprehensive),
                MenuItem("0", "返回主菜单", "返回上级菜单", self._back_to_main, symbols['back'])
            ],

            MenuType.VISUALIZATION: [
                MenuItem("1", "趋势图表", "生成趋势分析图", self._create_trend_charts),
                MenuItem("2", "排行图表", "生成排行榜图表", self._create_ranking_charts),
                MenuItem("3", "分布图表", "生成分布分析图", self._create_distribution_charts),
                MenuItem("4", "关联图表", "生成关联分析图", self._create_correlation_charts),
                MenuItem("5", "仪表板", "生成综合仪表板", self._create_dashboard),
                MenuItem("0", "返回主菜单", "返回上级菜单", self._back_to_main, symbols['back'])
            ],

            MenuType.REPORT: [
                MenuItem("1", "HTML报告", "生成HTML格式报告", self._generate_html_report),
                MenuItem("2", "风险评估报告", "生成风险评估报告", self._generate_risk_report),
                MenuItem("3", "统计摘要", "生成统计摘要", self._generate_summary_report),
                MenuItem("4", "自定义报告", "生成自定义报告", self._generate_custom_report),
                MenuItem("0", "返回主菜单", "返回上级菜单", self._back_to_main, symbols['back'])
            ],

            MenuType.DETECTION: [
                MenuItem("1", "异常检测", "检测异常模式", self._detect_anomalies),
                MenuItem("2", "新APP检测", "检测新出现的APP", self._detect_new_apps),
                MenuItem("3", "集群分析", "分析案件集群", self._detect_clusters),
                MenuItem("4", "时序异常", "检测时间序列异常", self._detect_temporal_anomalies),
                MenuItem("0", "返回主菜单", "返回上级菜单", self._back_to_main, symbols['back'])
            ],

            MenuType.NETWORK: [
                MenuItem("1", "关联网络", "分析实体关联关系", self._analyze_network_relationships),
                MenuItem("2", "社区发现", "发现犯罪团伙", self._detect_communities),
                MenuItem("3", "中心性分析", "分析关键节点", self._analyze_centrality),
                MenuItem("4", "路径分析", "分析传播路径", self._analyze_paths),
                MenuItem("0", "返回主菜单", "返回上级菜单", self._back_to_main, symbols['back'])
            ],

            MenuType.SETTINGS: [
                MenuItem("1", "数据库设置", "配置数据库连接", self._configure_database),
                MenuItem("2", "分析参数", "配置分析参数", self._configure_analysis),
                MenuItem("3", "可视化设置", "配置图表样式", self._configure_visualization),
                MenuItem("4", "导出设置", "配置导出选项", self._configure_export),
                MenuItem("5", "查看配置", "显示当前配置", self._show_config),
                MenuItem("0", "返回主菜单", "返回上级菜单", self._back_to_main, symbols['back'])
            ]
        }

    def run(self):
        """运行UI管理器主循环"""
        self._show_welcome()

        try:
            while True:
                self._display_menu()
                choice = self._get_user_input("请选择操作")

                if not choice:
                    continue

                self._handle_menu_choice(choice)

        except KeyboardInterrupt:
            self._show_message("用户中断操作", "warning")
        except Exception as e:
            self._show_message(f"系统错误: {e}", "error")
            self.logger.error(f"UI运行错误: {e}")
        finally:
            self._cleanup()

    def _show_welcome(self):
        """显示欢迎信息"""
        title = config.ui.menu_symbols['main_title']
        print(f"\n{title} 涉诈APP监控系统")
        print("=" * config.ui.menu_width)
        print(f"系统启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 检查数据库连接
        if self.db.is_connected:
            self._show_message("数据库连接正常", "success")
        else:
            self._show_message("数据库连接失败，系统将以离线模式运行", "warning")

        print("=" * config.ui.menu_width)

    def _display_menu(self):
        """显示当前菜单"""
        current_menu = self.menus[self.state.current_menu]

        print(f"\n📋 {self.state.current_menu.value.upper()} 菜单")
        print("-" * config.ui.menu_width)

        for item in current_menu:
            status = "✓" if item.enabled else "✗"
            icon = item.icon if item.icon else ""
            print(f"{item.key}. {icon} {item.title} - {item.description} {status}")

        print("-" * config.ui.menu_width)

        # 显示状态信息
        if self.state.status_message:
            print(f"状态: {self.state.status_message}")

        if self.state.last_data is not None:
            print(f"当前数据: {len(self.state.last_data)} 条记录")

    def _get_user_input(self, prompt: str) -> str:
        """获取用户输入"""
        try:
            return input(f"\n{prompt}: ").strip()
        except (EOFError, KeyboardInterrupt):
            return ""

    def _handle_menu_choice(self, choice: str):
        """处理菜单选择"""
        current_menu = self.menus[self.state.current_menu]

        for item in current_menu:
            if item.key == choice:
                if item.enabled:
                    try:
                        self.state.is_processing = True
                        self.state.status_message = f"执行: {item.title}"
                        item.action()
                    except Exception as e:
                        self._show_message(f"操作失败: {e}", "error")
                        self.logger.error(f"菜单操作错误: {e}")
                    finally:
                        self.state.is_processing = False
                else:
                    self._show_message("该功能暂不可用", "warning")
                return

        self._show_message("无效选择，请重新输入", "warning")

    def _show_message(self, message: str, msg_type: str = "info"):
        """显示消息"""
        symbols = config.ui.menu_symbols
        icon_map = {
            "success": symbols['success'],
            "error": symbols['error'],
            "warning": symbols['warning'],
            "info": symbols['info']
        }

        icon = icon_map.get(msg_type, symbols['info'])
        print(f"{icon} {message}")

    def _load_data(self, days: int = 30, use_cache: bool = True) -> Optional[pd.DataFrame]:
        """加载数据"""
        if self.state.last_data is not None and use_cache:
            return self.state.last_data

        self.progress_bar.start(100, "加载数据")

        try:
            # 检查数据库连接状态
            if not self.db.is_connected:
                self._show_message("数据库未连接，尝试从缓存加载数据", "warning")
                # 尝试从缓存获取数据
                df = self.db.get_fraud_cases(days=days, use_cache=True)
                if df is not None and not df.empty:
                    self.progress_bar.update(100, "从缓存加载数据完成")
                    self.progress_bar.finish("数据加载完成")
                    self.state.last_data = df
                    return df
                else:
                    self.progress_bar.finish("无缓存数据")
                    self._show_message("无可用数据", "warning")
                    return None

            # 从数据库获取数据
            self.progress_bar.update(20, "连接数据库")
            df = self.db.get_fraud_cases(days=days, use_cache=use_cache)

            if df is None or df.empty:
                self._show_message("未获取到数据", "warning")
                return None

            # 数据清洗
            self.progress_bar.update(60, "清洗数据")
            cleaned_df, quality_report = self.data_processor.clean_dataframe(df)

            self.progress_bar.update(100, "数据加载完成")
            self.progress_bar.finish("数据加载完成")

            # 显示数据质量报告
            self._show_data_quality_report(quality_report)

            self.state.last_data = cleaned_df
            return cleaned_df

        except Exception as e:
            self.progress_bar.finish("数据加载失败")
            self._show_message(f"数据加载失败: {e}", "error")
            return None

    def _show_data_quality_report(self, quality_report):
        """显示数据质量报告"""
        print(f"\n📊 数据质量报告:")
        print(f"   原始记录: {quality_report.total_records:,}")
        print(f"   有效记录: {quality_report.valid_records:,}")
        print(f"   无效记录: {quality_report.invalid_records:,}")
        print(f"   质量评分: {quality_report.quality_score:.2f}%")

        if quality_report.issues:
            print("   主要问题:")
            for issue in quality_report.issues[:5]:
                print(f"     - {issue}")

    def _get_analysis_params(self) -> Dict[str, Any]:
        """获取分析参数"""
        print("\n⚙️ 分析参数配置:")

        # 获取天数
        days_input = self._get_user_input("分析天数 (默认30)")
        days = int(days_input) if days_input.isdigit() else 30

        # 获取其他参数
        params = {
            'days': days,
            'use_cache': True,
            'min_cases': config.analysis.min_app_cases,
            'amount_threshold': config.analysis.amount_thresholds['high']
        }

        return params

    # ==================== 主菜单处理方法 ====================

    def _menu_analysis(self):
        """进入分析菜单"""
        self.state.current_menu = MenuType.ANALYSIS

    def _menu_visualization(self):
        """进入可视化菜单"""
        self.state.current_menu = MenuType.VISUALIZATION

    def _menu_report(self):
        """进入报告菜单"""
        self.state.current_menu = MenuType.REPORT

    def _menu_detection(self):
        """进入检测菜单"""
        self.state.current_menu = MenuType.DETECTION

    def _menu_network(self):
        """进入网络分析菜单"""
        self.state.current_menu = MenuType.NETWORK

    def _menu_settings(self):
        """进入设置菜单"""
        self.state.current_menu = MenuType.SETTINGS

    def _clear_cache(self):
        """清除缓存"""
        try:
            self.db.clear_cache()
            self.state.last_data = None
            self.state.last_analysis = None
            self._show_message("缓存已清除", "success")
        except Exception as e:
            self._show_message(f"清除缓存失败: {e}", "error")

    def _exit_system(self):
        """退出系统"""
        if config.ui.confirm_dangerous_actions:
            confirm = self._get_user_input("确认退出系统? (y/N)")
            if confirm.lower() != 'y':
                return

        self._show_message("正在退出系统...", "info")
        self._cleanup()
        sys.exit(0)

    def _back_to_main(self):
        """返回主菜单"""
        self.state.current_menu = MenuType.MAIN

    # ==================== 分析菜单处理方法 ====================

    def _analyze_apps(self):
        """APP风险分析"""
        params = self._get_analysis_params()
        df = self._load_data(params['days'])

        if df is None:
            return

        self.progress_bar.start(100, "APP风险分析")

        try:
            analyzer = self.analyzers['app']
            self.progress_bar.update(50, "执行分析")

            result = analyzer.analyze(df)
            self.progress_bar.update(100, "分析完成")
            self.progress_bar.finish("APP风险分析完成")

            # 显示结果
            self._display_analysis_result(result, "APP风险分析")

            # 保存到状态
            if not self.state.last_analysis:
                self.state.last_analysis = {}
            self.state.last_analysis['app'] = result

        except Exception as e:
            self.progress_bar.finish("分析失败")
            self._show_message(f"APP分析失败: {e}", "error")

    def _analyze_financial(self):
        """财务影响分析"""
        params = self._get_analysis_params()
        df = self._load_data(params['days'])

        if df is None:
            return

        self.progress_bar.start(100, "财务影响分析")

        try:
            analyzer = self.analyzers['financial']
            self.progress_bar.update(50, "执行分析")

            result = analyzer.analyze(df)
            self.progress_bar.update(100, "分析完成")
            self.progress_bar.finish("财务影响分析完成")

            # 显示结果
            self._display_analysis_result(result, "财务影响分析")

            # 保存到状态
            if not self.state.last_analysis:
                self.state.last_analysis = {}
            self.state.last_analysis['financial'] = result

        except Exception as e:
            self.progress_bar.finish("分析失败")
            self._show_message(f"财务分析失败: {e}", "error")

    def _analyze_victims(self):
        """受害人画像分析"""
        params = self._get_analysis_params()
        df = self._load_data(params['days'])

        if df is None:
            return

        self.progress_bar.start(100, "受害人画像分析")

        try:
            analyzer = self.analyzers['victim']
            self.progress_bar.update(50, "执行分析")

            result = analyzer.analyze(df)
            self.progress_bar.update(100, "分析完成")
            self.progress_bar.finish("受害人画像分析完成")

            # 显示结果
            self._display_analysis_result(result, "受害人画像分析")

            # 保存到状态
            if not self.state.last_analysis:
                self.state.last_analysis = {}
            self.state.last_analysis['victim'] = result

        except Exception as e:
            self.progress_bar.finish("分析失败")
            self._show_message(f"受害人分析失败: {e}", "error")

    def _analyze_fraud_methods(self):
        """诈骗手段分析"""
        params = self._get_analysis_params()
        df = self._load_data(params['days'])

        if df is None:
            return

        self.progress_bar.start(100, "诈骗手段分析")

        try:
            analyzer = self.analyzers['fraud_method']
            self.progress_bar.update(50, "执行分析")

            result = analyzer.analyze(df)
            self.progress_bar.update(100, "分析完成")
            self.progress_bar.finish("诈骗手段分析完成")

            # 显示结果
            self._display_analysis_result(result, "诈骗手段分析")

            # 保存到状态
            if not self.state.last_analysis:
                self.state.last_analysis = {}
            self.state.last_analysis['fraud_method'] = result

        except Exception as e:
            self.progress_bar.finish("分析失败")
            self._show_message(f"诈骗手段分析失败: {e}", "error")

    def _analyze_comprehensive(self):
        """综合分析"""
        params = self._get_analysis_params()
        df = self._load_data(params['days'])

        if df is None:
            return

        self.progress_bar.start(100, "综合分析")

        try:
            results = {}
            total_analyzers = len(self.analyzers)

            for i, (name, analyzer) in enumerate(self.analyzers.items()):
                progress = int((i / total_analyzers) * 80)
                self.progress_bar.update(progress, f"执行{name}分析")

                result = analyzer.analyze(df)
                results[name] = result

            self.progress_bar.update(100, "综合分析完成")
            self.progress_bar.finish("综合分析完成")

            # 显示综合结果摘要
            self._display_comprehensive_summary(results)

            # 保存到状态
            self.state.last_analysis = results

        except Exception as e:
            self.progress_bar.finish("综合分析失败")
            self._show_message(f"综合分析失败: {e}", "error")

    # ==================== 可视化菜单处理方法 ====================

    def _create_trend_charts(self):
        """创建趋势图表"""
        df = self._load_data()
        if df is None:
            return

        self.progress_bar.start(100, "生成趋势图表")

        try:
            self.progress_bar.update(30, "创建趋势图")
            fig = self.visualizer.create_trend_chart(df, "案件趋势分析")

            self.progress_bar.update(80, "保存图表")
            filename = f"trend_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            if self.visualizer.save_chart(fig, filename):
                self.progress_bar.finish("趋势图表已保存")
                self._show_message(f"图表已保存: {filename}.png", "success")
            else:
                self.progress_bar.finish("保存失败")

        except Exception as e:
            self.progress_bar.finish("生成失败")
            self._show_message(f"趋势图表生成失败: {e}", "error")

    def _create_ranking_charts(self):
        """创建排行图表"""
        df = self._load_data()
        if df is None:
            return

        # 获取排行数量
        top_n_input = self._get_user_input("显示TOP数量 (默认20)")
        top_n = int(top_n_input) if top_n_input.isdigit() else 20

        self.progress_bar.start(100, "生成排行图表")

        try:
            self.progress_bar.update(30, "创建排行图")
            fig = self.visualizer.create_app_ranking_chart(df, top_n, f"TOP{top_n} APP排行")

            self.progress_bar.update(80, "保存图表")
            filename = f"ranking_chart_top{top_n}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            if self.visualizer.save_chart(fig, filename):
                self.progress_bar.finish("排行图表已保存")
                self._show_message(f"图表已保存: {filename}.png", "success")
            else:
                self.progress_bar.finish("保存失败")

        except Exception as e:
            self.progress_bar.finish("生成失败")
            self._show_message(f"排行图表生成失败: {e}", "error")

    def _create_distribution_charts(self):
        """创建分布图表"""
        df = self._load_data()
        if df is None:
            return

        self.progress_bar.start(100, "生成分布图表")

        try:
            if 'involved_amount' in df.columns:
                self.progress_bar.update(30, "创建金额分布图")
                fig = self.visualizer.create_amount_distribution_chart(df)

                self.progress_bar.update(80, "保存图表")
                filename = f"amount_distribution_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                if self.visualizer.save_chart(fig, filename):
                    self.progress_bar.finish("分布图表已保存")
                    self._show_message(f"图表已保存: {filename}.png", "success")
                else:
                    self.progress_bar.finish("保存失败")
            else:
                self.progress_bar.finish("数据不足")
                self._show_message("数据中缺少金额字段", "warning")

        except Exception as e:
            self.progress_bar.finish("生成失败")
            self._show_message(f"分布图表生成失败: {e}", "error")

    def _create_correlation_charts(self):
        """创建关联图表"""
        df = self._load_data()
        if df is None:
            return

        self.progress_bar.start(100, "生成关联图表")

        try:
            self.progress_bar.update(30, "分析数据关联")
            # 这里可以添加关联分析逻辑

            self.progress_bar.update(80, "创建关联图")
            # 创建关联图表的逻辑

            self.progress_bar.finish("关联图表生成完成")
            self._show_message("关联图表功能开发中", "info")

        except Exception as e:
            self.progress_bar.finish("生成失败")
            self._show_message(f"关联图表生成失败: {e}", "error")

    def _create_dashboard(self):
        """创建综合仪表板"""
        df = self._load_data()
        if df is None:
            return

        self.progress_bar.start(100, "生成综合仪表板")

        try:
            self.progress_bar.update(20, "创建仪表板图表")
            charts = self.visualizer.create_dashboard_charts(df)

            self.progress_bar.update(60, "保存图表")
            saved_files = self.visualizer.save_all_charts(charts, "dashboard_")

            self.progress_bar.update(100, "仪表板生成完成")
            self.progress_bar.finish("综合仪表板已生成")

            self._show_message(f"已生成 {len(saved_files)} 个图表", "success")
            for file in saved_files[:5]:  # 显示前5个文件名
                print(f"  - {file}")
            if len(saved_files) > 5:
                print(f"  ... 还有 {len(saved_files) - 5} 个文件")

        except Exception as e:
            self.progress_bar.finish("生成失败")
            self._show_message(f"仪表板生成失败: {e}", "error")

    # ==================== 报告生成菜单处理方法 ====================

    def _generate_html_report(self):
        """生成HTML报告"""
        df = self._load_data()
        if df is None:
            return

        # 确保有分析结果
        if not self.state.last_analysis:
            self._show_message("请先执行数据分析", "warning")
            return

        self.progress_bar.start(100, "生成HTML报告")

        try:
            self.progress_bar.update(30, "准备报告数据")

            # 检测模式（如果需要）
            pattern_results = None
            try:
                # 只有在有数据的情况下才尝试检测模式
                if df is not None and not df.empty:
                    pattern_results = self.pattern_detector.detect_patterns(df)
            except Exception as e:
                self.logger.warning(f"模式检测失败: {e}")

            self.progress_bar.update(60, "生成报告")
            report_path = self.report_generator.generate_comprehensive_report(
                df, self.state.last_analysis, pattern_results
            )

            self.progress_bar.update(100, "报告生成完成")
            self.progress_bar.finish("HTML报告已生成")

            self._show_message(f"报告已保存: {report_path}", "success")

        except Exception as e:
            self.progress_bar.finish("报告生成失败")
            self._show_message(f"HTML报告生成失败: {e}", "error")

    def _generate_risk_report(self):
        """生成风险评估报告"""
        df = self._load_data()
        if df is None:
            return

        self.progress_bar.start(100, "生成风险评估报告")

        try:
            self.progress_bar.update(50, "风险评估")
            # 这里可以添加专门的风险评估逻辑

            self.progress_bar.finish("风险评估报告生成完成")
            self._show_message("风险评估报告功能开发中", "info")

        except Exception as e:
            self.progress_bar.finish("报告生成失败")
            self._show_message(f"风险评估报告生成失败: {e}", "error")

    def _generate_summary_report(self):
        """生成统计摘要"""
        df = self._load_data()
        if df is None:
            return

        try:
            print("\n📊 数据统计摘要")
            print("=" * 50)

            # 基础统计
            total_cases = len(df)
            unique_apps = df['app_name'].nunique() if 'app_name' in df.columns else 0
            total_amount = df['involved_amount'].sum() if 'involved_amount' in df.columns else 0

            print(f"案件总数: {total_cases:,}")
            print(f"涉及APP: {unique_apps:,}")
            print(f"涉案金额: {total_amount:,.2f} 元")
            print(f"平均金额: {total_amount/max(total_cases, 1):,.2f} 元")

            # 时间范围
            if 'insert_day' in df.columns:
                date_range = df['insert_day'].max() - df['insert_day'].min()
                print(f"时间跨度: {date_range.days} 天")

            # TOP APP
            if 'app_name' in df.columns:
                print(f"\nTOP10 APP:")
                top_apps = df['app_name'].value_counts().head(10)
                for i, (app, count) in enumerate(top_apps.items(), 1):
                    print(f"  {i:2d}. {app}: {count} 起")

            print("=" * 50)

        except Exception as e:
            self._show_message(f"统计摘要生成失败: {e}", "error")

    def _generate_custom_report(self):
        """生成自定义报告"""
        self._show_message("自定义报告功能开发中", "info")

    # ==================== 检测菜单处理方法 ====================

    def _detect_anomalies(self):
        """检测异常模式"""
        df = self._load_data()
        if df is None:
            return

        # 检查是否有足够的数据进行分析
        if len(df) < 10:
            self._show_message("数据量不足，至少需要10条记录才能进行异常检测", "warning")
            return

        self.progress_bar.start(100, "异常检测")

        try:
            self.progress_bar.update(30, "分析数据模式")
            patterns = self.pattern_detector.detect_patterns(df)

            self.progress_bar.update(80, "识别异常")
            anomalies = [p for p in patterns if p.severity in ['高', '极高']]

            self.progress_bar.finish("异常检测完成")

            if anomalies:
                print(f"\n🚨 发现 {len(anomalies)} 个异常模式:")
                for i, anomaly in enumerate(anomalies[:10], 1):
                    print(f"  {i}. [{anomaly.pattern_type}] {anomaly.description}")
                    print(f"     风险等级: {anomaly.severity}")
                    print(f"     涉及APP: {', '.join(anomaly.affected_apps[:3])}")
                    if len(anomaly.affected_apps) > 3:
                        print(f"     ... 还有 {len(anomaly.affected_apps) - 3} 个")
                    print()
            else:
                self._show_message("未发现明显异常模式", "info")

        except Exception as e:
            self.progress_bar.finish("检测失败")
            self._show_message(f"异常检测失败: {e}", "error")

    def _detect_new_apps(self):
        """检测新APP"""
        df = self._load_data()
        if df is None:
            return

        self.progress_bar.start(100, "新APP检测")

        try:
            self.progress_bar.update(50, "分析APP出现时间")

            if 'app_name' not in df.columns or 'insert_day' not in df.columns:
                self.progress_bar.finish("数据不足")
                self._show_message("数据中缺少必要字段", "warning")
                return

            # 计算每个APP的首次出现时间
            app_first_seen = df.groupby('app_name')['insert_day'].min().reset_index()
            app_first_seen.columns = ['app_name', 'first_seen']

            # 获取最近N天的新APP
            recent_days = 7
            cutoff_date = datetime.now() - timedelta(days=recent_days)
            new_apps = app_first_seen[app_first_seen['first_seen'] >= cutoff_date]

            self.progress_bar.finish("新APP检测完成")

            if not new_apps.empty:
                print(f"\n🆕 最近{recent_days}天发现 {len(new_apps)} 个新APP:")
                for _, app in new_apps.head(15).iterrows():
                    app_cases = df[df['app_name'] == app['app_name']]
                    case_count = len(app_cases)
                    total_amount = app_cases['involved_amount'].sum() if 'involved_amount' in df.columns else 0

                    print(f"  • {app['app_name']}")
                    print(f"    首次出现: {app['first_seen'].strftime('%Y-%m-%d')}")
                    print(f"    案件数量: {case_count}")
                    print(f"    涉案金额: {total_amount:,.2f} 元")
                    print()
            else:
                self._show_message(f"最近{recent_days}天未发现新APP", "info")

        except Exception as e:
            self.progress_bar.finish("检测失败")
            self._show_message(f"新APP检测失败: {e}", "error")

    def _detect_clusters(self):
        """检测案件集群"""
        self._show_message("集群分析功能开发中", "info")

    def _detect_temporal_anomalies(self):
        """检测时序异常"""
        self._show_message("时序异常检测功能开发中", "info")

    # ==================== 网络分析菜单处理方法 ====================

    def _analyze_network_relationships(self):
        """分析网络关系"""
        df = self._load_data()
        if df is None:
            return

        self.progress_bar.start(100, "网络关系分析")

        try:
            self.progress_bar.update(30, "构建关系网络")
            network_data = self.network_analyzer.build_network(df)

            self.progress_bar.update(70, "分析网络结构")
            analysis_result = self.network_analyzer.analyze_network(network_data)

            self.progress_bar.finish("网络关系分析完成")

            # 显示分析结果
            print(f"\n🕸️ 网络关系分析结果:")
            print(f"  节点数量: {analysis_result.get('node_count', 0)}")
            print(f"  边数量: {analysis_result.get('edge_count', 0)}")
            print(f"  连通组件: {analysis_result.get('components', 0)}")
            print(f"  网络密度: {analysis_result.get('density', 0):.4f}")

        except Exception as e:
            self.progress_bar.finish("分析失败")
            self._show_message(f"网络关系分析失败: {e}", "error")

    def _detect_communities(self):
        """发现社区"""
        self._show_message("社区发现功能开发中", "info")

    def _analyze_centrality(self):
        """分析中心性"""
        self._show_message("中心性分析功能开发中", "info")

    def _analyze_paths(self):
        """分析路径"""
        self._show_message("路径分析功能开发中", "info")

    # ==================== 设置菜单处理方法 ====================

    def _configure_database(self):
        """配置数据库"""
        print(f"\n🔧 当前数据库配置:")
        print(f"  主机: {config.db.host}")
        print(f"  端口: {config.db.port}")
        print(f"  数据库: {config.db.database}")
        print(f"  用户名: {config.db.username}")

        # 测试连接
        if self.db.is_connected:
            self._show_message("数据库连接正常", "success")
        else:
            self._show_message("数据库连接失败", "error")
            
        # 提供重新连接选项
        retry = self._get_user_input("是否尝试重新连接数据库? (y/N)")
        if retry.lower() == 'y':
            if self.db.test_connection():
                self._show_message("数据库连接成功", "success")
            else:
                self._show_message("数据库连接失败", "error")

    def _configure_analysis(self):
        """配置分析参数"""
        print(f"\n⚙️ 当前分析配置:")
        print(f"  默认分析天数: {config.analysis.default_days}")
        print(f"  最小APP案件数: {config.analysis.min_app_cases}")
        print(f"  批处理大小: {config.analysis.batch_size}")
        print(f"  金额阈值:")
        for level, amount in config.analysis.amount_thresholds.items():
            print(f"    {level}: {amount:,} 元")

    def _configure_visualization(self):
        """配置可视化"""
        print(f"\n🎨 当前可视化配置:")
        print(f"  图表样式: {config.visualization.style}")
        print(f"  图表尺寸: {config.visualization.figsize}")
        print(f"  DPI: {config.visualization.dpi}")
        print(f"  字体: {config.visualization.font_family}")
        print(f"  字体大小: {config.visualization.font_size}")

    def _configure_export(self):
        """配置导出"""
        print(f"\n📁 当前导出配置:")
        print(f"  导出目录: {config.paths.export_dir}")
        print(f"  缓存目录: {config.paths.cache_dir}")
        print(f"  支持格式: {', '.join(config.report.export_formats)}")

    def _show_config(self):
        """显示完整配置"""
        print(f"\n📋 系统配置概览:")
        config_dict = config.to_dict()

        for section, settings in config_dict.items():
            print(f"\n[{section.upper()}]")
            for key, value in settings.items():
                if isinstance(value, dict):
                    print(f"  {key}:")
                    for sub_key, sub_value in value.items():
                        print(f"    {sub_key}: {sub_value}")
                else:
                    print(f"  {key}: {value}")

    # ==================== 辅助方法 ====================

    def _display_analysis_result(self, result, analysis_type: str):
        """显示分析结果"""
        print(f"\n📊 {analysis_type}结果:")
        print("-" * 50)

        if hasattr(result, 'data_summary') and result.data_summary:
            print("数据摘要:")
            for key, value in result.data_summary.items():
                print(f"  {key}: {value}")

        if hasattr(result, 'results') and result.results:
            print("\n主要发现:")
            for key, value in result.results.items():
                if isinstance(value, dict):
                    print(f"  {key}:")
                    for sub_key, sub_value in value.items():
                        print(f"    {sub_key}: {sub_value}")
                else:
                    print(f"  {key}: {value}")

        if hasattr(result, 'recommendations') and result.recommendations:
            print("\n建议措施:")
            for i, rec in enumerate(result.recommendations, 1):
                print(f"  {i}. {rec}")

        print("-" * 50)

    def _display_comprehensive_summary(self, results: Dict[str, Any]):
        """显示综合分析摘要"""
        print(f"\n📈 综合分析摘要")
        print("=" * 60)

        for analysis_type, result in results.items():
            print(f"\n[{analysis_type.upper()}]")

            if hasattr(result, 'data_summary') and result.data_summary:
                # 显示关键指标
                key_metrics = []
                for key, value in result.data_summary.items():
                    if isinstance(value, (int, float)):
                        key_metrics.append(f"{key}: {value:,}")
                    else:
                        key_metrics.append(f"{key}: {value}")

                if key_metrics:
                    print("  " + " | ".join(key_metrics[:3]))

            if hasattr(result, 'results') and result.results:
                # 显示主要发现
                findings = []
                for key, value in result.results.items():
                    if isinstance(value, (int, float)) and value > 0:
                        findings.append(f"{key}: {value}")

                if findings:
                    print("  主要发现: " + " | ".join(findings[:2]))

        print("=" * 60)

    def _cleanup(self):
        """清理资源"""
        try:
            # 关闭数据库连接
            if self.db:
                self.db.close()

            # 关闭所有图表
            self.visualizer.close_all_figures()

            self._show_message("资源清理完成", "success")

        except Exception as e:
            self.logger.error(f"资源清理失败: {e}")


# ==================== 工厂函数和主函数 ====================

def create_ui_manager() -> UIManager:
    """创建UI管理器实例"""
    return UIManager()


def main():
    """主函数"""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('fraud_monitor.log'),
            logging.StreamHandler()
        ]
    )

    try:
        # 创建UI管理器
        ui_manager = create_ui_manager()

        # 运行主循环
        ui_manager.run()

    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序运行异常: {e}")
        logging.error(f"程序异常: {e}")
    finally:
        print("🔒 程序已退出")


if __name__ == "__main__":
    main()