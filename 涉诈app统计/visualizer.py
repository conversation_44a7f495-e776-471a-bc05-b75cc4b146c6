"""
可视化和图表生成模块
负责生成各种图表和可视化内容
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import logging

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class ChartVisualizer:
    """图表可视化器"""

    def __init__(self, export_dir: str = None):
        self.logger = logging.getLogger(__name__)

        # 导出目录
        self.export_dir = Path(export_dir) if export_dir else Path("./charts")
        self.export_dir.mkdir(exist_ok=True)

        # 颜色配置
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'success': '#5C946E',
            'warning': '#F18F01',
            'danger': '#C73E1D',
            'info': '#8B9DC3',
            'background': '#FAFAFA',
            'text': '#2C3E50',
            'grid': '#E8E8E8'
        }

        # 调色板
        self.color_palette = [
            '#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#5C946E',
            '#8B9DC3', '#F0BE38', '#7B68EE', '#20B2AA', '#FF6347',
            '#32CD32', '#FFD700', '#FF69B4', '#1E90FF', '#FF4500',
            '#9370DB', '#00CED1', '#FFA500', '#DC143C', '#00FA9A'
        ]

        # 图表样式配置
        self.style_config = {
            'figure_size': (16, 9),
            'dpi': 300,
            'title_fontsize': 20,
            'label_fontsize': 14,
            'tick_fontsize': 12,
            'legend_fontsize': 12,
            'line_width': 3,
            'marker_size': 8
        }

    def create_trend_chart(self, df: pd.DataFrame, title: str = "趋势图") -> plt.Figure:
        """创建趋势图"""
        fig, ax = plt.subplots(figsize=self.style_config['figure_size'])
        fig.patch.set_facecolor(self.colors['background'])

        if 'insert_day' not in df.columns:
            self.logger.error("数据中缺少 insert_day 列")
            return fig

        # 按日期分组统计
        daily_stats = df.groupby('insert_day').size().reset_index(name='case_count')

        # 绘制趋势线
        ax.plot(daily_stats['insert_day'], daily_stats['case_count'],
                color=self.colors['primary'], linewidth=self.style_config['line_width'],
                marker='o', markersize=self.style_config['marker_size'], alpha=0.8)

        # 填充区域
        ax.fill_between(daily_stats['insert_day'], daily_stats['case_count'],
                        alpha=0.3, color=self.colors['primary'])

        # 设置标题和标签
        ax.set_title(title, fontsize=self.style_config['title_fontsize'],
                     fontweight='bold', pad=20, color=self.colors['text'])
        ax.set_xlabel('日期', fontsize=self.style_config['label_fontsize'])
        ax.set_ylabel('案件数量', fontsize=self.style_config['label_fontsize'])

        # 格式化日期轴
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=2))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        # 网格和样式
        ax.grid(True, alpha=0.3, color=self.colors['grid'])
        ax.set_facecolor('white')

        plt.tight_layout()
        return fig

    def create_app_ranking_chart(self, df: pd.DataFrame, top_n: int = 10,
                                 title: str = "APP案件数排行") -> plt.Figure:
        """创建APP排行图"""
        fig, ax = plt.subplots(figsize=self.style_config['figure_size'])
        fig.patch.set_facecolor(self.colors['background'])

        if 'final_app_name' not in df.columns:
            self.logger.error("数据中缺少 final_app_name 列")
            return fig

        # 统计APP案件数
        app_counts = df['final_app_name'].value_counts().head(top_n)

        # 创建水平柱状图
        bars = ax.barh(range(len(app_counts)), app_counts.values,
                       color=self.color_palette[:len(app_counts)], alpha=0.8)

        # 设置标签
        ax.set_yticks(range(len(app_counts)))
        ax.set_yticklabels(app_counts.index, fontsize=self.style_config['tick_fontsize'])
        ax.set_xlabel('案件数量', fontsize=self.style_config['label_fontsize'])
        ax.set_title(title, fontsize=self.style_config['title_fontsize'],
                     fontweight='bold', pad=20, color=self.colors['text'])

        # 在柱子上添加数值
        for i, (bar, value) in enumerate(zip(bars, app_counts.values)):
            ax.text(value + max(app_counts.values) * 0.01, i, str(value),
                    va='center', fontsize=self.style_config['tick_fontsize'], fontweight='bold')

        # 反转y轴，让最高的在上面
        ax.invert_yaxis()
        ax.grid(True, axis='x', alpha=0.3, color=self.colors['grid'])
        ax.set_facecolor('white')

        plt.tight_layout()
        return fig

    def create_amount_distribution_chart(self, df: pd.DataFrame,
                                         title: str = "涉案金额分布") -> plt.Figure:
        """创建金额分布图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
        fig.patch.set_facecolor(self.colors['background'])

        if 'involved_amount' not in df.columns:
            self.logger.error("数据中缺少 involved_amount 列")
            return fig

        amounts = df['involved_amount'].dropna()
        if amounts.empty:
            return fig

        # 左图：直方图
        ax1.hist(amounts, bins=30, color=self.colors['primary'], alpha=0.7, edgecolor='white')
        ax1.set_title('金额分布直方图', fontsize=self.style_config['title_fontsize'], fontweight='bold')
        ax1.set_xlabel('涉案金额(元)', fontsize=self.style_config['label_fontsize'])
        ax1.set_ylabel('案件数量', fontsize=self.style_config['label_fontsize'])
        ax1.grid(True, alpha=0.3)

        # 右图：箱线图
        box_plot = ax2.boxplot(amounts, patch_artist=True)
        box_plot['boxes'][0].set_facecolor(self.colors['secondary'])
        box_plot['boxes'][0].set_alpha(0.7)

        ax2.set_title('金额分布箱线图', fontsize=self.style_config['title_fontsize'], fontweight='bold')
        ax2.set_ylabel('涉案金额(元)', fontsize=self.style_config['label_fontsize'])
        ax2.grid(True, alpha=0.3)

        # 添加统计信息
        stats_text = f'平均值: {amounts.mean():,.0f}元\n中位数: {amounts.median():,.0f}元\n最大值: {amounts.max():,.0f}元'
        ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes, fontsize=12,
                 verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()
        return fig

    def create_time_heatmap(self, df: pd.DataFrame,
                            title: str = "案件时间热力图") -> plt.Figure:
        """创建时间热力图"""
        fig, ax = plt.subplots(figsize=(14, 8))
        fig.patch.set_facecolor(self.colors['background'])

        if 'occurrence_time' not in df.columns:
            self.logger.error("数据中缺少 occurrence_time 列")
            return fig

        # 转换时间格式
        df_copy = df.copy()
        if df_copy['occurrence_time'].dtype == 'object':
            df_copy['occurrence_time'] = pd.to_datetime(df_copy['occurrence_time'], errors='coerce')

        # 提取小时和星期
        df_copy['hour'] = df_copy['occurrence_time'].dt.hour
        df_copy['weekday'] = df_copy['occurrence_time'].dt.day_name()

        # 创建透视表
        heatmap_data = df_copy.groupby(['weekday', 'hour']).size().unstack(fill_value=0)

        # 重新排序星期
        weekday_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        heatmap_data = heatmap_data.reindex(weekday_order)

        # 绘制热力图
        sns.heatmap(heatmap_data, annot=True, fmt='d', cmap='YlOrRd',
                    cbar_kws={'label': '案件数量'}, ax=ax)

        ax.set_title(title, fontsize=self.style_config['title_fontsize'],
                     fontweight='bold', pad=20)
        ax.set_xlabel('小时', fontsize=self.style_config['label_fontsize'])
        ax.set_ylabel('星期', fontsize=self.style_config['label_fontsize'])

        plt.tight_layout()
        return fig

    def create_geographic_chart(self, df: pd.DataFrame,
                                title: str = "地区分布图") -> plt.Figure:
        """创建地区分布图"""
        fig, ax = plt.subplots(figsize=self.style_config['figure_size'])
        fig.patch.set_facecolor(self.colors['background'])

        if 'occurrence_area' not in df.columns:
            self.logger.error("数据中缺少 occurrence_area 列")
            return fig

        # 统计地区案件数
        area_counts = df['occurrence_area'].value_counts().head(15)

        # 创建饼图
        colors = self.color_palette[:len(area_counts)]
        wedges, texts, autotexts = ax.pie(area_counts.values, labels=area_counts.index,
                                          autopct='%1.1f%%', colors=colors, startangle=90)

        # 美化文本
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
            autotext.set_fontsize(10)

        ax.set_title(title, fontsize=self.style_config['title_fontsize'],
                     fontweight='bold', pad=20, color=self.colors['text'])

        plt.tight_layout()
        return fig

    def create_multi_line_chart(self, df: pd.DataFrame, group_col: str,
                                title: str = "多线趋势图") -> plt.Figure:
        """创建多线趋势图"""
        fig, ax = plt.subplots(figsize=self.style_config['figure_size'])
        fig.patch.set_facecolor(self.colors['background'])

        if group_col not in df.columns or 'insert_day' not in df.columns:
            self.logger.error(f"数据中缺少必要列: {group_col} 或 insert_day")
            return fig

        # 按组和日期统计
        grouped_data = df.groupby([group_col, 'insert_day']).size().unstack(fill_value=0).T

        # 绘制每条线
        for i, col in enumerate(grouped_data.columns[:10]):  # 最多显示10条线
            color = self.color_palette[i % len(self.color_palette)]
            ax.plot(grouped_data.index, grouped_data[col],
                    color=color, linewidth=self.style_config['line_width'],
                    marker='o', markersize=6, label=col, alpha=0.8)

        ax.set_title(title, fontsize=self.style_config['title_fontsize'],
                     fontweight='bold', pad=20, color=self.colors['text'])
        ax.set_xlabel('日期', fontsize=self.style_config['label_fontsize'])
        ax.set_ylabel('案件数量', fontsize=self.style_config['label_fontsize'])

        # 格式化日期轴
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3, color=self.colors['grid'])
        ax.set_facecolor('white')

        plt.tight_layout()
        return fig

    def create_correlation_matrix(self, df: pd.DataFrame,
                                  title: str = "相关性矩阵") -> plt.Figure:
        """创建相关性矩阵图"""
        fig, ax = plt.subplots(figsize=(12, 10))
        fig.patch.set_facecolor(self.colors['background'])

        # 选择数值列
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) < 2:
            self.logger.warning("数值列不足，无法创建相关性矩阵")
            return fig

        # 计算相关性
        corr_matrix = df[numeric_cols].corr()

        # 绘制热力图
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
        sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='coolwarm',
                    center=0, square=True, ax=ax, cbar_kws={'shrink': 0.8})

        ax.set_title(title, fontsize=self.style_config['title_fontsize'],
                     fontweight='bold', pad=20)

        plt.tight_layout()
        return fig

    def save_chart(self, fig: plt.Figure, filename: str,
                   format: str = 'png') -> bool:
        """保存图表"""
        try:
            filepath = self.export_dir / f"{filename}.{format}"
            fig.savefig(filepath, dpi=self.style_config['dpi'],
                        bbox_inches='tight', facecolor='white', edgecolor='none')
            self.logger.info(f"图表已保存: {filepath}")
            return True
        except Exception as e:
            self.logger.error(f"保存图表失败: {e}")
            return False

    def create_dashboard_charts(self, df: pd.DataFrame) -> Dict[str, plt.Figure]:
        """创建仪表板图表集合"""
        charts = {}

        try:
            # 1. 趋势图
            charts['trend'] = self.create_trend_chart(df, "案件趋势图")

            # 2. APP排行
            charts['app_ranking'] = self.create_app_ranking_chart(df, 15, "TOP15 APP排行")

            # 3. 金额分布
            if 'involved_amount' in df.columns:
                charts['amount_dist'] = self.create_amount_distribution_chart(df)

            # 4. 时间热力图
            if 'occurrence_time' in df.columns:
                charts['time_heatmap'] = self.create_time_heatmap(df)

            # 5. 地区分布
            if 'occurrence_area' in df.columns:
                charts['geographic'] = self.create_geographic_chart(df)

            # 6. 多线趋势（按APP分组）
            if 'final_app_name' in df.columns:
                charts['multi_trend'] = self.create_multi_line_chart(df, 'final_app_name', "APP趋势对比")

            self.logger.info(f"成功创建 {len(charts)} 个图表")

        except Exception as e:
            self.logger.error(f"创建仪表板图表失败: {e}")

        return charts

    def save_all_charts(self, charts: Dict[str, plt.Figure],
                        prefix: str = "") -> List[str]:
        """批量保存图表"""
        saved_files = []

        for chart_name, fig in charts.items():
            filename = f"{prefix}{chart_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            if self.save_chart(fig, filename):
                saved_files.append(f"{filename}.png")

        return saved_files

    def close_all_figures(self):
        """关闭所有图表"""
        plt.close('all')
        self.logger.info("已关闭所有图表")


# 工具函数
def create_visualizer(export_dir: str = None, custom_colors: Dict[str, str] = None) -> ChartVisualizer:
    """创建可视化器实例"""
    visualizer = ChartVisualizer(export_dir)

    if custom_colors:
        visualizer.colors.update(custom_colors)

    return visualizer