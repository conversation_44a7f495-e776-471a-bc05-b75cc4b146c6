"""
涉案APP监控系统 - 通用工具模块
包含项目中使用的各种通用工具函数和辅助方法
"""

import logging
import hashlib
import json
import re
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union
from pathlib import Path
import pandas as pd
import numpy as np


def setup_logging(log_level: int = logging.INFO, log_file: Optional[str] = None) -> logging.Logger:
    """
    设置日志配置
    
    Args:
        log_level: 日志级别
        log_file: 日志文件路径（可选）
        
    Returns:
        配置好的logger实例
    """
    # 创建logger
    logger = logging.getLogger('fraud_monitor')
    logger.setLevel(log_level)
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 添加控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 如果指定了日志文件，添加文件处理器
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def generate_md5_hash(data: str) -> str:
    """
    生成字符串的MD5哈希值
    
    Args:
        data: 输入字符串
        
    Returns:
        MD5哈希值
    """
    return hashlib.md5(data.encode('utf-8')).hexdigest()


def format_currency(amount: Union[int, float]) -> str:
    """
    格式化金额显示
    
    Args:
        amount: 金额数值
        
    Returns:
        格式化后的金额字符串
    """
    if amount >= 100000000:  # 1亿以上
        return f"{amount / 100000000:.2f}亿"
    elif amount >= 10000:  # 1万以上
        return f"{amount / 10000:.2f}万"
    else:
        return f"{amount:.2f}"


def format_date_range(start_date: datetime, end_date: datetime) -> str:
    """
    格式化日期范围显示
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
        
    Returns:
        格式化后的日期范围字符串
    """
    if start_date.date() == end_date.date():
        return start_date.strftime("%Y-%m-%d")
    else:
        return f"{start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}"


def validate_phone_number(phone: str) -> bool:
    """
    验证手机号格式是否正确
    
    Args:
        phone: 手机号字符串
        
    Returns:
        是否为有效手机号
    """
    if not phone:
        return False
    
    # 中国手机号正则表达式
    pattern = r'^1[3-9]\d{9}$'
    return bool(re.match(pattern, str(phone)))


def validate_id_card(id_card: str) -> bool:
    """
    验证身份证号格式是否正确
    
    Args:
        id_card: 身份证号字符串
        
    Returns:
        是否为有效身份证号
    """
    if not id_card:
        return False
    
    # 18位身份证号正则表达式
    pattern = r'^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$'
    return bool(re.match(pattern, str(id_card)))


def calculate_age(birth_date: Union[str, datetime]) -> int:
    """
    根据出生日期计算年龄
    
    Args:
        birth_date: 出生日期
        
    Returns:
        年龄
    """
    if not birth_date:
        return 0
    
    if isinstance(birth_date, str):
        try:
            birth_date = datetime.strptime(birth_date, "%Y-%m-%d")
        except ValueError:
            return 0
    
    today = datetime.today()
    age = today.year - birth_date.year
    
    # 检查是否还没过生日
    if (today.month, today.day) < (birth_date.month, birth_date.day):
        age -= 1
        
    return max(0, age)


def safe_divide(numerator: Union[int, float], denominator: Union[int, float], 
                default: Union[int, float] = 0) -> Union[int, float]:
    """
    安全除法运算，避免除零错误
    
    Args:
        numerator: 分子
        denominator: 分母
        default: 默认返回值（当分母为0时）
        
    Returns:
        除法结果或默认值
    """
    if denominator == 0:
        return default
    return numerator / denominator


def percentage(part: Union[int, float], whole: Union[int, float], 
               decimals: int = 2) -> float:
    """
    计算百分比
    
    Args:
        part: 部分值
        whole: 总值
        decimals: 保留小数位数
        
    Returns:
        百分比值
    """
    if whole == 0:
        return 0.0
    return round((part / whole) * 100, decimals)


def chunks(lst: List[Any], n: int) -> List[List[Any]]:
    """
    将列表分割成指定大小的块
    
    Args:
        lst: 输入列表
        n: 每块大小
        
    Yields:
        分割后的子列表
    """
    for i in range(0, len(lst), n):
        yield lst[i:i + n]


def merge_dicts(dict1: Dict[Any, Any], dict2: Dict[Any, Any]) -> Dict[Any, Any]:
    """
    合并两个字典，处理嵌套字典
    
    Args:
        dict1: 第一个字典
        dict2: 第二个字典
        
    Returns:
        合并后的字典
    """
    result = dict1.copy()
    
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = merge_dicts(result[key], value)
        else:
            result[key] = value
            
    return result


def is_weekend(date: Union[str, datetime]) -> bool:
    """
    判断日期是否为周末
    
    Args:
        date: 日期
        
    Returns:
        是否为周末
    """
    if isinstance(date, str):
        date = datetime.strptime(date, "%Y-%m-%d")
    return date.weekday() >= 5  # 5=Saturday, 6=Sunday


def get_date_range(days: int = 30) -> tuple:
    """
    获取指定天数的日期范围
    
    Args:
        days: 天数
        
    Returns:
        (开始日期, 结束日期) 元组
    """
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    return (start_date, end_date)


def clean_text(text: str) -> str:
    """
    清理文本，移除多余空格和特殊字符
    
    Args:
        text: 输入文本
        
    Returns:
        清理后的文本
    """
    if not text:
        return ""
    
    # 移除首尾空格
    text = text.strip()
    
    # 将多个连续空格替换为单个空格
    text = re.sub(r'\s+', ' ', text)
    
    return text


def extract_chinese(text: str) -> str:
    """
    从文本中提取中文字符
    
    Args:
        text: 输入文本
        
    Returns:
        提取的中文字符
    """
    if not text:
        return ""
    
    # 中文字符正则表达式
    pattern = r'[\u4e00-\u9fff]+'
    chinese_chars = re.findall(pattern, text)
    return ''.join(chinese_chars)


def mask_sensitive_info(info: str, mask_char: str = "*") -> str:
    """
    遮蔽敏感信息（如手机号、身份证号等）
    
    Args:
        info: 敏感信息
        mask_char: 遮蔽字符
        
    Returns:
        遮蔽后的信息
    """
    if not info:
        return ""
    
    info = str(info)
    length = len(info)
    
    # 手机号遮蔽（保留前3位和后4位）
    if length == 11 and info.isdigit():
        return info[:3] + mask_char * 4 + info[-4:]
    
    # 身份证号遮蔽（保留前6位和后4位）
    elif length == 18 and (info[:17].isdigit() or (info[:17].isdigit() and info[17].upper() in "0123456789X")):
        return info[:6] + mask_char * 8 + info[-4:]
    
    # 其他信息遮蔽（保留首尾各1位）
    elif length > 2:
        return info[0] + mask_char * (length - 2) + info[-1]
    
    # 短信息直接遮蔽
    else:
        return mask_char * length


def convert_to_dataframe(data: Union[List[Dict], Dict]) -> pd.DataFrame:
    """
    将数据转换为DataFrame
    
    Args:
        data: 输入数据（字典列表或字典）
        
    Returns:
        DataFrame对象
    """
    if isinstance(data, list):
        return pd.DataFrame(data)
    elif isinstance(data, dict):
        # 如果是单个字典，转换为单行DataFrame
        return pd.DataFrame([data])
    else:
        return pd.DataFrame()


def get_unique_values(df: pd.DataFrame, column: str) -> List[Any]:
    """
    获取DataFrame某列的唯一值
    
    Args:
        df: DataFrame对象
        column: 列名
        
    Returns:
        唯一值列表
    """
    if column not in df.columns:
        return []
    
    return df[column].dropna().unique().tolist()


def safe_get_nested_value(data: Dict, keys: List[str], default: Any = None) -> Any:
    """
    安全获取嵌套字典中的值
    
    Args:
        data: 字典数据
        keys: 键列表（按层级顺序）
        default: 默认值
        
    Returns:
        获取到的值或默认值
    """
    current = data
    try:
        for key in keys:
            current = current[key]
        return current
    except (KeyError, TypeError):
        return default


def serialize_for_json(obj: Any) -> Any:
    """
    将对象序列化为JSON兼容格式
    
    Args:
        obj: 待序列化对象
        
    Returns:
        JSON兼容的对象
    """
    if isinstance(obj, (datetime, pd.Timestamp)):
        return obj.isoformat()
    elif isinstance(obj, (np.integer, np.floating)):
        return obj.item()
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif pd.isna(obj):
        return None
    else:
        return obj


# 导出函数列表
__all__ = [
    'setup_logging',
    'generate_md5_hash',
    'format_currency',
    'format_date_range',
    'validate_phone_number',
    'validate_id_card',
    'calculate_age',
    'safe_divide',
    'percentage',
    'chunks',
    'merge_dicts',
    'is_weekend',
    'get_date_range',
    'clean_text',
    'extract_chinese',
    'mask_sensitive_info',
    'convert_to_dataframe',
    'get_unique_values',
    'safe_get_nested_value',
    'serialize_for_json'
]