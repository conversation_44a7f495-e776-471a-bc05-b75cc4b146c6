"""
涉诈APP监控系统 - 主程序
整合所有模块功能，提供完整的监控、分析和预警功能
"""

import logging
import sys
import os
from pathlib import Path
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入项目模块
from config import config
from ui_manager import UIManager
from database import DatabaseManager, get_db
from data_processor import DataProcessor
from analyzers import AppAnalyzer, FinancialAnalyzer, VictimAnalyzer, FraudMethodAnalyzer
from visualizer import ChartVisualizer
from report_generator import HTMLReportGenerator
from pattern_detector import PatternDetector
from network_analyzer import NetworkAnalyzer
from utils import setup_logging


def initialize_system():
    """
    初始化系统组件
    
    Returns:
        tuple: (logger, db_manager, ui_manager) 系统核心组件
    """
    # 设置日志
    log_file = Path(config.paths.log_dir) / "fraud_monitor.log"
    logger = setup_logging(logging.INFO, str(log_file))
    logger.info("🚀 涉诈APP监控系统启动")
    
    try:
        # 初始化数据库管理器
        db_manager = DatabaseManager()
        
        # 检查数据库连接状态
        if db_manager.is_connected:
            logger.info("🔌 数据库管理器初始化完成")
            logger.info("✅ 数据库连接成功")
        else:
            logger.warning("⚠️ 数据库连接失败，系统将以离线模式运行")
            # 这里可以添加额外的处理逻辑，如：
            # - 使用本地缓存数据
            # - 启动连接重试机制
            # - 通知管理员
        
        # 初始化UI管理器
        ui_manager = UIManager()
        logger.info("🖥️ UI管理器初始化完成")
        
        return logger, db_manager, ui_manager
        
    except Exception as e:
        logger.error(f"❌ 系统初始化失败: {e}")
        return logger, None, None


def run_comprehensive_analysis(db_manager):
    """
    运行综合分析
    
    Args:
        db_manager: 数据库管理器实例
        
    Returns:
        dict: 分析结果
    """
    logger = logging.getLogger(__name__)
    logger.info("🔍 开始综合分析")
    
    try:
        # 获取数据
        df = db_manager.get_fraud_cases(days=config.analysis.default_days)
        if df is None or df.empty:
            logger.warning("⚠️ 未获取到案件数据")
            return {}
        
        logger.info(f"📊 获取到 {len(df)} 条案件数据")
        
        # 数据清洗
        data_processor = DataProcessor()
        cleaned_df, quality_report = data_processor.clean_dataframe(df)
        
        logger.info(f"✨ 数据清洗完成，有效记录: {len(cleaned_df)} 条，质量评分: {quality_report.quality_score:.2f}%")
        
        # 初始化分析器
        analyzers = {
            'app': AppAnalyzer(),
            'financial': FinancialAnalyzer(),
            'victim': VictimAnalyzer(),
            'fraud_method': FraudMethodAnalyzer()
        }
        
        # 执行各类分析
        analysis_results = {}
        
        for name, analyzer in analyzers.items():
            logger.info(f"📈 执行{name}分析")
            try:
                result = analyzer.analyze(cleaned_df)
                analysis_results[name] = result
                logger.info(f"✅ {name}分析完成")
            except Exception as e:
                logger.error(f"❌ {name}分析失败: {e}")
        
        # 模式检测
        logger.info("🔍 执行模式检测")
        try:
            pattern_detector = PatternDetector()
            patterns = pattern_detector.detect_all_patterns(cleaned_df)
            analysis_results['patterns'] = patterns
            logger.info(f"✅ 模式检测完成，发现 {len(patterns)} 个可疑模式")
        except Exception as e:
            logger.error(f"❌ 模式检测失败: {e}")
        
        # 网络分析
        logger.info("🕸️ 执行网络分析")
        try:
            network_analyzer = NetworkAnalyzer()
            network_result = network_analyzer.analyze(cleaned_df)
            analysis_results['network'] = network_result
            logger.info("✅ 网络分析完成")
        except Exception as e:
            logger.error(f"❌ 网络分析失败: {e}")
        
        return analysis_results
        
    except Exception as e:
        logger.error(f"❌ 综合分析过程中发生错误: {e}")
        return {}


def generate_comprehensive_report(analysis_results, output_dir=None):
    """
    生成综合报告
    
    Args:
        analysis_results (dict): 分析结果
        output_dir (str): 输出目录
        
    Returns:
        str: 报告文件路径
    """
    logger = logging.getLogger(__name__)
    logger.info("📋 开始生成综合报告")
    
    try:
        # 准备报告数据
        report_data = {
            'app_analysis': analysis_results.get('app'),
            'financial_analysis': analysis_results.get('financial'),
            'victim_analysis': analysis_results.get('victim'),
            'fraud_method_analysis': analysis_results.get('fraud_method'),
            'patterns': analysis_results.get('patterns'),
            'network_analysis': analysis_results.get('network')
        }
        
        # 生成HTML报告
        report_generator = HTMLReportGenerator(output_dir=output_dir)
        # 获取一些真实数据用于报告生成
        sample_data = pd.DataFrame({'id': [1, 2, 3], 'name': ['test1', 'test2', 'test3']})
        report_path = report_generator.generate_comprehensive_report(
            sample_data,
            analysis_results,
            analysis_results.get('patterns', [])
        )
        
        logger.info(f"✅ 综合报告生成完成: {report_path}")
        return report_path
        
    except Exception as e:
        logger.error(f"❌ 报告生成失败: {e}")
        return None


def main():
    """
    主函数 - 系统入口点
    """
    # 初始化系统
    logger, db_manager, ui_manager = initialize_system()
    
    if ui_manager is None:
        logger.error("❌ 系统初始化失败，程序退出")
        sys.exit(1)
    
    try:
        # 检查命令行参数
        if len(sys.argv) > 1:
            command = sys.argv[1].lower()
            
            if command == 'analyze':
                # 执行后台分析
                logger.info("⚙️ 执行后台分析模式")
                if db_manager and db_manager.is_connected:
                    analysis_results = run_comprehensive_analysis(db_manager)
                    
                    if analysis_results:
                        logger.info("✅ 后台分析完成")
                        # 可以在这里保存分析结果到文件或者数据库
                    else:
                        logger.warning("⚠️ 分析未产生结果")
                else:
                    logger.error("❌ 数据库未连接，无法执行分析")
                    
            elif command == 'report':
                # 生成报告
                logger.info("📋 执行报告生成模式")
                if db_manager and db_manager.is_connected:
                    analysis_results = run_comprehensive_analysis(db_manager)
                    if analysis_results:
                        report_path = generate_comprehensive_report(analysis_results)
                        if report_path:
                            logger.info(f"✅ 报告已生成: {report_path}")
                        else:
                            logger.error("❌ 报告生成失败")
                    else:
                        logger.error("❌ 无法生成报告，分析结果为空")
                else:
                    logger.error("❌ 数据库未连接，无法执行分析和生成报告")
                    
            else:
                logger.warning(f"⚠️ 未知命令: {command}")
                logger.info("ℹ️  可用命令: analyze, report")
                logger.info("ℹ️  不带参数运行将启动交互式界面")
        
        else:
            # 启动交互式UI
            logger.info("🖥️ 启动交互式用户界面")
            ui_manager.run()
            
    except KeyboardInterrupt:
        logger.info("👋 用户中断程序")
    except Exception as e:
        logger.error(f"❌ 程序运行异常: {e}")
    finally:
        # 清理资源
        if db_manager and hasattr(db_manager, 'close'):
            db_manager.close()
        logger.info("🔒 系统已关闭")


if __name__ == "__main__":
    main()