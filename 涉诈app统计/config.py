"""
涉案APP监控系统 - 配置管理
统一管理所有配置项，支持从环境变量和配置文件加载
"""
import os
from dataclasses import dataclass
from typing import Dict, Any, Optional, List
from pathlib import Path


@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = "**************"
    port: int = 3306
    username: str = "FZUser"
    password: str = "fz@20250324"
    database: str = "antiFraudPlatform"
    charset: str = "utf8mb4"
    connect_timeout: int = 10

    # 连接池配置
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 3600
    pool_pre_ping: bool = True

    @property
    def connection_url(self) -> str:
        """获取数据库连接URL"""
        return f"mysql+pymysql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}?charset={self.charset}"


@dataclass
class PathConfig:
    """路径配置"""
    base_dir: str = "/Users/<USER>/Documents/测试"
    cache_dir: str = "../cache"
    export_dir: str = "/Users/<USER>/Documents/测试"
    log_dir: str = "./logs"
    temp_dir: str = "./temp"

    def __post_init__(self):
        """确保所有目录存在"""
        for path_attr in ['base_dir', 'cache_dir', 'export_dir', 'log_dir', 'temp_dir']:
            path = Path(getattr(self, path_attr))
            path.mkdir(parents=True, exist_ok=True)


@dataclass
class CacheConfig:
    """缓存配置"""
    enabled: bool = True
    memory_cache: bool = True
    file_cache: bool = True
    expire_hours: int = 24
    max_memory_items: int = 1000

    @property
    def cache_file_name(self) -> str:
        return "app_fraud_cache.json"


@dataclass
class AnalysisConfig:
    """分析配置"""
    # 默认分析天数
    default_days: int = 30

    # 金额阈值配置
    amount_thresholds: Dict[str, int] = None

    # APP过滤配置
    excluded_app_names: tuple = None
    min_app_cases: int = 5  # APP最少案件数才进行分析

    # 批处理配置
    batch_size: int = 1000

    def __post_init__(self):
        if self.amount_thresholds is None:
            self.amount_thresholds = {
                "low": 1000,  # 1千
                "medium": 10000,  # 1万
                "high": 50000,  # 5万
                "very_high": 100000,  # 10万
                "extreme": 500000  # 50万
            }

        if self.excluded_app_names is None:
            self.excluded_app_names = (
                '', '-', '无', '韩文', '符号', '未命名', '日文',
                '不详', '？？', '未知', 'NULL', 'null'
            )


@dataclass
class RiskConfig:
    """风险评估配置"""
    # 运营商风险阈值
    operator_risk_thresholds: Dict[str, Dict[str, float]] = None

    # 预警阈值
    alert_thresholds: Dict[str, int] = None

    # 风险等级定义
    risk_levels: Dict[str, Dict[str, Any]] = None

    def __post_init__(self):
        if self.operator_risk_thresholds is None:
            self.operator_risk_thresholds = {
                '电信': {'threshold': 2.5, 'high_risk': 5, 'color': '#0769B6'},
                '联通': {'threshold': 2.0, 'high_risk': 4, 'color': '#E60012'},
                '移动': {'threshold': 3.0, 'high_risk': 6, 'color': '#00A0E9'}
            }

        if self.alert_thresholds is None:
            self.alert_thresholds = {
                "daily_cases_spike": 10,  # 日案件数激增阈值
                "amount_anomaly": 100000,  # 金额异常阈值
                "new_app_cases": 5,  # 新APP案件数阈值
                "geographic_concentration": 8  # 地理集中度阈值
            }

        if self.risk_levels is None:
            self.risk_levels = {
                "极高": {"score_min": 80, "color": "#DC143C", "emoji": "🔴"},
                "高": {"score_min": 60, "color": "#FF4500", "emoji": "🟠"},
                "中": {"score_min": 40, "color": "#FFA500", "emoji": "🟡"},
                "低": {"score_min": 20, "color": "#32CD32", "emoji": "🟢"},
                "极低": {"score_min": 0, "color": "#90EE90", "emoji": "⚪"}
            }


@dataclass
class VisualizationConfig:
    """可视化配置"""
    # 图表样式
    style: str = "seaborn-v0_8"
    figsize: tuple = (15, 8)
    dpi: int = 300

    # 颜色配置
    colors: Dict[str, str] = None

    # 调色板（用于多系列图表）
    color_palette: List[str] = None

    # 字体配置
    font_family: str = "Microsoft YaHei"
    font_size: int = 12

    def __post_init__(self):
        if self.colors is None:
            self.colors = {
                'primary': '#0769B6',
                'danger': '#E34234',
                'warning': '#F0BE38',
                'success': '#28A745',
                'neutral': '#6C757D',
                'background': '#F8F9FA',
                'grid': '#E9ECEF',
                'text': '#2C3E50'
            }

        if self.color_palette is None:
            self.color_palette = [
                '#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#5C946E',
                '#8B9DC3', '#F0BE38', '#7B68EE', '#20B2AA', '#FF6347',
                '#32CD32', '#FFD700', '#FF69B4', '#1E90FF', '#FF4500',
                '#9370DB', '#00CED1', '#FFA500', '#DC143C', '#00FA9A',
                '#4169E1', '#FF1493', '#00BFFF', '#FF6B6B', '#4ECDC4',
                '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'
            ]


@dataclass
class ReportConfig:
    """报告配置"""
    # HTML报告配置
    html_template: str = "risk_report_template.html"
    include_charts: bool = True
    include_raw_data: bool = False

    # 报告内容配置
    max_app_display: int = 20
    max_case_samples: int = 100
    include_recommendations: bool = True

    # 导出格式
    export_formats: List[str] = None

    def __post_init__(self):
        if self.export_formats is None:
            self.export_formats = ['html', 'pdf']


@dataclass
class UIConfig:
    """用户界面配置"""
    # 菜单显示配置
    menu_width: int = 60
    progress_bar_width: int = 50

    # 显示限制
    max_display_items: int = 20
    page_size: int = 10

    # 交互配置
    auto_refresh: bool = False
    confirm_dangerous_actions: bool = True

    # 颜色和符号
    menu_symbols: Dict[str, str] = None

    def __post_init__(self):
        if self.menu_symbols is None:
            self.menu_symbols = {
                'main_title': '🎯',
                'analysis': '📊',
                'visualization': '📈',
                'report': '📋',
                'detection': '🔍',
                'network': '🕸️',
                'settings': '⚙️',
                'cache': '🗑️',
                'exit': '❌',
                'back': '🔙',
                'success': '✅',
                'error': '❌',
                'warning': '⚠️',
                'info': 'ℹ️'
            }


class Config:
    """主配置类"""

    def __init__(self, config_file: Optional[str] = None):
        self.db = DatabaseConfig()
        self.paths = PathConfig()
        self.cache = CacheConfig()
        self.analysis = AnalysisConfig()
        self.risk = RiskConfig()
        self.visualization = VisualizationConfig()
        self.report = ReportConfig()
        self.ui = UIConfig()

        # 如果提供了配置文件，则加载配置
        if config_file and os.path.exists(config_file):
            self._load_from_file(config_file)

        # 从环境变量加载配置
        self._load_from_env()

    def _load_from_env(self):
        """从环境变量加载配置"""
        # 数据库配置
        if os.getenv("DB_HOST"):
            self.db.host = os.getenv("DB_HOST")
        if os.getenv("DB_PORT"):
            self.db.port = int(os.getenv("DB_PORT"))
        if os.getenv("DB_USERNAME"):
            self.db.username = os.getenv("DB_USERNAME")
        if os.getenv("DB_PASSWORD"):
            self.db.password = os.getenv("DB_PASSWORD")
        if os.getenv("DB_DATABASE"):
            self.db.database = os.getenv("DB_DATABASE")

        # 路径配置
        if os.getenv("EXPORT_DIR"):
            self.paths.export_dir = os.getenv("EXPORT_DIR")
        if os.getenv("CACHE_DIR"):
            self.paths.cache_dir = os.getenv("CACHE_DIR")

        # 分析配置
        if os.getenv("DEFAULT_ANALYSIS_DAYS"):
            self.analysis.default_days = int(os.getenv("DEFAULT_ANALYSIS_DAYS"))

    def _load_from_file(self, config_file: str):
        """从配置文件加载配置"""
        # TODO: 实现从YAML/JSON文件加载配置
        pass

    def get_cache_file_path(self) -> Path:
        """获取缓存文件完整路径"""
        return Path(self.paths.cache_dir) / self.cache.cache_file_name

    def get_export_path(self, filename: str) -> Path:
        """获取导出文件完整路径"""
        return Path(self.paths.export_dir) / filename

    def get_risk_level(self, score: int) -> Dict[str, Any]:
        """根据分数获取风险等级"""
        for level, config in self.risk.risk_levels.items():
            if score >= config["score_min"]:
                return {"level": level, **config}
        return {"level": "未知", "color": "#808080", "emoji": "❓"}

    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典"""
        return {
            "database": {
                "host": self.db.host,
                "port": self.db.port,
                "username": self.db.username,
                "database": self.db.database
            },
            "paths": {
                "export_dir": self.paths.export_dir,
                "cache_dir": self.paths.cache_dir
            },
            "analysis": {
                "default_days": self.analysis.default_days,
                "amount_thresholds": self.analysis.amount_thresholds
            },
            "risk": {
                "operator_thresholds": self.risk.operator_risk_thresholds,
                "alert_thresholds": self.risk.alert_thresholds
            }
        }


# 全局配置实例
config = Config()