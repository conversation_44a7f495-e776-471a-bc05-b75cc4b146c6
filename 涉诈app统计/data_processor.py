"""
涉案APP监控系统 - 数据处理模块
负责数据清洗、转换、验证和预处理
"""
import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
import re
from dataclasses import dataclass

from config import config

logger = logging.getLogger(__name__)


@dataclass
class DataQualityReport:
    """数据质量报告"""
    total_records: int = 0
    valid_records: int = 0
    invalid_records: int = 0
    missing_values: Dict[str, int] = None
    duplicate_records: int = 0
    data_types: Dict[str, str] = None
    quality_score: float = 0.0
    issues: List[str] = None

    def __post_init__(self):
        if self.missing_values is None:
            self.missing_values = {}
        if self.data_types is None:
            self.data_types = {}
        if self.issues is None:
            self.issues = []


class DataProcessor:
    """数据处理器 - 负责所有数据清洗和转换操作"""

    def __init__(self):
        self.config = config
        self.logger = logging.getLogger(__name__)

        # 预编译正则表达式
        self._phone_pattern = re.compile(r'1[3-9]\d{9}')
        self._amount_pattern = re.compile(r'[\d,]+\.?\d*')
        self._app_name_clean_pattern = re.compile(r'[^\w\u4e00-\u9fff\s]')

    def clean_dataframe(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, DataQualityReport]:
        """
        清洗DataFrame数据

        Args:
            df: 原始数据DataFrame

        Returns:
            Tuple[pd.DataFrame, DataQualityReport]: 清洗后的数据和质量报告
        """
        self.logger.info(f"开始清洗数据，原始记录数: {len(df)}")

        if df is None or df.empty:
            self.logger.warning("输入数据为空")
            return pd.DataFrame(), DataQualityReport()

        # 创建副本避免修改原数据
        cleaned_df = df.copy()
        original_count = len(cleaned_df)

        # 生成数据质量报告
        quality_report = self._generate_quality_report(cleaned_df)

        # 1. 清理列名
        cleaned_df = self._clean_column_names(cleaned_df)

        # 2. 处理缺失值
        cleaned_df = self._handle_missing_values(cleaned_df)

        # 3. 清理APP名称
        if 'final_app_name' in cleaned_df.columns:
            cleaned_df = self._clean_app_names(cleaned_df)

        # 4. 标准化日期格式
        cleaned_df = self._standardize_dates(cleaned_df)

        # 5. 清理金额数据
        if 'involved_amount' in cleaned_df.columns:
            cleaned_df = self._clean_amounts(cleaned_df)

        # 6. 清理手机号数据
        cleaned_df = self._clean_phone_numbers(cleaned_df)

        # 7. 去重
        cleaned_df = self._remove_duplicates(cleaned_df)

        # 8. 数据类型转换
        cleaned_df = self._convert_data_types(cleaned_df)

        # 9. 过滤无效记录
        cleaned_df = self._filter_invalid_records(cleaned_df)

        # 更新质量报告
        quality_report.valid_records = len(cleaned_df)
        quality_report.invalid_records = original_count - len(cleaned_df)
        quality_report.quality_score = (len(cleaned_df) / original_count * 100) if original_count > 0 else 0

        self.logger.info(f"数据清洗完成，有效记录数: {len(cleaned_df)}, 质量分数: {quality_report.quality_score:.2f}%")

        return cleaned_df, quality_report

    def _generate_quality_report(self, df: pd.DataFrame) -> DataQualityReport:
        """生成数据质量报告"""
        report = DataQualityReport()
        report.total_records = len(df)

        # 统计缺失值
        report.missing_values = df.isnull().sum().to_dict()

        # 统计重复记录
        report.duplicate_records = df.duplicated().sum()

        # 记录数据类型
        report.data_types = df.dtypes.astype(str).to_dict()

        # 记录数据质量问题
        issues = []
        for col, missing_count in report.missing_values.items():
            if missing_count > 0:
                missing_percent = missing_count / report.total_records * 100
                issues.append(f"列 '{col}' 缺失 {missing_count} 条记录 ({missing_percent:.1f}%)")

        if report.duplicate_records > 0:
            issues.append(f"发现 {report.duplicate_records} 条重复记录")

        report.issues = issues

        return report

    def _clean_column_names(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理列名"""
        # 去除列名中的空格和特殊字符
        df.columns = df.columns.str.strip().str.replace(' ', '_')
        return df

    def _handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理缺失值"""
        # 对于字符串列，用空字符串填充
        string_columns = df.select_dtypes(include=['object']).columns
        df[string_columns] = df[string_columns].fillna('')

        # 对于数值列，用0填充
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        df[numeric_columns] = df[numeric_columns].fillna(0)

        return df

    def _clean_app_names(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理APP名称"""
        if 'final_app_name' not in df.columns:
            return df

        # 转换为字符串
        df['final_app_name'] = df['final_app_name'].astype(str)

        # 去除前后空格
        df['final_app_name'] = df['final_app_name'].str.strip()

        # 替换无效的APP名称
        invalid_names = self.config.analysis.excluded_app_names
        df.loc[df['final_app_name'].isin(invalid_names), 'final_app_name'] = '未知应用'

        # 清理特殊字符（保留中文、英文、数字）
        df['final_app_name'] = df['final_app_name'].apply(
            lambda x: re.sub(r'[^\w\u4e00-\u9fff\s]', '', str(x)) if pd.notna(x) else '未知应用'
        )

        # 处理过长的APP名称
        df['final_app_name'] = df['final_app_name'].apply(
            lambda x: x[:50] + '...' if len(str(x)) > 50 else x
        )

        return df

    def _standardize_dates(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化日期格式"""
        date_columns = ['insert_day', 'case_date', 'report_date', '日期']

        for col in date_columns:
            if col in df.columns:
                try:
                    # 尝试转换为datetime
                    df[col] = pd.to_datetime(df[col], errors='coerce')

                    # 处理异常日期（如未来日期）
                    future_mask = df[col] > datetime.now()
                    if future_mask.any():
                        self.logger.warning(f"发现 {future_mask.sum()} 个未来日期，已设置为当前日期")
                        df.loc[future_mask, col] = datetime.now()

                    # 处理过早日期（如1900年之前）
                    early_mask = df[col] < datetime(1900, 1, 1)
                    if early_mask.any():
                        self.logger.warning(f"发现 {early_mask.sum()} 个异常早期日期，已设置为空值")
                        df.loc[early_mask, col] = pd.NaT

                except Exception as e:
                    self.logger.error(f"日期列 {col} 转换失败: {e}")

        return df

    def _clean_amounts(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理金额数据"""
        if 'involved_amount' not in df.columns:
            return df

        # 转换为字符串处理
        df['involved_amount'] = df['involved_amount'].astype(str)

        # 提取数字（去除逗号等格式字符）
        df['involved_amount'] = df['involved_amount'].str.replace(',', '').str.replace('，', '')

        # 转换为数值类型
        df['involved_amount'] = pd.to_numeric(df['involved_amount'], errors='coerce')

        # 处理异常金额
        df['involved_amount'] = df['involved_amount'].fillna(0)

        # 处理负数金额
        negative_mask = df['involved_amount'] < 0
        if negative_mask.any():
            self.logger.warning(f"发现 {negative_mask.sum()} 个负数金额，已转换为绝对值")
            df.loc[negative_mask, 'involved_amount'] = df.loc[negative_mask, 'involved_amount'].abs()

        # 处理异常大金额（超过10亿）
        large_mask = df['involved_amount'] > 1000000000
        if large_mask.any():
            self.logger.warning(f"发现 {large_mask.sum()} 个异常大金额，已设置上限")
            df.loc[large_mask, 'involved_amount'] = 1000000000

        return df

    def _clean_phone_numbers(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理手机号数据"""
        phone_columns = ['suspect_phone', 'victim_phone', 'phone_number']

        for col in phone_columns:
            if col in df.columns:
                # 提取有效手机号
                df[col] = df[col].astype(str).apply(self._extract_phone_number)

        return df

    def _extract_phone_number(self, phone_str: str) -> str:
        """提取有效手机号"""
        if pd.isna(phone_str) or phone_str == 'nan':
            return ''

        # 使用正则表达式提取11位手机号
        matches = self._phone_pattern.findall(str(phone_str))
        return matches[0] if matches else ''

    def _remove_duplicates(self, df: pd.DataFrame) -> pd.DataFrame:
        """去除重复记录"""
        original_count = len(df)

        # 基于关键字段去重
        key_columns = []
        for col in ['case_number', 'final_app_name', 'insert_day', 'involved_amount']:
            if col in df.columns:
                key_columns.append(col)

        if key_columns:
            df = df.drop_duplicates(subset=key_columns, keep='first')
            removed_count = original_count - len(df)
            if removed_count > 0:
                self.logger.info(f"去除重复记录: {removed_count} 条")

        return df

    def _convert_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """转换数据类型"""
        # 数值列转换
        numeric_columns = ['involved_amount', 'case_count', 'victim_count']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)

        # 分类列转换
        category_columns = ['final_app_name', 'fraud_type', 'operator']
        for col in category_columns:
            if col in df.columns and df[col].nunique() < len(df) * 0.5:
                df[col] = df[col].astype('category')

        return df

    def _filter_invalid_records(self, df: pd.DataFrame) -> pd.DataFrame:
        """过滤无效记录"""
        original_count = len(df)

        # 过滤条件
        filters = []

        # 1. APP名称不能为空或无效
        if 'final_app_name' in df.columns:
            valid_app_mask = ~df['final_app_name'].isin(['', '未知应用', 'nan', None])
            filters.append(valid_app_mask)

        # 2. 金额必须大于0（如果存在金额列）
        if 'involved_amount' in df.columns:
            valid_amount_mask = df['involved_amount'] > 0
            filters.append(valid_amount_mask)

        # 3. 日期必须有效
        date_columns = ['insert_day', 'case_date']
        for col in date_columns:
            if col in df.columns:
                valid_date_mask = df[col].notna()
                filters.append(valid_date_mask)
                break  # 只需要一个有效日期列

        # 应用所有过滤条件
        if filters:
            combined_filter = filters[0]
            for f in filters[1:]:
                combined_filter = combined_filter & f

            df = df[combined_filter]
            filtered_count = original_count - len(df)
            if filtered_count > 0:
                self.logger.info(f"过滤无效记录: {filtered_count} 条")

        return df

    def add_derived_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加派生列"""
        if df.empty:
            return df

        # 1. 添加金额等级
        if 'involved_amount' in df.columns:
            df['amount_level'] = self._categorize_amount(df['involved_amount'])

        # 2. 添加时间相关列
        if 'insert_day' in df.columns:
            df['year'] = df['insert_day'].dt.year
            df['month'] = df['insert_day'].dt.month
            df['day'] = df['insert_day'].dt.day
            df['weekday'] = df['insert_day'].dt.dayofweek
            df['is_weekend'] = df['weekday'].isin([5, 6])

        # 3. 添加APP风险等级（基于历史数据）
        if 'final_app_name' in df.columns:
            df['app_risk_level'] = self._calculate_app_risk_level(df)

        # 4. 添加运营商信息（如果有手机号）
        phone_columns = ['suspect_phone', 'victim_phone']
        for col in phone_columns:
            if col in df.columns:
                operator_col = f'{col}_operator'
                df[operator_col] = df[col].apply(self._get_phone_operator)

        return df

    def _categorize_amount(self, amounts: pd.Series) -> pd.Series:
        """金额分类"""
        thresholds = self.config.analysis.amount_thresholds

        conditions = [
            amounts >= thresholds['extreme'],
            amounts >= thresholds['very_high'],
            amounts >= thresholds['high'],
            amounts >= thresholds['medium'],
            amounts >= thresholds['low']
        ]

        choices = ['极高金额', '很高金额', '高金额', '中等金额', '低金额']

        return pd.Series(np.select(conditions, choices, default='微小金额'), index=amounts.index)

    def _calculate_app_risk_level(self, df: pd.DataFrame) -> pd.Series:
        """计算APP风险等级"""
        if 'final_app_name' not in df.columns:
            return pd.Series(['未知'] * len(df), index=df.index)

        # 基于APP的案件数和平均金额计算风险等级
        app_stats = df.groupby('final_app_name').agg({
            'involved_amount': ['count', 'mean', 'sum'] if 'involved_amount' in df.columns else ['count']
        }).round(2)

        if 'involved_amount' in df.columns:
            app_stats.columns = ['case_count', 'avg_amount', 'total_amount']

            # 风险评分计算
            app_stats['risk_score'] = (
                    app_stats['case_count'] * 0.4 +
                    (app_stats['avg_amount'] / 10000) * 0.3 +
                    (app_stats['total_amount'] / 100000) * 0.3
            )
        else:
            app_stats.columns = ['case_count']
            app_stats['risk_score'] = app_stats['case_count'] * 0.5

        # 风险等级分类
        risk_levels = pd.cut(
            app_stats['risk_score'],
            bins=[0, 2, 5, 10, 20, float('inf')],
            labels=['低风险', '中低风险', '中风险', '高风险', '极高风险']
        )

        # 映射回原DataFrame
        risk_mapping = risk_levels.to_dict()
        return df['final_app_name'].map(risk_mapping).fillna('未知')

    def _get_phone_operator(self, phone: str) -> str:
        """根据手机号判断运营商"""
        if not phone or len(phone) != 11:
            return '未知'

        prefix = phone[:3]

        # 电信号段
        telecom_prefixes = ['133', '149', '153', '173', '177', '180', '181', '189', '191', '193', '199']
        if prefix in telecom_prefixes:
            return '电信'

        # 联通号段
        unicom_prefixes = ['130', '131', '132', '145', '155', '156', '166', '171', '175', '176', '185', '186']
        if prefix in unicom_prefixes:
            return '联通'

        # 移动号段
        mobile_prefixes = ['134', '135', '136', '137', '138', '139', '147', '150', '151', '152', '157', '158', '159',
                           '172', '178', '182', '183', '184', '187', '188', '198']
        if prefix in mobile_prefixes:
            return '移动'

        # 广电号段
        if prefix in ['192']:
            return '广电'

        return '未知'

    def validate_data_integrity(self, df: pd.DataFrame) -> Dict[str, Any]:
        """验证数据完整性"""
        validation_results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'statistics': {}
        }

        if df.empty:
            validation_results['is_valid'] = False
            validation_results['errors'].append('数据集为空')
            return validation_results

        # 检查必需列
        required_columns = ['final_app_name']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            validation_results['is_valid'] = False
            validation_results['errors'].append(f'缺少必需列: {missing_columns}')

        # 检查数据质量
        if 'final_app_name' in df.columns:
            empty_app_count = df['final_app_name'].isin(['', '未知应用', None]).sum()
            if empty_app_count > len(df) * 0.1:  # 超过10%的记录APP名称为空
                validation_results['warnings'].append(f'APP名称为空的记录过多: {empty_app_count}')

        if 'involved_amount' in df.columns:
            zero_amount_count = (df['involved_amount'] == 0).sum()
            if zero_amount_count > len(df) * 0.2:  # 超过20%的记录金额为0
                validation_results['warnings'].append(f'金额为0的记录过多: {zero_amount_count}')

        # 统计信息
        validation_results['statistics'] = {
            'total_records': len(df),
            'columns': list(df.columns),
            'memory_usage': df.memory_usage(deep=True).sum(),
            'data_types': df.dtypes.to_dict()
        }

        return validation_results

    def prepare_for_analysis(self, df: pd.DataFrame, analysis_type: str = 'comprehensive') -> pd.DataFrame:
        """为特定分析准备数据"""
        if df.empty:
            return df

        prepared_df = df.copy()

        if analysis_type == 'financial':
            # 金融分析：确保金额数据有效
            if 'involved_amount' in prepared_df.columns:
                prepared_df = prepared_df[prepared_df['involved_amount'] > 0]

        elif analysis_type == 'app':
            # APP分析：确保APP名称有效
            if 'final_app_name' in prepared_df.columns:
                prepared_df = prepared_df[
                    ~prepared_df['final_app_name'].isin(['', '未知应用', None])
                ]

        elif analysis_type == 'temporal':
            # 时间分析：确保日期数据有效
            date_columns = ['insert_day', 'case_date']
            for col in date_columns:
                if col in prepared_df.columns:
                    prepared_df = prepared_df[prepared_df[col].notna()]
                    break

        elif analysis_type == 'network':
            # 网络分析：确保关键字段存在
            required_fields = ['final_app_name']
            for field in required_fields:
                if field in prepared_df.columns:
                    prepared_df = prepared_df[prepared_df[field].notna()]

        return prepared_df