"""
涉案APP监控系统 - 数据库管理
提供统一的数据库连接、查询和缓存管理
"""
import json
import logging
import pandas as pd
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from contextlib import contextmanager
from urllib.parse import quote_plus

from sqlalchemy import create_engine, text
from sqlalchemy.engine import Engine
from sqlalchemy.exc import SQLAlchemyError, DisconnectionError
from sqlalchemy.pool import QueuePool

from config import config

# 配置日志
logger = logging.getLogger(__name__)


class CacheManager:
    """缓存管理器"""

    def __init__(self):
        self._memory_cache: Dict[str, Dict[str, Any]] = {}
        self._cache_lock = threading.Lock()
        self.cache_file = config.get_cache_file_path()
        self._load_file_cache()

    def _load_file_cache(self):
        """从文件加载缓存"""
        if not config.cache.file_cache:
            return

        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    file_cache = json.load(f)

                # 检查缓存是否过期
                current_time = datetime.now()
                for key, cache_data in file_cache.items():
                    cache_time = datetime.fromisoformat(cache_data.get('timestamp', '1970-01-01'))
                    if current_time - cache_time < timedelta(hours=config.cache.expire_hours):
                        with self._cache_lock:
                            self._memory_cache[key] = cache_data

                logger.info(f"从文件加载了 {len(self._memory_cache)} 个缓存项")
        except Exception as e:
            logger.warning(f"加载文件缓存失败: {e}")

    def _save_file_cache(self):
        """保存缓存到文件"""
        if not config.cache.file_cache:
            return

        try:
            with self._cache_lock:
                cache_to_save = dict(self._memory_cache)

            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_to_save, f, ensure_ascii=False, indent=2, default=str)

        except Exception as e:
            logger.warning(f"保存文件缓存失败: {e}")

    def get(self, key: str) -> Optional[pd.DataFrame]:
        """获取缓存数据"""
        if not config.cache.enabled:
            return None

        with self._cache_lock:
            cache_data = self._memory_cache.get(key)

        if cache_data is None:
            return None

        # 检查是否过期
        cache_time = datetime.fromisoformat(cache_data['timestamp'])
        if datetime.now() - cache_time > timedelta(hours=config.cache.expire_hours):
            self.remove(key)
            return None

        try:
            # 从JSON恢复DataFrame
            df = pd.read_json(cache_data['data'], orient='records')
            logger.debug(f"从缓存获取数据: {key}, 记录数: {len(df)}")
            return df
        except Exception as e:
            logger.warning(f"缓存数据解析失败: {e}")
            self.remove(key)
            return None

    def set(self, key: str, data: pd.DataFrame):
        """设置缓存数据"""
        if not config.cache.enabled or data is None or data.empty:
            return

        try:
            cache_data = {
                'timestamp': datetime.now().isoformat(),
                'data': data.to_json(orient='records', date_format='iso'),
                'rows': len(data),
                'columns': list(data.columns)
            }

            with self._cache_lock:
                self._memory_cache[key] = cache_data

                # 检查内存缓存大小限制
                if len(self._memory_cache) > config.cache.max_memory_items:
                    # 删除最旧的缓存项
                    oldest_key = min(self._memory_cache.keys(),
                                     key=lambda k: self._memory_cache[k]['timestamp'])
                    del self._memory_cache[oldest_key]

            logger.debug(f"缓存数据: {key}, 记录数: {len(data)}")

            # 异步保存到文件
            if config.cache.file_cache:
                threading.Thread(target=self._save_file_cache, daemon=True).start()

        except Exception as e:
            logger.warning(f"设置缓存失败: {e}")

    def remove(self, key: str):
        """删除缓存项"""
        with self._cache_lock:
            self._memory_cache.pop(key, None)

    def clear(self):
        """清空所有缓存"""
        with self._cache_lock:
            self._memory_cache.clear()

        if self.cache_file.exists():
            try:
                self.cache_file.unlink()
                logger.info("文件缓存已清除")
            except Exception as e:
                logger.warning(f"清除文件缓存失败: {e}")

    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        with self._cache_lock:
            cache_keys = list(self._memory_cache.keys())
            total_rows = sum(item.get('rows', 0) for item in self._memory_cache.values())

        return {
            'enabled': config.cache.enabled,
            'memory_items': len(cache_keys),
            'total_rows': total_rows,
            'file_exists': self.cache_file.exists(),
            'file_size': self.cache_file.stat().st_size if self.cache_file.exists() else 0,
            'keys': cache_keys
        }


class DatabaseManager:
    """数据库管理器"""

    def __init__(self):
        self.engine: Optional[Engine] = None
        self.cache_manager = CacheManager()
        self.is_connected = False
        self._connection_lock = threading.Lock()
        self._initialize_engine()

    def _initialize_engine(self):
        """初始化数据库引擎"""
        try:
            # URL编码密码中的特殊字符
            encoded_password = quote_plus(config.db.password)

            connection_string = (
                f"mysql+pymysql://{config.db.username}:{encoded_password}@"
                f"{config.db.host}:{config.db.port}/{config.db.database}"
                f"?charset={config.db.charset}"
            )

            # 创建引擎
            self.engine = create_engine(
                connection_string,
                poolclass=QueuePool,
                pool_size=config.db.pool_size,
                max_overflow=config.db.max_overflow,
                pool_timeout=config.db.pool_timeout,
                pool_recycle=config.db.pool_recycle,
                pool_pre_ping=config.db.pool_pre_ping,
                connect_args={'connect_timeout': config.db.connect_timeout},
                echo=False
            )

            # 测试连接
            self._test_connection()
            self.is_connected = True
            logger.info("数据库引擎初始化成功")

        except Exception as e:
            logger.error(f"数据库引擎初始化失败: {e}")
            self.is_connected = False
            # 不抛出异常，允许系统在无数据库连接的情况下运行
            # raise

    def _test_connection(self):
        """测试数据库连接"""
        if self.engine:
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))

    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        if not self.engine:
            raise RuntimeError("数据库引擎未初始化")

        connection = None
        try:
            connection = self.engine.connect()
            yield connection
        except DisconnectionError:
            logger.warning("数据库连接断开，尝试重新连接...")
            self._initialize_engine()
            if self.engine:
                connection = self.engine.connect()
                yield connection
            else:
                raise
        except Exception as e:
            logger.error(f"数据库连接错误: {e}")
            if connection:
                connection.rollback()
            raise
        finally:
            if connection:
                connection.close()

    def execute_query(self, sql: str, params: Optional[Dict[str, Any]] = None,
                      use_cache: bool = True) -> pd.DataFrame:
        """
        执行查询并返回DataFrame

        Args:
            sql: SQL查询语句
            params: 查询参数
            use_cache: 是否使用缓存

        Returns:
            pd.DataFrame: 查询结果
        """
        if not sql or not sql.strip():
            logger.warning("SQL语句为空")
            return pd.DataFrame()

        # 生成缓存键
        cache_key = self._generate_cache_key(sql, params)

        # 尝试从缓存获取
        if use_cache and config.cache.enabled:
            cached_data = self.cache_manager.get(cache_key)
            if cached_data is not None:
                return cached_data

        # 如果数据库未连接，直接返回空DataFrame
        if not self.is_connected or not self.engine:
            logger.warning("数据库未连接，无法执行查询")
            return pd.DataFrame()

        try:
            with self.get_connection() as connection:
                # 使用text()包装SQL语句以正确处理参数
                df = pd.read_sql(text(sql), connection, params=params or {})

                # 缓存结果
                if use_cache and config.cache.enabled and not df.empty:
                    self.cache_manager.set(cache_key, df)

                logger.debug(f"查询成功，返回 {len(df)} 条记录")
                return df

        except SQLAlchemyError as e:
            logger.error(f"数据库查询错误: {e}")
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"查询过程中发生未知错误: {e}")
            return pd.DataFrame()

    def execute_queries_batch(self, queries: List[str],
                              params_list: Optional[List[Dict[str, Any]]] = None,
                              use_cache: bool = True) -> List[pd.DataFrame]:
        """
        批量执行查询

        Args:
            queries: SQL查询语句列表
            params_list: 参数列表
            use_cache: 是否使用缓存

        Returns:
            List[pd.DataFrame]: 查询结果列表
        """
        if not queries:
            return []

        if params_list and len(params_list) != len(queries):
            raise ValueError("参数列表长度与查询列表长度不匹配")

        results = []
        for i, query in enumerate(queries):
            params = params_list[i] if params_list else None
            result = self.execute_query(query, params, use_cache)
            results.append(result)

        return results

    def _generate_cache_key(self, sql: str, params: Optional[Dict[str, Any]] = None) -> str:
        """生成缓存键"""
        import hashlib

        # 标准化SQL（去除多余空格和换行）
        normalized_sql = ' '.join(sql.split())

        # 包含参数的字符串
        params_str = str(sorted((params or {}).items()))

        # 生成MD5哈希
        cache_string = f"{normalized_sql}|{params_str}"
        return hashlib.md5(cache_string.encode('utf-8')).hexdigest()

    def get_fraud_cases(self, days: int = 30, use_cache: bool = True) -> pd.DataFrame:
        """
        获取指定天数内的诈骗案件数据

        Args:
            days: 查询天数
            use_cache: 是否使用缓存

        Returns:
            pd.DataFrame: 案件数据
        """
        sql = """
        SELECT
            id, case_number, app_name as final_app_name, involved_amount,
            victim_age,
            case_main_type, case_sub_type, insert_day,
            occurrence_area, brief_case_description
        FROM anti_fraud_case_new
        WHERE insert_day >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
        AND app_name IS NOT NULL
        AND app_name != ''
        ORDER BY insert_day DESC
        """

        return self.execute_query(sql, {'days': days}, use_cache)

    def get_app_statistics(self, days: int = 30, use_cache: bool = True) -> pd.DataFrame:
        """
        获取APP统计数据

        Args:
            days: 查询天数
            use_cache: 是否使用缓存

        Returns:
            pd.DataFrame: APP统计数据
        """
        sql = """
        SELECT 
            app_name as final_app_name,
            COUNT(*) as case_count,
            SUM(involved_amount) as total_amount,
            AVG(involved_amount) as avg_amount,
            MAX(involved_amount) as max_amount,
            MIN(insert_day) as first_case_date,
            MAX(insert_day) as last_case_date,
            COUNT(DISTINCT victim_province) as province_count,
            COUNT(DISTINCT victim_city) as city_count
        FROM anti_fraud_case_new 
        WHERE insert_day >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
        AND app_name IS NOT NULL 
        AND app_name != ''
        GROUP BY app_name
        HAVING case_count >= :min_cases
        ORDER BY case_count DESC, total_amount DESC
        """

        params = {'days': days, 'min_cases': config.analysis.min_app_cases}
        return self.execute_query(sql, params, use_cache)

    def get_daily_trends(self, days: int = 30, use_cache: bool = True) -> pd.DataFrame:
        """
        获取每日趋势数据

        Args:
            days: 查询天数
            use_cache: 是否使用缓存

        Returns:
            pd.DataFrame: 每日趋势数据
        """
        sql = """
        SELECT 
            insert_day,
            COUNT(*) as daily_cases,
            SUM(involved_amount) as daily_amount,
            COUNT(DISTINCT app_name) as daily_apps,
            COUNT(DISTINCT victim_province) as daily_provinces
        FROM anti_fraud_case_new 
        WHERE insert_day >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
        AND app_name IS NOT NULL 
        AND app_name != ''
        GROUP BY insert_day
        ORDER BY insert_day
        """

        return self.execute_query(sql, {'days': days}, use_cache)

    def get_operator_statistics(self, days: int = 30, use_cache: bool = True) -> pd.DataFrame:
        """
        获取运营商统计数据

        Args:
            days: 查询天数
            use_cache: 是否使用缓存

        Returns:
            pd.DataFrame: 运营商统计数据
        """
        sql = """
        SELECT 
            operator_type,
            COUNT(*) as case_count,
            SUM(involved_amount) as total_amount,
            AVG(involved_amount) as avg_amount,
            COUNT(DISTINCT app_name) as app_count,
            COUNT(DISTINCT victim_province) as province_count
        FROM anti_fraud_case_new 
        WHERE insert_day >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
        AND app_name IS NOT NULL 
        AND app_name != ''
        AND operator_type IS NOT NULL
        GROUP BY operator_type
        ORDER BY case_count DESC
        """

        return self.execute_query(sql, {'days': days}, use_cache)

    def test_connection(self) -> bool:
        """
        测试数据库连接

        Returns:
            bool: 连接是否成功
        """
        try:
            self._test_connection()
            logger.info("数据库连接测试成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            return False

    def get_table_info(self, table_name: str = "anti_fraud_case_new") -> pd.DataFrame:
        """
        获取表结构信息

        Args:
            table_name: 表名

        Returns:
            pd.DataFrame: 表结构信息
        """
        sql = f"DESCRIBE {table_name}"
        return self.execute_query(sql, use_cache=False)

    def get_database_stats(self) -> Dict[str, Any]:
        """
        获取数据库统计信息

        Returns:
            Dict[str, Any]: 数据库统计信息
        """
        try:
            # 获取表记录数
            total_cases_df = self.execute_query(
                "SELECT COUNT(*) as total FROM anti_fraud_case_new",
                use_cache=False
            )
            total_cases = total_cases_df.iloc[0]['total'] if not total_cases_df.empty else 0

            # 获取最新记录时间
            latest_df = self.execute_query(
                "SELECT MAX(insert_day) as latest_date FROM anti_fraud_case_new",
                use_cache=False
            )
            latest_date = latest_df.iloc[0]['latest_date'] if not latest_df.empty else None

            # 获取缓存信息
            cache_info = self.cache_manager.get_cache_info()

            return {
                'connected': self.is_connected,
                'total_cases': total_cases,
                'latest_date': latest_date,
                'cache_info': cache_info,
                'engine_info': {
                    'pool_size': config.db.pool_size,
                    'max_overflow': config.db.max_overflow,
                    'pool_timeout': config.db.pool_timeout
                }
            }

        except Exception as e:
            logger.error(f"获取数据库统计信息失败: {e}")
            return {'connected': False, 'error': str(e)}

    def clear_cache(self):
        """清空缓存"""
        self.cache_manager.clear()
        logger.info("数据库缓存已清空")

    def close(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()
            self.is_connected = False
            logger.info("数据库连接池已关闭")


# 全局数据库管理器实例
db_manager = DatabaseManager()


# 便捷函数
def get_db() -> DatabaseManager:
    """获取数据库管理器实例"""
    return db_manager


def execute_query(sql: str, params: Optional[Dict[str, Any]] = None,
                  use_cache: bool = True) -> pd.DataFrame:
    """执行查询的便捷函数"""
    return db_manager.execute_query(sql, params, use_cache)


def get_fraud_cases(days: int = 30, use_cache: bool = True) -> pd.DataFrame:
    """获取诈骗案件数据的便捷函数"""
    return db_manager.get_fraud_cases(days, use_cache)


def test_database_connection() -> bool:
    """测试数据库连接的便捷函数"""
    return db_manager.test_connection()


if __name__ == "__main__":
    # 测试数据库连接和功能
    print("🔌 测试数据库连接...")

    if test_database_connection():
        print("✅ 数据库连接成功")

        # 获取数据库统计信息
        stats = db_manager.get_database_stats()
        print(f"📊 数据库统计信息:")
        print(f"   总案件数: {stats.get('total_cases', 0):,}")
        print(f"   最新数据: {stats.get('latest_date', 'N/A')}")
        print(f"   缓存项数: {stats.get('cache_info', {}).get('memory_items', 0)}")

        # 测试查询
        print("\n🔍 测试数据查询...")
        df = get_fraud_cases(days=7, use_cache=True)
        print(f"   近7天案件数: {len(df)}")

        if not df.empty:
            print(f"   涉案APP数: {df['final_app_name'].nunique()}")
            print(f"   涉案总金额: {df['involved_amount'].sum():,.2f} 元")

    else:
        print("❌ 数据库连接失败")