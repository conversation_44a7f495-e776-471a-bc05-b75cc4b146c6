"""
可疑模式检测模块
负责检测各种异常模式和可疑行为
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict, Counter
import logging
from difflib import SequenceMatcher


@dataclass
class SuspiciousPattern:
    """可疑模式数据类"""
    pattern_id: str
    pattern_type: str  # 模式类型：案件激增、金额异常、APP变种等
    severity: str  # 严重程度：低、中、高、极高
    confidence: float  # 置信度 0-1
    description: str  # 描述
    affected_apps: List[str]  # 涉及的APP
    evidence: Dict[str, Any]  # 证据数据
    detected_at: datetime


class PatternDetector:
    """可疑模式检测器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 检测阈值配置
        self.thresholds = {
            'case_spike_ratio': 3.0,  # 案件数激增倍数
            'amount_spike_ratio': 2.5,  # 金额激增倍数
            'similarity_threshold': 0.8,  # APP名称相似度
            'rapid_growth_days': 3,  # 快速增长天数
            'high_amount_threshold': 100000,  # 高额案件阈值
            'burst_case_count': 5  # 爆发案件数阈值
        }

        self.detected_patterns = []

    def detect_all_patterns(self, df: pd.DataFrame) -> List[SuspiciousPattern]:
        """检测所有可疑模式"""
        if df is None or df.empty:
            self.logger.warning("数据为空，无法进行模式检测")
            return []

        self.detected_patterns.clear()

        # 1. 案件数激增检测
        case_spike_patterns = self._detect_case_spikes(df)
        self.detected_patterns.extend(case_spike_patterns)

        # 2. 金额异常检测
        amount_anomaly_patterns = self._detect_amount_anomalies(df)
        self.detected_patterns.extend(amount_anomaly_patterns)

        # 3. APP变种检测
        variant_patterns = self._detect_app_variants(df)
        self.detected_patterns.extend(variant_patterns)

        # 4. 快速传播检测
        rapid_spread_patterns = self._detect_rapid_spread(df)
        self.detected_patterns.extend(rapid_spread_patterns)

        # 5. 时间集中检测
        temporal_patterns = self._detect_temporal_clustering(df)
        self.detected_patterns.extend(temporal_patterns)

        # 6. 地理集中检测
        geographic_patterns = self._detect_geographic_clustering(df)
        self.detected_patterns.extend(geographic_patterns)

        self.logger.info(f"检测完成，发现 {len(self.detected_patterns)} 个可疑模式")
        return self.detected_patterns

    def detect_patterns(self, df: pd.DataFrame) -> List[SuspiciousPattern]:
        """检测可疑模式（兼容旧接口）"""
        return self.detect_all_patterns(df)

    def _detect_case_spikes(self, df: pd.DataFrame) -> List[SuspiciousPattern]:
        """检测案件数激增模式"""
        patterns = []

        if 'final_app_name' not in df.columns or 'insert_day' not in df.columns:
            return patterns

        current_time = datetime.now()
        recent_days = self.thresholds['rapid_growth_days']
        recent_date = current_time - timedelta(days=recent_days)

        # 最近几天的案件
        recent_cases = df[df['insert_day'] >= recent_date]
        if recent_cases.empty:
            return patterns

        recent_app_counts = recent_cases['final_app_name'].value_counts()

        # 历史对比期间
        history_start = current_time - timedelta(days=recent_days * 3)
        history_end = recent_date
        history_cases = df[(df['insert_day'] >= history_start) &
                           (df['insert_day'] < history_end)]

        if not history_cases.empty:
            history_app_counts = history_cases['final_app_name'].value_counts()
            history_daily_avg = history_app_counts / (recent_days * 2)

            for app, recent_count in recent_app_counts.items():
                if recent_count >= self.thresholds['burst_case_count']:
                    historical_avg = history_daily_avg.get(app, 0) * recent_days

                    if historical_avg > 0:
                        spike_ratio = recent_count / historical_avg
                        if spike_ratio >= self.thresholds['case_spike_ratio']:
                            severity = self._calculate_severity(spike_ratio, [3, 5, 8])

                            pattern = SuspiciousPattern(
                                pattern_id=f"case_spike_{app}_{int(current_time.timestamp())}",
                                pattern_type="案件数激增",
                                severity=severity,
                                confidence=min(0.9, spike_ratio / 10),
                                description=f"{app} 最近{recent_days}天案件数激增{spike_ratio:.1f}倍",
                                affected_apps=[app],
                                evidence={
                                    'recent_count': recent_count,
                                    'historical_avg': historical_avg,
                                    'spike_ratio': spike_ratio,
                                    'detection_period': recent_days
                                },
                                detected_at=current_time
                            )
                            patterns.append(pattern)

        return patterns

    def _detect_amount_anomalies(self, df: pd.DataFrame) -> List[SuspiciousPattern]:
        """检测金额异常模式"""
        patterns = []

        if 'involved_amount' not in df.columns or 'final_app_name' not in df.columns:
            return patterns

        # 按APP分组分析金额分布
        for app, app_data in df.groupby('final_app_name'):
            if len(app_data) < 5:  # 数据太少，跳过
                continue

            amounts = app_data['involved_amount'].dropna()
            if amounts.empty:
                continue

            mean_amount = amounts.mean()
            std_amount = amounts.std()

            if std_amount == 0:
                continue

            # 检测异常高额案件
            high_threshold = mean_amount + 2 * std_amount
            high_amount_cases = amounts[amounts > high_threshold]

            if len(high_amount_cases) >= 2:
                max_amount = high_amount_cases.max()
                anomaly_ratio = max_amount / mean_amount

                if anomaly_ratio >= self.thresholds['amount_spike_ratio']:
                    severity = self._calculate_severity(anomaly_ratio, [2.5, 5, 10])

                    pattern = SuspiciousPattern(
                        pattern_id=f"amount_anomaly_{app}_{int(datetime.now().timestamp())}",
                        pattern_type="金额异常",
                        severity=severity,
                        confidence=min(0.9, len(high_amount_cases) / len(amounts)),
                        description=f"{app} 出现{len(high_amount_cases)}起异常高额案件",
                        affected_apps=[app],
                        evidence={
                            'high_amount_count': len(high_amount_cases),
                            'max_amount': max_amount,
                            'mean_amount': mean_amount,
                            'anomaly_ratio': anomaly_ratio
                        },
                        detected_at=datetime.now()
                    )
                    patterns.append(pattern)

        return patterns

    def _detect_app_variants(self, df: pd.DataFrame) -> List[SuspiciousPattern]:
        """检测APP变种模式"""
        patterns = []

        if 'final_app_name' not in df.columns:
            return patterns

        app_names = df['final_app_name'].unique()
        variant_groups = []

        # 寻找相似的APP名称
        for i, app1 in enumerate(app_names):
            similar_apps = [app1]

            for j, app2 in enumerate(app_names[i + 1:], i + 1):
                similarity = SequenceMatcher(None, app1.lower(), app2.lower()).ratio()
                if similarity >= self.thresholds['similarity_threshold']:
                    similar_apps.append(app2)

            if len(similar_apps) > 1:
                # 检查是否已经在其他组中
                is_duplicate = any(
                    any(app in existing_group for app in similar_apps)
                    for existing_group in variant_groups
                )

                if not is_duplicate:
                    variant_groups.append(similar_apps)

        # 为每个变种组创建模式
        for group in variant_groups:
            if len(group) >= 2:
                total_cases = sum(len(df[df['final_app_name'] == app]) for app in group)

                severity = self._calculate_severity(len(group), [2, 4, 6])

                pattern = SuspiciousPattern(
                    pattern_id=f"app_variants_{hash(tuple(sorted(group)))}",
                    pattern_type="APP变种",
                    severity=severity,
                    confidence=0.8,
                    description=f"发现{len(group)}个疑似变种APP",
                    affected_apps=group,
                    evidence={
                        'variant_count': len(group),
                        'total_cases': total_cases,
                        'apps': group
                    },
                    detected_at=datetime.now()
                )
                patterns.append(pattern)

        return patterns

    def _detect_rapid_spread(self, df: pd.DataFrame) -> List[SuspiciousPattern]:
        """检测快速传播模式"""
        patterns = []

        if 'final_app_name' not in df.columns or 'insert_day' not in df.columns:
            return patterns

        # 分析每个APP的传播速度
        for app, app_data in df.groupby('final_app_name'):
            if len(app_data) < 5:
                continue

            # 按天统计案件数
            daily_cases = app_data.groupby('insert_day').size().sort_index()

            if len(daily_cases) < 3:
                continue

            # 检测连续增长
            consecutive_growth = 0
            max_consecutive = 0

            for i in range(1, len(daily_cases)):
                if daily_cases.iloc[i] > daily_cases.iloc[i - 1]:
                    consecutive_growth += 1
                    max_consecutive = max(max_consecutive, consecutive_growth)
                else:
                    consecutive_growth = 0

            # 检测增长率
            if len(daily_cases) >= 2:
                growth_rate = (daily_cases.iloc[-1] - daily_cases.iloc[0]) / max(daily_cases.iloc[0], 1)

                if max_consecutive >= 3 and growth_rate >= 2:
                    severity = self._calculate_severity(growth_rate, [2, 5, 10])

                    pattern = SuspiciousPattern(
                        pattern_id=f"rapid_spread_{app}_{int(datetime.now().timestamp())}",
                        pattern_type="快速传播",
                        severity=severity,
                        confidence=min(0.9, max_consecutive / 7),
                        description=f"{app} 呈现快速传播趋势，连续{max_consecutive}天增长",
                        affected_apps=[app],
                        evidence={
                            'consecutive_growth_days': max_consecutive,
                            'growth_rate': growth_rate,
                            'daily_cases': daily_cases.to_dict()
                        },
                        detected_at=datetime.now()
                    )
                    patterns.append(pattern)

        return patterns

    def _detect_temporal_clustering(self, df: pd.DataFrame) -> List[SuspiciousPattern]:
        """检测时间集中模式"""
        patterns = []

        if 'occurrence_time' not in df.columns or 'final_app_name' not in df.columns:
            return patterns

        # 按APP分析时间分布
        for app, app_data in df.groupby('final_app_name'):
            if len(app_data) < 10:
                continue

            # 提取小时信息
            if app_data['occurrence_time'].dtype == 'object':
                try:
                    app_data = app_data.copy()
                    app_data['occurrence_time'] = pd.to_datetime(app_data['occurrence_time'])
                except:
                    continue

            hours = app_data['occurrence_time'].dt.hour
            hour_counts = hours.value_counts()

            # 检测是否有明显的时间集中
            max_hour_count = hour_counts.max()
            total_cases = len(app_data)
            concentration_ratio = max_hour_count / total_cases

            if concentration_ratio >= 0.3 and max_hour_count >= 5:
                peak_hour = hour_counts.idxmax()
                severity = self._calculate_severity(concentration_ratio, [0.3, 0.5, 0.7])

                pattern = SuspiciousPattern(
                    pattern_id=f"temporal_cluster_{app}_{peak_hour}",
                    pattern_type="时间集中",
                    severity=severity,
                    confidence=concentration_ratio,
                    description=f"{app} 在{peak_hour}时集中发生{max_hour_count}起案件",
                    affected_apps=[app],
                    evidence={
                        'peak_hour': peak_hour,
                        'peak_count': max_hour_count,
                        'concentration_ratio': concentration_ratio,
                        'hour_distribution': hour_counts.to_dict()
                    },
                    detected_at=datetime.now()
                )
                patterns.append(pattern)

        return patterns

    def _detect_geographic_clustering(self, df: pd.DataFrame) -> List[SuspiciousPattern]:
        """检测地理集中模式"""
        patterns = []

        if 'occurrence_area' not in df.columns or 'final_app_name' not in df.columns:
            return patterns

        # 按APP分析地理分布
        for app, app_data in df.groupby('final_app_name'):
            if len(app_data) < 10:
                continue

            area_counts = app_data['occurrence_area'].value_counts()

            # 检测地理集中度
            max_area_count = area_counts.max()
            total_cases = len(app_data)
            concentration_ratio = max_area_count / total_cases

            if concentration_ratio >= 0.4 and max_area_count >= 5:
                top_area = area_counts.index[0]
                severity = self._calculate_severity(concentration_ratio, [0.4, 0.6, 0.8])

                pattern = SuspiciousPattern(
                    pattern_id=f"geo_cluster_{app}_{hash(top_area)}",
                    pattern_type="地理集中",
                    severity=severity,
                    confidence=concentration_ratio,
                    description=f"{app} 在{top_area}集中发生{max_area_count}起案件",
                    affected_apps=[app],
                    evidence={
                        'top_area': top_area,
                        'area_count': max_area_count,
                        'concentration_ratio': concentration_ratio,
                        'area_distribution': area_counts.head(5).to_dict()
                    },
                    detected_at=datetime.now()
                )
                patterns.append(pattern)

        return patterns

    def _calculate_severity(self, value: float, thresholds: List[float]) -> str:
        """根据阈值计算严重程度"""
        if value >= thresholds[2]:
            return "极高"
        elif value >= thresholds[1]:
            return "高"
        elif value >= thresholds[0]:
            return "中"
        else:
            return "低"

    def get_patterns_by_type(self, pattern_type: str) -> List[SuspiciousPattern]:
        """按类型获取模式"""
        return [p for p in self.detected_patterns if p.pattern_type == pattern_type]

    def get_patterns_by_severity(self, severity: str) -> List[SuspiciousPattern]:
        """按严重程度获取模式"""
        return [p for p in self.detected_patterns if p.severity == severity]

    def get_patterns_by_app(self, app_name: str) -> List[SuspiciousPattern]:
        """按APP获取模式"""
        return [p for p in self.detected_patterns if app_name in p.affected_apps]

    def get_pattern_summary(self) -> Dict[str, Any]:
        """获取模式检测摘要"""
        if not self.detected_patterns:
            return {
                'total_patterns': 0,
                'severity_distribution': {},
                'type_distribution': {},
                'affected_apps_count': 0
            }

        severity_counts = Counter(p.severity for p in self.detected_patterns)
        type_counts = Counter(p.pattern_type for p in self.detected_patterns)

        all_affected_apps = set()
        for pattern in self.detected_patterns:
            all_affected_apps.update(pattern.affected_apps)

        return {
            'total_patterns': len(self.detected_patterns),
            'severity_distribution': dict(severity_counts),
            'type_distribution': dict(type_counts),
            'affected_apps_count': len(all_affected_apps),
            'high_risk_patterns': len([p for p in self.detected_patterns if p.severity in ['高', '极高']])
        }

    def clear_patterns(self):
        """清空检测结果"""
        self.detected_patterns.clear()
        self.logger.info("已清空模式检测结果")


# 工具函数
def create_pattern_detector(custom_thresholds: Optional[Dict[str, float]] = None) -> PatternDetector:
    """创建模式检测器实例"""
    detector = PatternDetector()

    if custom_thresholds:
        detector.thresholds.update(custom_thresholds)

    return detector