"""
报告生成模块
负责生成HTML报告和风险评估报告
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Pattern
import logging
import json
import base64
from io import BytesIO
from dataclasses import dataclass, asdict
import matplotlib.pyplot as plt


@dataclass
class RiskAssessment:
    """风险评估结果"""
    overall_risk_level: str  # 整体风险等级：低、中、高、极高
    risk_score: float  # 风险评分 0-100
    key_risks: List[str]  # 主要风险点
    recommendations: List[str]  # 建议措施
    assessment_time: datetime


@dataclass
class ReportSection:
    """报告章节"""
    title: str
    content: str
    charts: List[str] = None
    data_tables: List[Dict] = None


class HTMLReportGenerator:
    """HTML报告生成器"""

    def __init__(self, template_dir: str = None, output_dir: str = None):
        self.logger = logging.getLogger(__name__)

        # 目录配置
        self.template_dir = Path(template_dir) if template_dir else Path("./templates")
        self.output_dir = Path(output_dir) if output_dir else Path("./reports")
        self.output_dir.mkdir(exist_ok=True)

        # HTML模板
        self.html_template = self._get_html_template()

    def _get_html_template(self) -> str:
        """获取HTML模板"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        body {{ 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            background: #f5f5f5;
        }}
        .container {{ 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 20px; 
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        .header {{ 
            text-align: center; 
            padding: 30px 0; 
            border-bottom: 3px solid #2E86AB;
            margin-bottom: 30px;
        }}
        .header h1 {{ 
            color: #2E86AB; 
            font-size: 2.5em; 
            margin-bottom: 10px;
        }}
        .header .meta {{ 
            color: #666; 
            font-size: 1.1em;
        }}
        .section {{ 
            margin-bottom: 40px; 
            padding: 20px;
            background: #fafafa;
            border-radius: 8px;
            border-left: 4px solid #2E86AB;
        }}
        .section h2 {{ 
            color: #2E86AB; 
            font-size: 1.8em; 
            margin-bottom: 20px;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }}
        .section h3 {{ 
            color: #A23B72; 
            font-size: 1.4em; 
            margin: 20px 0 15px 0;
        }}
        .risk-level {{ 
            display: inline-block; 
            padding: 8px 16px; 
            border-radius: 20px; 
            font-weight: bold;
            color: white;
        }}
        .risk-low {{ background: #5C946E; }}
        .risk-medium {{ background: #F18F01; }}
        .risk-high {{ background: #C73E1D; }}
        .risk-extreme {{ background: #8B0000; }}
        .chart-container {{ 
            text-align: center; 
            margin: 20px 0;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .chart-container img {{ 
            max-width: 100%; 
            height: auto;
            border-radius: 4px;
        }}
        .data-table {{ 
            width: 100%; 
            border-collapse: collapse; 
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .data-table th {{ 
            background: #2E86AB; 
            color: white; 
            padding: 12px; 
            text-align: left;
        }}
        .data-table td {{ 
            padding: 12px; 
            border-bottom: 1px solid #eee;
        }}
        .data-table tr:hover {{ background: #f9f9f9; }}
        .summary-cards {{ 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 20px; 
            margin: 20px 0;
        }}
        .summary-card {{ 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-top: 4px solid #2E86AB;
        }}
        .summary-card h3 {{ 
            color: #2E86AB; 
            margin-bottom: 10px;
        }}
        .summary-card .value {{ 
            font-size: 2em; 
            font-weight: bold; 
            color: #A23B72;
        }}
        .alert-box {{ 
            background: #fff3cd; 
            border: 1px solid #ffeaa7; 
            border-radius: 8px; 
            padding: 15px; 
            margin: 15px 0;
        }}
        .alert-box.danger {{ 
            background: #f8d7da; 
            border-color: #f5c6cb;
        }}
        .alert-box.success {{ 
            background: #d4edda; 
            border-color: #c3e6cb;
        }}
        .footer {{ 
            text-align: center; 
            padding: 20px; 
            color: #666; 
            border-top: 1px solid #eee;
            margin-top: 40px;
        }}
        .pattern-list {{ 
            list-style: none;
        }}
        .pattern-list li {{ 
            background: white; 
            margin: 10px 0; 
            padding: 15px; 
            border-radius: 8px;
            border-left: 4px solid #F18F01;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        .pattern-severity {{ 
            float: right; 
            padding: 4px 8px; 
            border-radius: 12px; 
            font-size: 0.8em;
            color: white;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{title}</h1>
            <div class="meta">
                生成时间: {generation_time} | 数据时间范围: {date_range}
            </div>
        </div>

        {content}

        <div class="footer">
            <p>本报告由涉诈APP监控系统自动生成 | 数据来源: 反诈平台数据库</p>
            <p>报告生成时间: {generation_time}</p>
        </div>
    </div>
</body>
</html>
        """

    def generate_comprehensive_report(self, df: pd.DataFrame, analysis_results: Dict[str, Any],
                                      pattern_results: List = None, title: str = "涉诈APP分析报告") -> str:
        """生成综合分析报告"""
        try:
            # 生成各个章节
            sections = []

            # 1. 执行摘要
            sections.append(self._generate_executive_summary(df, analysis_results))

            # 2. 风险评估
            risk_assessment = self._generate_risk_assessment(df, analysis_results, pattern_results)
            sections.append(self._generate_risk_section(risk_assessment))

            # 3. 数据概览
            sections.append(self._generate_data_overview(df))

            # 4. APP分析
            sections.append(self._generate_app_analysis(df, analysis_results))

            # 5. 资金分析
            if 'financial' in analysis_results:
                sections.append(self._generate_financial_analysis(analysis_results['financial']))

            # 6. 可疑模式检测
            if pattern_results:
                sections.append(self._generate_pattern_analysis(pattern_results))

            # 7. 新APP检测
            if 'new_apps' in analysis_results:
                sections.append(self._generate_new_app_analysis(analysis_results['new_apps']))

            # 8. 受害人画像
            if 'victim_profile' in analysis_results:
                sections.append(self._generate_victim_analysis(analysis_results['victim_profile']))

            # 9. 建议措施
            sections.append(self._generate_recommendations(risk_assessment))

            # 组装HTML内容
            content = "\n".join([self._section_to_html(section) for section in sections])

            # 生成完整HTML
            html_content = self.html_template.format(
                title=title,
                generation_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                date_range=self._get_date_range(df),
                content=content
            )

            # 保存报告
            filename = f"fraud_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
            filepath = self.output_dir / filename

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)

            self.logger.info(f"报告已生成: {filepath}")
            return str(filepath)

        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")
            raise

    def _generate_executive_summary(self, df: pd.DataFrame, analysis_results: Dict) -> ReportSection:
        """生成执行摘要"""
        total_cases = len(df)
        total_apps = df['final_app_name'].nunique() if 'final_app_name' in df.columns else 0
        total_amount = df['involved_amount'].sum() if 'involved_amount' in df.columns else 0

        # 获取时间范围
        date_range = self._get_date_range(df)

        # 关键指标卡片
        summary_cards = f"""
        <div class="summary-cards">
            <div class="summary-card">
                <h3>总案件数</h3>
                <div class="value">{total_cases:,}</div>
            </div>
            <div class="summary-card">
                <h3>涉及APP数</h3>
                <div class="value">{total_apps:,}</div>
            </div>
            <div class="summary-card">
                <h3>涉案总金额</h3>
                <div class="value">{total_amount / 10000:.1f}万</div>
            </div>
            <div class="summary-card">
                <h3>平均案件金额</h3>
                <div class="value">{total_amount / max(total_cases, 1) / 10000:.1f}万</div>
            </div>
        </div>
        """

        # 主要发现
        key_findings = []
        if 'new_apps' in analysis_results and analysis_results['new_apps']:
            new_count = len(analysis_results['new_apps'].get('new_apps', []))
            if new_count > 0:
                key_findings.append(f"发现 {new_count} 个新增涉诈APP")

        if 'financial' in analysis_results:
            financial = analysis_results['financial']
            if 'high_amount_cases' in financial and financial['high_amount_cases'] > 0:
                key_findings.append(f"检测到 {financial['high_amount_cases']} 起高额案件")

        findings_html = ""
        if key_findings:
            findings_list = "\n".join([f"<li>{finding}</li>" for finding in key_findings])
            findings_html = f"""
            <h3>主要发现</h3>
            <ul>{findings_list}</ul>
            """

        content = f"""
        <p>本报告分析了 {date_range} 期间的涉诈APP案件数据，共涉及 {total_cases:,} 起案件，
        {total_apps:,} 个不同的APP，总涉案金额 {total_amount / 10000:.1f} 万元。</p>

        {summary_cards}
        {findings_html}
        """

        return ReportSection(title="执行摘要", content=content)

    def _generate_risk_assessment(self, df: pd.DataFrame, analysis_results: Dict,
                                  pattern_results: List = None) -> RiskAssessment:
        """生成风险评估"""
        risk_factors = []
        risk_score = 0

        # 基于案件数量评估
        total_cases = len(df)
        if total_cases > 1000:
            risk_factors.append("案件数量极高")
            risk_score += 30
        elif total_cases > 500:
            risk_factors.append("案件数量较高")
            risk_score += 20
        elif total_cases > 100:
            risk_factors.append("案件数量中等")
            risk_score += 10

        # 基于金额评估
        if 'involved_amount' in df.columns:
            total_amount = df['involved_amount'].sum()
            avg_amount = df['involved_amount'].mean()

            if avg_amount > 100000:
                risk_factors.append("平均案件金额极高")
                risk_score += 25
            elif avg_amount > 50000:
                risk_factors.append("平均案件金额较高")
                risk_score += 15

            if total_amount > 10000000:
                risk_factors.append("总涉案金额巨大")
                risk_score += 20

        # 基于新APP数量评估
        if 'new_apps' in analysis_results and analysis_results['new_apps']:
            new_count = len(analysis_results['new_apps'].get('new_apps', []))
            if new_count > 10:
                risk_factors.append("新增APP数量过多")
                risk_score += 15
            elif new_count > 5:
                risk_factors.append("新增APP数量较多")
                risk_score += 10

        # 基于可疑模式评估
        if pattern_results:
            high_risk_patterns = [p for p in pattern_results if p.severity in ['高', '极高']]
            if len(high_risk_patterns) > 5:
                risk_factors.append("发现多个高风险模式")
                risk_score += 20
            elif len(high_risk_patterns) > 0:
                risk_factors.append("发现高风险模式")
                risk_score += 15

        # 确定风险等级
        if risk_score >= 80:
            risk_level = "极高"
        elif risk_score >= 60:
            risk_level = "高"
        elif risk_score >= 40:
            risk_level = "中"
        else:
            risk_level = "低"

        # 生成建议
        recommendations = self._generate_risk_recommendations(risk_level, risk_factors)

        return RiskAssessment(
            overall_risk_level=risk_level,
            risk_score=risk_score,
            key_risks=risk_factors,
            recommendations=recommendations,
            assessment_time=datetime.now()
        )

    def _generate_risk_recommendations(self, risk_level: str, risk_factors: List[str]) -> List[str]:
        """生成风险建议"""
        recommendations = []

        if risk_level in ["极高", "高"]:
            recommendations.extend([
                "立即启动应急响应机制",
                "加强对高风险APP的监控和拦截",
                "协调相关部门进行联合打击",
                "及时发布预警信息"
            ])

        if "新增APP数量过多" in risk_factors or "新增APP数量较多" in risk_factors:
            recommendations.append("重点关注新增APP的传播渠道和推广方式")

        if "案件数量极高" in risk_factors:
            recommendations.append("增加人力资源投入，提高处置效率")

        if "平均案件金额极高" in risk_factors or "总涉案金额巨大" in risk_factors:
            recommendations.extend([
                "加强资金流向追踪",
                "协调银行等金融机构加强风控"
            ])

        # 通用建议
        recommendations.extend([
            "持续监控数据变化趋势",
            "完善预警机制和响应流程",
            "加强宣传教育，提高公众防范意识"
        ])

        return recommendations

    def _generate_risk_section(self, risk_assessment: RiskAssessment) -> ReportSection:
        """生成风险评估章节"""
        risk_class = f"risk-{risk_assessment.overall_risk_level.lower()}"
        if risk_assessment.overall_risk_level == "极高":
            risk_class = "risk-extreme"
        elif risk_assessment.overall_risk_level == "中":
            risk_class = "risk-medium"
        elif risk_assessment.overall_risk_level == "低":
            risk_class = "risk-low"
        else:
            risk_class = "risk-high"

        # 风险因素列表
        risk_factors_html = ""
        if risk_assessment.key_risks:
            factors_list = "\n".join([f"<li>{factor}</li>" for factor in risk_assessment.key_risks])
            risk_factors_html = f"<ul>{factors_list}</ul>"

        # 建议措施列表
        recommendations_html = ""
        if risk_assessment.recommendations:
            rec_list = "\n".join([f"<li>{rec}</li>" for rec in risk_assessment.recommendations])
            recommendations_html = f"<ol>{rec_list}</ol>"

        content = f"""
        <div class="alert-box danger">
            <h3>整体风险等级: <span class="risk-level {risk_class}">{risk_assessment.overall_risk_level}</span></h3>
            <p>风险评分: <strong>{risk_assessment.risk_score:.1f}/100</strong></p>
        </div>

        <h3>主要风险因素</h3>
        {risk_factors_html}

        <h3>建议措施</h3>
        {recommendations_html}
        """

        return ReportSection(title="风险评估", content=content)

    def _generate_data_overview(self, df: pd.DataFrame) -> ReportSection:
        """生成数据概览章节"""
        # 基础统计
        total_cases = len(df)
        date_range = self._get_date_range(df)

        # 按日期统计
        daily_stats = ""
        if 'insert_day' in df.columns:
            daily_counts = df.groupby('insert_day').size()
            avg_daily = daily_counts.mean()
            max_daily = daily_counts.max()
            min_daily = daily_counts.min()

            daily_stats = f"""
            <h3>时间分布</h3>
            <p>日均案件数: {avg_daily:.1f} 起</p>
            <p>单日最高: {max_daily} 起</p>
            <p>单日最低: {min_daily} 起</p>
            """

        # APP统计
        app_stats = ""
        if 'final_app_name' in df.columns:
            app_counts = df['final_app_name'].value_counts()
            top_apps = app_counts.head(5)

            app_list = "\n".join([
                f"<tr><td>{app}</td><td>{count}</td><td>{count / total_cases * 100:.1f}%</td></tr>"
                for app, count in top_apps.items()
            ])

            app_stats = f"""
            <h3>TOP5 涉案APP</h3>
            <table class="data-table">
                <thead>
                    <tr><th>APP名称</th><th>案件数</th><th>占比</th></tr>
                </thead>
                <tbody>{app_list}</tbody>
            </table>
            """

        content = f"""
        <p>数据时间范围: {date_range}</p>
        <p>总案件数: {total_cases:,} 起</p>

        {daily_stats}
        {app_stats}
        """

        return ReportSection(title="数据概览", content=content)

    def _generate_app_analysis(self, df: pd.DataFrame, analysis_results: Dict) -> ReportSection:
        """生成APP分析章节"""
        if 'final_app_name' not in df.columns:
            return ReportSection(title="APP分析", content="<p>缺少APP数据</p>")

        app_counts = df['final_app_name'].value_counts()
        total_apps = len(app_counts)

        # 集中度分析
        top5_ratio = app_counts.head(5).sum() / len(df) * 100
        top10_ratio = app_counts.head(10).sum() / len(df) * 100

        concentration_analysis = f"""
        <h3>集中度分析</h3>
        <p>TOP5 APP占比: {top5_ratio:.1f}%</p>
        <p>TOP10 APP占比: {top10_ratio:.1f}%</p>
        """

        # 长尾分析
        single_case_apps = len(app_counts[app_counts == 1])
        longtail_analysis = f"""
        <h3>长尾分析</h3>
        <p>仅有1起案件的APP: {single_case_apps} 个 ({single_case_apps / total_apps * 100:.1f}%)</p>
        """

        content = f"""
        <p>共涉及 {total_apps} 个不同的APP</p>

        {concentration_analysis}
        {longtail_analysis}
        """

        return ReportSection(title="APP分析", content=content)

    def _generate_financial_analysis(self, financial_data: Dict) -> ReportSection:
        """生成资金分析章节"""
        content = "<p>资金影响分析结果:</p><ul>"

        for key, value in financial_data.items():
            if isinstance(value, (int, float)):
                if 'amount' in key.lower():
                    content += f"<li>{key}: {value / 10000:.1f}万元</li>"
                else:
                    content += f"<li>{key}: {value:,}</li>"
            else:
                content += f"<li>{key}: {value}</li>"

        content += "</ul>"

        return ReportSection(title="资金分析", content=content)

    def _generate_pattern_analysis(self, pattern_results: List) -> ReportSection:
        """生成模式分析章节"""
        if not pattern_results:
            return ReportSection(title="可疑模式检测", content="<p>未发现可疑模式</p>")

        # 按严重程度分组
        severity_counts = {}
        for pattern in pattern_results:
            severity = pattern.severity
            severity_counts[severity] = severity_counts.get(severity, 0) + 1

        # 统计信息
        stats_html = f"""
        <div class="alert-box">
            <h3>检测统计</h3>
            <p>共发现 {len(pattern_results)} 个可疑模式</p>
        """

        for severity, count in severity_counts.items():
            stats_html += f"<p>{severity}风险: {count} 个</p>"

        stats_html += "</div>"

        # 模式列表
        patterns_html = "<ul class='pattern-list'>"
        for pattern in pattern_results[:10]:  # 显示前10个
            severity_class = f"risk-{pattern.severity.lower()}"
            if pattern.severity == "极高":
                severity_class = "risk-extreme"
            elif pattern.severity == "中":
                severity_class = "risk-medium"
            elif pattern.severity == "低":
                severity_class = "risk-low"
            else:
                severity_class = "risk-high"

            patterns_html += f"""
            <li>
                <span class="pattern-severity {severity_class}">{pattern.severity}</span>
                <strong>[{pattern.pattern_type}]</strong> {pattern.description}
                <br><small>涉及APP: {', '.join(pattern.affected_apps[:3])}
                {'...' if len(pattern.affected_apps) > 3 else ''}</small>
            </li>
            """

        patterns_html += "</ul>"

        content = stats_html + patterns_html

        return ReportSection(title="可疑模式检测", content=content)

    def _generate_new_app_analysis(self, new_app_data: Dict) -> ReportSection:
        """生成新APP分析章节"""
        new_apps = new_app_data.get('new_apps', [])

        if not new_apps:
            return ReportSection(title="新APP检测", content="<p>未发现新增APP</p>")

        content = f"<p>发现 {len(new_apps)} 个新增涉诈APP:</p><ul>"

        for app in new_apps[:10]:  # 显示前10个
            if isinstance(app, dict):
                app_name = app.get('app_name', 'Unknown')
                case_count = app.get('case_count', 0)
                content += f"<li>{app_name} ({case_count} 起案件)</li>"
            else:
                content += f"<li>{app}</li>"

        if len(new_apps) > 10:
            content += f"<li>... 还有 {len(new_apps) - 10} 个</li>"

        content += "</ul>"

        return ReportSection(title="新APP检测", content=content)

    def _generate_victim_analysis(self, victim_data: Dict) -> ReportSection:
        """生成受害人分析章节"""
        content = "<h3>受害人特征分析</h3>"

        if 'age_distribution' in victim_data:
            content += "<h4>年龄分布</h4><ul>"
            for age_group, count in victim_data['age_distribution'].items():
                content += f"<li>{age_group}: {count} 人</li>"
            content += "</ul>"

        if 'gender_distribution' in victim_data:
            content += "<h4>性别分布</h4><ul>"
            for gender, count in victim_data['gender_distribution'].items():
                content += f"<li>{gender}: {count} 人</li>"
            content += "</ul>"

        return ReportSection(title="受害人画像", content=content)

    def _generate_recommendations(self, risk_assessment: RiskAssessment) -> ReportSection:
        """生成建议措施章节"""
        if not risk_assessment.recommendations:
            return ReportSection(title="建议措施", content="<p>暂无特殊建议</p>")

        content = "<ol>"
        for rec in risk_assessment.recommendations:
            content += f"<li>{rec}</li>"
        content += "</ol>"

        return ReportSection(title="建议措施", content=content)

    def _section_to_html(self, section: ReportSection) -> str:
        """将章节转换为HTML"""
        html = f"""
        <div class="section">
            <h2>{section.title}</h2>
            {section.content}
        """

        if section.charts:
            for chart in section.charts:
                html += f'<div class="chart-container"><img src="{chart}" alt="图表"></div>'

        if section.data_tables:
            for table in section.data_tables:
                html += self._dict_to_table(table)

        html += "</div>"
        return html

    def _dict_to_table(self, data: Dict) -> str:
        """将字典转换为HTML表格"""
        if not data:
            return ""

        html = '<table class="data-table"><thead><tr>'

        # 表头
        for key in data.keys():
            html += f"<th>{key}</th>"
        html += "</tr></thead><tbody><tr>"

        # 数据行
        for value in data.values():
            html += f"<td>{value}</td>"
        html += "</tr></tbody></table>"

        return html

    def _get_date_range(self, df: pd.DataFrame) -> str:
        """获取数据时间范围"""
        if 'insert_day' not in df.columns or df.empty:
            return "未知"

        try:
            min_date = df['insert_day'].min()
            max_date = df['insert_day'].max()

            if pd.isna(min_date) or pd.isna(max_date):
                return "未知"

            if min_date == max_date:
                return min_date.strftime("%Y-%m-%d")
            else:
                return f"{min_date.strftime('%Y-%m-%d')} 至 {max_date.strftime('%Y-%m-%d')}"
        except:
            return "未知"

    def embed_chart_in_html(self, fig: plt.Figure) -> str:
        """将matplotlib图表嵌入HTML"""
        try:
            buffer = BytesIO()
            fig.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)

            image_base64 = base64.b64encode(buffer.read()).decode()
            buffer.close()

            return f"data:image/png;base64,{image_base64}"
        except Exception as e:
            self.logger.error(f"嵌入图表失败: {e}")
            return ""


# 工具函数
def create_report_generator(template_dir: str = None, output_dir: str = None) -> HTMLReportGenerator:
    """创建报告生成器实例"""
    return HTMLReportGenerator(template_dir, output_dir)