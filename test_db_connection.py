#!/usr/bin/env python3
"""
测试数据库连接脚本
"""
import pandas as pd
from sqlalchemy import create_engine
from urllib.parse import quote_plus

# 数据库配置
DB_CONFIG = {
    'host': '**************',
    'port': 3306,
    'user': 'FZUser',
    'password': 'fz@20250324',
    'database': 'antiFraudPlatform',
    'charset': 'utf8mb4'
}

def test_connection():
    """测试数据库连接"""
    try:
        print("正在测试数据库连接...")
        print(f"主机: {DB_CONFIG['host']}")
        print(f"端口: {DB_CONFIG['port']}")
        print(f"用户: {DB_CONFIG['user']}")
        print(f"数据库: {DB_CONFIG['database']}")
        
        # 使用URL编码处理密码中的特殊字符
        encoded_password = quote_plus(DB_CONFIG['password'])
        connection_string = (
            f"mysql+pymysql://{DB_CONFIG['user']}:{encoded_password}@"
            f"{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}"
        )
        
        print(f"连接字符串: mysql+pymysql://{DB_CONFIG['user']}:***@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")
        
        # 创建引擎
        engine = create_engine(connection_string)
        
        # 测试连接
        with engine.connect() as connection:
            result = connection.execute("SELECT 1 as test")
            row = result.fetchone()
            print(f"连接测试成功! 结果: {row[0]}")
        
        # 测试查询表
        print("\n正在测试表查询...")
        test_query = "SELECT COUNT(*) as count FROM anti_fraud_case_new LIMIT 1"
        df = pd.read_sql(test_query, engine)
        print(f"anti_fraud_case_new 表记录数: {df.iloc[0]['count']}")
        
        # 测试查询字段
        print("\n正在测试表结构...")
        structure_query = "DESCRIBE anti_fraud_case_new"
        df_structure = pd.read_sql(structure_query, engine)
        print("表字段:")
        for _, row in df_structure.head(10).iterrows():
            print(f"  {row['Field']}: {row['Type']}")
        
        # 测试查询特定日期的数据
        print("\n正在测试日期查询...")
        date_query = "SELECT insert_day, COUNT(*) as count FROM anti_fraud_case_new GROUP BY insert_day ORDER BY insert_day DESC LIMIT 5"
        df_dates = pd.read_sql(date_query, engine)
        print("最近的数据日期:")
        for _, row in df_dates.iterrows():
            print(f"  {row['insert_day']}: {row['count']} 条记录")
        
        return True
        
    except Exception as e:
        print(f"数据库连接失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 提供一些调试建议
        print("\n调试建议:")
        print("1. 检查网络连接是否正常")
        print("2. 确认数据库服务器是否运行")
        print("3. 验证用户名和密码是否正确")
        print("4. 检查防火墙设置")
        print("5. 确认数据库是否允许远程连接")
        
        return False

if __name__ == "__main__":
    test_connection()
