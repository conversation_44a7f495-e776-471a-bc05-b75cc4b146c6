from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH
import json
from flask import send_file
from docx.shared import Pt, Cm
from docx.oxml.ns import qn
from docx.shared import RGBColor
import logging


class WordReportUtilsMonth:
    # 常量定义
    FONT_FANGSONG = '仿宋_GB2312'
    FONT_HEITI = '黑体'
    FONT_SIMSUN = 'SimSun'

    # 字体大小常量
    FONT_SIZE_NORMAL = Pt(16)
    FONT_SIZE_HEADING = Pt(15)
    FONT_SIZE_TITLE = Pt(20)
    FONT_SIZE_TABLE = Pt(10)

    # 颜色常量
    COLOR_BLACK = RGBColor(0, 0, 0)

    # 运营商常量
    OPERATORS = {
        'TELECOM': '电信',
        'UNICOM': '联通',
        'MOBILE': '移动',
        'BROADCAST': '广电',
        'VIRTUAL': '虚商'
    }

    @staticmethod
    def generate_case_analysis_report(data, file_stream):
        """
        生成案情分析报告 Word 文件
        :param data: 包含案件数据的字典
        :param file_stream: 文件流对象
        :raises RuntimeError: 当生成报告失败时抛出异常
        """
        try:
            doc = Document()

            # 设置文档样式
            WordReportUtilsMonth._setup_document_styles(doc)

            # 添加标题
            WordReportUtilsMonth._add_title(doc)

            # 添加概述段落
            WordReportUtilsMonth._add_overview_paragraph(doc, data)

            # 添加案情总体形势
            WordReportUtilsMonth._add_overall_situation_section(doc, data)

            # 添加案件分布类型
            WordReportUtilsMonth._add_case_distribution_section(doc, data)

            # 添加邮件附件目录
            WordReportUtilsMonth._add_email_attachments_section(doc, data)

            # 添加附件表
            WordReportUtilsMonth._add_appendix_tables_section(doc, data)

            # 保存文档
            doc.save(file_stream)

        except Exception as e:
            logging.error(f"生成案情分析报告失败: {str(e)}")
            raise RuntimeError("生成案情分析报告失败") from e

    @staticmethod
    def _setup_document_styles(doc):
        """设置文档样式"""
        # 设置正文样式
        style = doc.styles['Normal']
        font = style.font
        font.name = WordReportUtilsMonth.FONT_FANGSONG
        font.size = WordReportUtilsMonth.FONT_SIZE_NORMAL
        font.color.rgb = WordReportUtilsMonth.COLOR_BLACK
        style._element.rPr.rFonts.set(qn('w:eastAsia'), WordReportUtilsMonth.FONT_FANGSONG)
        style.paragraph_format.first_line_indent = Cm(0.67)

        # 设置标题样式
        for heading_style in ['Heading 1', 'Heading 2']:
            h_style = doc.styles[heading_style]
            h_font = h_style.font
            h_font.name = WordReportUtilsMonth.FONT_HEITI
            h_font.size = WordReportUtilsMonth.FONT_SIZE_HEADING
            h_font.color.rgb = WordReportUtilsMonth.COLOR_BLACK
            h_style._element.rPr.rFonts.set(qn('w:eastAsia'), WordReportUtilsMonth.FONT_HEITI)

    @staticmethod
    def _add_title(doc):
        """添加文档标题"""
        title = doc.add_heading('案情分析月报', level=1)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        for run in title.runs:
            run.font.name = WordReportUtilsMonth.FONT_SIMSUN
            run.font.size = WordReportUtilsMonth.FONT_SIZE_TITLE
            run.font.bold = True
            run.font.color.rgb = WordReportUtilsMonth.COLOR_BLACK
            run._element.rPr.rFonts.set(qn('w:eastAsia'), WordReportUtilsMonth.FONT_SIMSUN)

    @staticmethod
    def _add_overview_paragraph(doc, data):
        """添加概述段落"""
        overview_text = (
            f"{data.get('date_range', '')}共接收案情{data.get('case_count', '0')}条，"
            f"案情涉案总金额{data.get('loss_amount', '')}亿元，"
            f"针对案情总体形势、高发类型、损失金额等方面进行分析，详情如下："
        )
        WordReportUtilsMonth._add_centered_paragraph(doc, overview_text)

    @staticmethod
    def _add_overall_situation_section(doc, data):
        """添加案情总体形势部分"""
        title_two = doc.add_heading('一、案情总体形势', level=2)
        WordReportUtilsMonth._format_heading(title_two)

        # 构建案情描述
        height_data = data.get("phone_height_1000", [])
        length = len(height_data)
        result = "，".join([item["case_number"] for item in height_data])

        situation_text = (
            f"本月案情{data.get('case_count', '0')}条，95106未出现涉诈案件。"
            f"其中url类涉案{data.get('url_count', '')}起，占比{data.get('url_prop', '')}%；"
            f"APP类涉案{data.get('app_count', '')}起；占比{data.get('app_prop', '')}%；"
            f"电话、短信引流{data.get('sms_count', '')}起，占比{data.get('sms_prop', '')}%"
            f"（中国电信手机号码{data.get('telephone_count', '')}起）；"
            f"国际号码涉案{data.get('intel_count', '')}起，"
            f"106开头行短端口涉案{data.get('international_count', '')}起，"
            f"涉案金额大于1000万以上案件{length}起"
            f"{'，案件编号：' + result if length > 0 else '。'}"
        )
        WordReportUtilsMonth._add_paragraph(doc, situation_text)

    @staticmethod
    def _add_case_distribution_section(doc, data):
        """添加案件分布类型部分"""
        title_th = doc.add_heading('二、案件分布类型', level=2)
        WordReportUtilsMonth._format_heading(title_th)

        # 添加案件分布描述
        WordReportUtilsMonth._add_paragraph(
            doc, f"本月案件高发的5种案件类型分别为；{data.get('case_distribution_desc', '')}"
        )

        WordReportUtilsMonth._add_paragraph(
            doc, f"本月案件APP涉案635起，高发的5类APP包括{data.get('app_case_desc', '')}"
        )

        # 添加手机记录统计
        phone_stats_text = (
            f"涉案手机记录{data.get('phone_total', '')}个，"
            f"电信{data.get('tele_phone_total', '')}个，占比{data.get('tele_phone_proportion', '')}%，"
            f"联通{data.get('unicom_phone_total', '')}个，占比{data.get('unicom_phone_proportion', '')}%，"
            f"移动{data.get('move_phone_total', '')}个，占比{data.get('move_phone_proportion', '')}%，"
            f"广电{data.get('sva_phone_total', '')}个，占比{data.get('sva_phone_proportion', '')}%，"
            f"虚商{data.get('vc_phone_total', '')}个，占比{data.get('vc_phone_proportion', '')}%；"
        )
        WordReportUtilsMonth._add_paragraph(doc, phone_stats_text)

        # 添加各运营商案件金额描述
        operators_data = [
            ('', WordReportUtilsMonth.OPERATORS['TELECOM']),
            ('lt_', WordReportUtilsMonth.OPERATORS['UNICOM']),
            ('yd_', WordReportUtilsMonth.OPERATORS['MOBILE']),
            ('gd_', WordReportUtilsMonth.OPERATORS['BROADCAST']),
            ('xs_', WordReportUtilsMonth.OPERATORS['VIRTUAL'])
        ]

        for prefix, operator in operators_data:
            description = WordReportUtilsMonth._build_operator_amount_description(data, prefix, operator)
            WordReportUtilsMonth._add_paragraph(doc, "".join(description))

    @staticmethod
    def _add_email_attachments_section(doc, data):
        """添加邮件附件目录部分"""
        title_th = doc.add_heading('三、邮件附件目录', level=2)
        WordReportUtilsMonth._format_heading(title_th)

        attachments = [
            "全量案情",
            "电话短信引流案件",
            "url类案件",
            "国际号码案件",
            "涉APP类案件",
            "106号码案件",
            "运营商归属地统计结果"
        ]

        date_range = data.get("date_range", "")
        for i, attachment in enumerate(attachments, 1):
            WordReportUtilsMonth._add_paragraph(doc, f"{i}.附件{i}：{date_range}{attachment}")

    @staticmethod
    def _add_appendix_tables_section(doc, data):
        """添加附件表部分"""
        title_th = doc.add_heading('四、附件表', level=2)
        WordReportUtilsMonth._format_heading(title_th)

        # 添加案件类型表格
        WordReportUtilsMonth._add_paragraph(doc, "1．案件类型排序见下表：")
        WordReportUtilsMonth._add_case_table(doc, data.get("case_types", []))

        # 添加APP涉案表格
        WordReportUtilsMonth._add_paragraph(doc, "2.涉案APP TOP20详情如下：")
        WordReportUtilsMonth._add_app_table(doc, data.get("app_top_list", []))

    @staticmethod
    def _format_heading(heading):
        """格式化标题"""
        for run in heading.runs:
            run.font.name = WordReportUtilsMonth.FONT_SIMSUN
            run.font.size = WordReportUtilsMonth.FONT_SIZE_HEADING
            run.font.bold = True
            run.font.color.rgb = WordReportUtilsMonth.COLOR_BLACK
            run._element.rPr.rFonts.set(qn('w:eastAsia'), WordReportUtilsMonth.FONT_SIMSUN)

    @staticmethod
    def _add_paragraph(doc, text):
        """添加普通段落"""
        if text:
            doc.add_paragraph(text)

    @staticmethod
    def _add_centered_paragraph(doc, text):
        """添加居中段落"""
        if text:
            p = doc.add_paragraph(text)
            p.alignment = WD_ALIGN_PARAGRAPH.CENTER

    @staticmethod
    def _add_case_table(doc, case_data):
        """添加案件类型表格"""
        if not case_data:
            return

        table = doc.add_table(rows=1, cols=5)
        table.style = 'Table Grid'

        # 设置表头
        headers = ['序号', '类型', '总数（起）', '金额(万元)', '占比（%）']
        hdr_cells = table.rows[0].cells
        for i, header in enumerate(headers):
            hdr_cells[i].text = header

        # 添加数据行
        for item in case_data:
            row_cells = table.add_row().cells
            row_cells[0].text = str(item.get("id", ""))
            row_cells[1].text = str(item.get("type", ""))
            row_cells[2].text = str(item.get("total_count", ""))
            row_cells[3].text = str(item.get("amount", ""))
            row_cells[4].text = str(item.get("percentage", ""))

        WordReportUtilsMonth._set_table_font_style(table)

    @staticmethod
    def _add_app_table(doc, app_data):
        """添加APP涉案表格"""
        if not app_data:
            return

        table = doc.add_table(rows=1, cols=4)
        table.style = 'Table Grid'

        # 设置表头
        headers = ['序号', 'APP名称', '总数（起）', '占比（%）']
        hdr_cells = table.rows[0].cells
        for i, header in enumerate(headers):
            hdr_cells[i].text = header

        # 添加数据行
        for item in app_data:
            row_cells = table.add_row().cells
            row_cells[0].text = str(item.get("id", ""))
            row_cells[1].text = str(item.get("app_name", ""))
            row_cells[2].text = str(item.get("total_count", ""))
            row_cells[3].text = str(item.get("percentage", ""))

        WordReportUtilsMonth._set_table_font_style(table)

    @staticmethod
    def _set_table_font_style(table, font_name=None, font_size=None, font_color=None):
        """设置表格字体样式"""
        font_name = font_name or WordReportUtilsMonth.FONT_FANGSONG
        font_size = font_size or WordReportUtilsMonth.FONT_SIZE_TABLE
        font_color = font_color or WordReportUtilsMonth.COLOR_BLACK

        for row in table.rows:
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                    for run in paragraph.runs:
                        run.font.name = font_name
                        run.font.size = font_size
                        run.font.color.rgb = font_color
                        run._element.rPr.rFonts.set(qn('w:eastAsia'), font_name)

    @staticmethod
    def send_word_report(file_stream, filename="CaseAnalysisReport.docx"):
        """发送Word报告文件"""
        return send_file(
            file_stream,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )

    @staticmethod
    def _format_telecom_phones(results, operator_type):
        """
        从查询结果中筛选出指定运营商的手机号，并按归属地拼接成指定格式的字符串
        :param results: 查询结果列表
        :param operator_type: 运营商类型
        :return: 拼接后的字符串
        """
        if not results:
            return ""

        try:
            telecom_phones = []
            for item in results:
                phone_info_str = item.get("suspect_phone_info", "")
                if not phone_info_str:
                    continue

                phone_info_list = json.loads(phone_info_str)
                for info in phone_info_list:
                    if info.get("operator") == operator_type:
                        phone = info.get('phone', '')
                        province = info.get('province', '')
                        if phone and province:
                            telecom_phones.append(f"{phone}(归属地{province})")

            return "、".join(telecom_phones)
        except (json.JSONDecodeError, KeyError, TypeError) as e:
            logging.warning(f"解析手机信息失败: {str(e)}")
            return ""

    @staticmethod
    def _build_operator_amount_description(data, prefix, operator_name):
        """
        构建运营商案件金额描述
        :param data: 数据字典
        :param prefix: 数据键前缀
        :param operator_name: 运营商名称
        :return: 描述文本列表
        """
        amount_key = f"{prefix}telephone_amount" if prefix else "telephone_amount"
        desc_parts = [f"其中中国{operator_name}号码涉案金额{data.get(amount_key, '')}万，"]

        # 处理不同金额级别的案件
        amount_levels = [
            (f"{prefix}high_amount_5", f"{prefix}phone_list_50", "50万"),
            (f"{prefix}high_amount_10", f"{prefix}phone_list_100", "100万"),
            (f"{prefix}high_amount_100", f"{prefix}phone_list_1000", "1000万")
        ]

        for i, (count_key, phone_list_key, amount_desc) in enumerate(amount_levels):
            count = data.get(count_key, "0")
            desc_parts.append(f"{amount_desc}以上的案件{count}个")

            if count != "0":
                phone_list = data.get(phone_list_key, [])
                phones_str = WordReportUtilsMonth._format_telecom_phones(phone_list, operator_name)
                desc_parts.append(f":{phones_str}")

            # 添加适当的标点符号
            if i < len(amount_levels) - 1:
                desc_parts.append("，")
            else:
                desc_parts.append("。")

        return desc_parts

    # 保持向后兼容性的方法
    @staticmethod
    def format_telecom_phones(results, operator_type):
        """向后兼容的方法名"""
        return WordReportUtilsMonth._format_telecom_phones(results, operator_type)

    @staticmethod
    def build_case_amount_description_with_phones(data):
        """向后兼容的方法 - 电信"""
        return "".join(WordReportUtilsMonth._build_operator_amount_description(data, "", "电信"))

    @staticmethod
    def build_case_amount_description_with_phones_lt(data):
        """向后兼容的方法 - 联通"""
        return "".join(WordReportUtilsMonth._build_operator_amount_description(data, "lt_", "联通"))

    @staticmethod
    def build_case_amount_description_with_phones_yd(data):
        """向后兼容的方法 - 移动"""
        return "".join(WordReportUtilsMonth._build_operator_amount_description(data, "yd_", "移动"))

    @staticmethod
    def build_case_amount_description_with_phones_gd(data):
        """向后兼容的方法 - 广电"""
        return "".join(WordReportUtilsMonth._build_operator_amount_description(data, "gd_", "广电"))

    @staticmethod
    def build_case_amount_description_with_phones_xs(data):
        """向后兼容的方法 - 虚商"""
        return "".join(WordReportUtilsMonth._build_operator_amount_description(data, "xs_", "虚商"))

    # 保持向后兼容性的方法名
    @staticmethod
    def set_table_font_style(table, font_name=None, font_size=None, font_color=None):
        """向后兼容的方法名"""
        WordReportUtilsMonth._set_table_font_style(table, font_name, font_size, font_color)