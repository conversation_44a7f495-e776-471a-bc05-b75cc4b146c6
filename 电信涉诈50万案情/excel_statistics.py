from openpyxl import load_workbook, Workbook
from util import file_path, query_sql, excel_statistics_name, operator_key, province_key
from collections import defaultdict, Counter
import json
import os


def group_excel():
    """根据运营商与归属地查询导出任务，生成运营商统计表"""
    print('分组开始')
    # 生成要筛选文件路径
    file = os.path.join(file_path, excel_statistics_name)

    # 检查文件是否存在
    if not os.path.exists(file):
        print(f"错误：文件 {file} 不存在")
        return

    try:
        result = set_excel_data(file)
        if not result:
            print("警告：Excel文件中没有数据")
            return

        total = len(result)
        print(f"共读取到 {total} 条记录")

        # 分组并统计数量
        operator_count = group_excel_num(result, operator_key, total)
        province_count = group_excel_nums(result, operator_key, province_key)

        data_list = {
            operator_key: operator_count,
            province_key: province_count
        }

        # 生成excel表格
        mk_excel(data_list)
        print('分组结束')

    except Exception as e:
        print(f"处理过程中发生错误：{str(e)}")


def mk_excel(data_list: dict):
    """生成Excel文件；参数data_list为数据集合"""
    # 生成要筛选文件路径
    output_filename = excel_statistics_name.split('.')[0] + "统计.xlsx"
    file = os.path.join(file_path, output_filename)

    try:
        wb = Workbook()
        # 删除默认工作表
        if wb.active:
            wb.remove(wb.active)

        # 遍历字典列表
        for sheet_name, data in data_list.items():
            if not data:  # 检查数据是否为空
                print(f"警告：工作表 {sheet_name} 没有数据")
                continue

            # 创建工作表
            sales_sheet = wb.create_sheet(sheet_name)

            # 写入表头（字典的 keys）
            headers = list(data[0].keys())
            sales_sheet.append(headers)

            # 写入数据（字典的 values）
            for row in data:
                sales_sheet.append(list(row.values()))

        # 如果所有工作表都被跳过，至少创建一个空工作表
        if not wb.worksheets:
            wb.create_sheet("空数据")

        wb.save(file)
        print(f"Excel文件已保存至：{file}")

    except Exception as e:
        print(f"保存Excel文件时发生错误：{str(e)}")


def set_excel_data(file: str) -> list:
    """获取excel表格数据"""
    try:
        wb = load_workbook(file)
        sheet = wb.active

        # 检查工作表是否为空
        if sheet.max_row < 2:
            print("警告：Excel文件没有数据行")
            return []

        # 获取表头
        headers = [cell.value for cell in sheet[1]]

        # 检查表头是否有效
        if not any(headers):
            print("警告：Excel文件表头为空")
            return []

        # 从第二行开始获取表格内容并生成字典
        result = []
        for row in sheet.iter_rows(min_row=2, values_only=True):
            # 跳过完全空白的行
            if not any(row):
                continue
            row_dict = dict(zip(headers, row))
            result.append(row_dict)

        wb.close()  # 显式关闭工作簿
        return result

    except Exception as e:
        print(f"读取Excel文件时发生错误：{str(e)}")
        return []


def group_excel_data(data: list, key: str) -> dict:
    """根据key进行分组，参数data为字典列表数据，参数key为字段名称"""
    if not data:
        return {}

    group_data = defaultdict(list)  # 使用defaultdict简化代码
    for user in data:
        if key in user and user[key] is not None:  # 检查key是否存在且不为None
            my_key = user[key]
            group_data[my_key].append(user)

    return dict(group_data)  # 转换回普通dict


def group_excel_num(data: list, key: str, total: int) -> list:
    """根据key进行统计，参数data为字典列表数据，参数key为字段名称，参数total为字典列表总数"""
    if not data or total == 0:
        return []

    # 分组
    operators = group_excel_data(data, key)

    # 指定字段进行分组，并统计总数
    counts = Counter(item[key] for item in data if key in item and item[key] is not None)

    result = []
    case_number_list = []
    sql = "SELECT case_number,involved_amount FROM anti_fraud_case_new WHERE suspect_phone_number REGEXP '"

    for prefix, count in counts.items():
        if prefix not in operators:
            continue

        # 获取号码信息
        phone_numbers = [phone.get('号码', '') for phone in operators[prefix] if phone.get('号码')]
        if not phone_numbers:
            continue

        phone_str = '|'.join(phone_numbers)
        my_sql = sql + phone_str + "'"

        try:
            # 查询SQL结果（保持原有逻辑不变）
            sql_result = query_sql(my_sql)

            # 查询金额数据并求和
            amount_list = []
            for amount in sql_result:
                case_number = amount.get('case_number')
                if case_number and case_number not in case_number_list:
                    case_number_list.append(case_number)
                    involved_amount = amount.get('involved_amount', 0)
                    # 确保金额是数值类型
                    if isinstance(involved_amount, (int, float)):
                        amount_list.append(involved_amount)
                    elif isinstance(involved_amount, str) and involved_amount.replace('.', '').isdigit():
                        amount_list.append(float(involved_amount))

            amount_total = round((sum(amount_list) / 10000), 2) if amount_list else 0

            result.append({
                key: prefix,
                '总数': count,
                '占比': round((count / total) * 100, 2),
                '号码': phone_str,
                '总金额': amount_total
            })

        except Exception as e:
            print(f"查询SQL时发生错误：{str(e)}")
            # 即使SQL查询失败，也添加基本统计信息
            result.append({
                key: prefix,
                '总数': count,
                '占比': round((count / total) * 100, 2),
                '号码': phone_str,
                '总金额': 0
            })

    return result


def group_excel_nums(data: list, key1: str, key2: str) -> list:
    """根据key进行统计，参数data为字典列表数据，参数key1、参数key2为分组名称"""
    if not data:
        return []

    grouped = defaultdict(list)
    for item in data:
        # 检查key是否存在
        if key1 in item and key2 in item and item[key1] is not None and item[key2] is not None:
            key = (item[key1], item[key2])  # 以元组作为复合键
            grouped[key].append(item)

    result = []
    for key, value in grouped.items():
        phone_numbers = [phone.get('号码', '') for phone in value if phone.get('号码')]
        result.append({
            key1: key[0],
            key2: key[1],
            '总数': len(value),
            '号码': ','.join(phone_numbers)  # 修复：逗号和点的位置
        })

    # 排序
    result = sorted(result, key=lambda x: (x[key1], x[key2]))
    return result


def get_phone_nums_day(date_query: str):
    """获取指定日期的电话号码数据"""
    if not date_query:
        print("错误：日期查询参数为空")
        return {}

    my_sql = f'''
           select *
           from anti_fraud_case_new afcnt 
           where insert_day = '{date_query}'
       '''
    try:
        data_list = query_sql(my_sql)
        return handl_operator_phone(data_list)
    except Exception as e:
        print(f"查询日期数据时发生错误：{str(e)}")
        return {}


def get_phone_nums_month(month: str):
    """获取指定月份的电话号码数据"""
    if not month:
        print("错误：月份查询参数为空")
        return {}

    my_sql = f'''
           select *
           from anti_fraud_case_new afcnt 
           where DATE_FORMAT(insert_day, '%Y-%m') = '{month}'
       '''
    try:
        data_list = query_sql(my_sql)
        return handl_operator_phone(data_list)
    except Exception as e:
        print(f"查询月份数据时发生错误：{str(e)}")
        return {}


def handl_operator_phone(data_list: list):
    """处理运营商电话数据"""
    if not data_list:
        return {}

    phone_list = []
    for dl in data_list:
        phone_info = dl.get('suspect_phone_info')

        # 检查phone_info是否有效
        if not phone_info or phone_info is None:
            continue

        try:
            # 尝试解析JSON
            parsed_phones = json.loads(phone_info)
            if isinstance(parsed_phones, list):
                phone_list.extend(parsed_phones)
        except (json.JSONDecodeError, TypeError) as e:
            print(f"解析JSON时发生错误：{str(e)}, 数据：{phone_info}")
            continue

    if not phone_list:
        return {}

    # 分组数据
    group_data = group_excel_data(phone_list, 'operator')

    data_amount = {}
    for key, value in group_data.items():
        phone_numbers = [phone.get('phone', '') for phone in value if phone.get('phone')]
        if phone_numbers:
            data_amount[key] = '|'.join(phone_numbers)

    return data_amount


if __name__ == "__main__":
    # 执行分组功能
    group_excel()