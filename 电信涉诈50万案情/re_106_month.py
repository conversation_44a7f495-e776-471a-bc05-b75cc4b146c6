import re
import pandas as pd
from util import file_path, query_sql


def get_106_df_month(month: str):
    """
    提取包含106开头短信端口号的反欺诈案例数据

    Args:
        month (str): 查询月份，格式为'YYYY-MM'，如果为空则查询所有数据
    """
    matten_106_list = []

    # SQL查询逻辑保持不变
    if month:
        my_sql = f'''
                select *
                from anti_fraud_case_new afcnt 
                where DATE_FORMAT(entry_start_time, '%Y-%m') = '{month}'
            '''
    else:
        my_sql = f'''
                    select *
                    from anti_fraud_case_new afcnt
                '''

    data_list = query_sql(my_sql)

    # 预编译正则表达式，提高性能
    pattern_106 = re.compile(r'[^\d\s]106\d+')
    pattern_106_phone = re.compile(r'106\d+')
    pattern_1062 = re.compile(r'106\d+')

    for dl in data_list:
        # 安全获取字段值
        zheng = str(dl.get('brief_case_description', ''))
        phone = str(dl.get('suspect_phone_number', ''))

        # 提取106开头的号码
        zheng_list = pattern_106.findall(zheng)
        phone_list = pattern_106_phone.findall(phone)
        matten_106 = zheng_list + phone_list

        if not matten_106:
            continue

        # 处理匹配到的106号码
        md106 = []
        for m in matten_106:
            matten_1062_list = pattern_1062.findall(m)
            if matten_1062_list:
                matten_1062 = matten_1062_list[0]
                # 去重并验证长度
                if matten_1062 not in md106 and 11 <= len(matten_1062) <= 20:
                    md106.append(matten_1062)

        # 如果找到有效的106号码，添加到结果中
        if md106:
            # 将号码列表转换为逗号分隔的字符串
            new_numbers = ','.join(md106)

            # 处理已存在的号码字段
            existing_numbers = dl.get('正则匹配短信端口号', '')
            if existing_numbers:
                dl['正则匹配短信端口号'] = existing_numbers + ',' + new_numbers
            else:
                dl['正则匹配短信端口号'] = new_numbers

            matten_106_list.append(dl)

    # 生成输出文件
    if matten_106_list:
        output_file = f"{file_path}/全国案情_106正则匹配短信端口号V2.xlsx"
        df = pd.DataFrame(matten_106_list)

        try:
            df.to_excel(output_file, sheet_name='sheet1', index=False)
            print(f"成功处理 {len(matten_106_list)} 条包含106号码的记录")
            print(f"文件已保存至: {output_file}")
        except Exception as e:
            print(f"保存文件时出错: {e}")
    else:
        print("未找到包含106号码的记录")


if __name__ == "__main__":
    get_106_df_month('2025-05')