from util import engine, text, file_path, excel_statistics_name, query_sql, date_query
from openpyxl import load_workbook
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def excel_up():
    """根据每日运营商与归属地查询导出任务更新suspect_phone_info字段"""
    logger.info('更新开始')

    try:
        # 生成要筛选文件路径
        file = file_path + '/' + excel_statistics_name
        data = set_excel_data(file)

        if not data:
            logger.warning('Excel文件中没有数据')
            return

        total_count = len(data)
        logger.info(f'共需要处理 {total_count} 条记录')

        for i, da in enumerate(data, 1):
            try:
                # 数据验证
                if not da.get('号码'):
                    logger.warning(f'第{i}行：号码为空，跳过处理')
                    continue

                phone = str(da['号码']).strip()
                operator = da.get('运营商(携号转网的以最终运营商为准)', '').strip()
                province = da.get('归属省', '').strip()
                city = da.get('归属市', '').strip()

                logger.info(f'{i}/{total_count}: 处理号码 {phone}')

                # 查询SQL保持不变
                sql = ("select id,suspect_phone_info from anti_fraud_case_new where "
                       f"DATE_FORMAT(insert_day,'%Y-%m-%d')='{date_query}' and "
                       f"suspect_phone_number!='' and suspect_phone_number like '%{phone}%'")

                result = query_sql(sql)

                if not result:
                    logger.debug(f'号码 {phone} 没有找到匹配的记录')
                    continue

                # 批量处理查询结果
                update_records(result, phone, operator, province, city)

            except Exception as e:
                logger.error(f'处理第{i}行数据时出错: {str(e)}')
                continue

    except Exception as e:
        logger.error(f'excel_up执行出错: {str(e)}')
        raise
    finally:
        logger.info('更新结束')


def update_records(records, phone, operator, province, city):
    """批量更新记录"""
    new_phone_info = {
        "phone": phone,
        "operator": operator,
        "province": province,
        "city": city
    }

    for rs in records:
        try:
            id = rs['id']
            info = rs['suspect_phone_info']

            # 解析现有的suspect_phone_info
            suspect_phone_info = []
            if info:
                try:
                    suspect_phone_info = json.loads(info)
                    if not isinstance(suspect_phone_info, list):
                        suspect_phone_info = []
                except (json.JSONDecodeError, TypeError) as e:
                    logger.warning(f'ID {id} 的suspect_phone_info格式错误，重置为空列表: {str(e)}')
                    suspect_phone_info = []

            # 添加新信息
            suspect_phone_info.append(new_phone_info)

            # 去重逻辑保持不变
            deduplicated = {item['phone']: item for item in suspect_phone_info}.values()
            suspect_phone_info = list(deduplicated)

            # 更新SQL保持不变
            up_sql = (f"update anti_fraud_case_new set suspect_phone_info='"
                      f"{json.dumps(suspect_phone_info, ensure_ascii=False, indent=2)}' "
                      f"where id={id}")

            with engine.connect() as connection:
                result = connection.execute(text(up_sql))
                connection.commit()

        except Exception as e:
            logger.error(f'更新ID {rs.get("id", "unknown")} 时出错: {str(e)}')
            continue


def set_excel_data(file: str) -> list:
    """获取excel表格数据"""
    try:
        wb = load_workbook(file)
        sheet = wb.active

        # 检查是否有数据
        if sheet.max_row < 2:
            logger.warning('Excel文件没有数据行')
            return []

        # 获取表头
        headers = [cell.value for cell in sheet[1]]

        # 验证必要的列是否存在
        required_columns = ['号码']
        missing_columns = [col for col in required_columns if col not in headers]
        if missing_columns:
            raise ValueError(f'Excel文件缺少必要的列: {missing_columns}')

        # 从第二行开始获取表格内容并生成字典
        result = []
        for row_num, row in enumerate(sheet.iter_rows(min_row=2, values_only=True), 2):
            row_data = dict(zip(headers, row))
            # 跳过完全空白的行
            if all(value is None or str(value).strip() == '' for value in row):
                continue
            result.append(row_data)

        logger.info(f'成功读取Excel文件，共{len(result)}条有效记录')
        return result

    except FileNotFoundError:
        logger.error(f'文件不存在: {file}')
        raise
    except Exception as e:
        logger.error(f'读取Excel文件时出错: {str(e)}')
        raise


if __name__ == "__main__":
    excel_up()