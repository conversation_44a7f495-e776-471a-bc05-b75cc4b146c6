import pandas as pd
import re
from datetime import datetime
import os
import glob
import logging
from sqlalchemy import create_engine, text
from typing import List, Optional

# ==================== 配置部分 ====================
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 从 util 导入配置（必须存在）
try:
    from util import FILE_PATH as file_path, EXCEL_CHECK_NAME as excel_check_name, DATE_QUERY as date_query, query_sql, engine
except ImportError as e:
    raise RuntimeError("请确保 util.py 存在并包含必要的配置") from e

# 表头映射
HEADER_MAPPING = {
    '案件编号': 'case_number',
    '嫌疑人手机号': 'suspect_phone_number',
    # 可按需添加更多字段
}

TARGET_TABLE = 'anti_fraud_case_new_2025_temp'
BATCH_SIZE = 1000
PHONE_PATTERN = re.compile(r'^1\d{10}$')


# ==================== 工具函数 ====================
def deduplicate_phones(phone_str: Optional[str]) -> str:
    """去重电话号码"""
    if not phone_str or pd.isna(phone_str):
        return ''
    phones = [p.strip() for p in str(phone_str).split(',') if p.strip()]
    return ','.join(list(dict.fromkeys(phones)))  # 保持顺序去重


def extract_valid_phones(phone_str: Optional[str]) -> List[str]:
    """提取有效的手机号码"""
    if not phone_str or pd.isna(phone_str):
        return []
    numbers = [num.strip() for num in str(phone_str).split(',') if num.strip()]
    return [num for num in numbers if PHONE_PATTERN.match(num) and not num.startswith('12')]

# ==================== 核心类 ====================
class FraudDataProcessor:
    def __init__(self, file_path, excel_check_name):
        self.file_path = file_path
        self.excel_check_name = excel_check_name
        self.file_path_full = os.path.join(self.file_path, self.excel_check_name)

    def _auto_find_excel(self):
        """自动查找Excel文件"""
        logger.info("开始自动查找Excel文件...")
        files = glob.glob(os.path.join(self.file_path, "*.xlsx")) + glob.glob(os.path.join(self.file_path, "*.xls"))
        if not files:
            raise FileNotFoundError(f"未找到Excel文件在目录: {self.file_path}")
        files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        selected = os.path.basename(files[0])
        logger.info(f"自动选择文件: {selected}")
        return selected

    def validate_config(self):
        """验证配置有效性"""
        if not os.path.exists(os.path.join(self.file_path, self.excel_check_name)):
            raise FileNotFoundError(f"指定的Excel文件不存在: {self.excel_check_name}")
        try:
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.info("数据库连接正常")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise

    def import_data(self):
        """导入数据到数据库"""
    logger.info("开始导入数据...")
    file_path_full = os.path.join(self.file_path, self.excel_check_name)

    # 读取Excel
    df = pd.read_excel(file_path_full, sheet_name=0)
    logger.info(f"读取到 {len(df)} 条记录")

    # 关键修复：处理 NaN 值
    # 对所有列进行 NaN 值处理
    for col in df.columns:
        if df[col].dtype == 'object':
            df[col] = df[col].fillna('')  # 字符串类型用空字符串填充
        else:
            df[col] = df[col].fillna(0)   # 数值类型用0填充

    # 清洗数据
    if '联系电话' in df.columns:
        df['联系电话'] = df['联系电话'].apply(deduplicate_phones)

    # 映射字段
    mapped_columns = [col for col in df.columns if col in HEADER_MAPPING]
    db_df = df[mapped_columns].rename(columns=HEADER_MAPPING)

    # 再次确保没有 NaN 值传递到数据库
    db_df = db_df.fillna('')

    # 插入SQL
    insert_cols = ', '.join(db_df.columns)
    values_placeholders = ', '.join([f':{col}' for col in db_df.columns])
    update_clause = ', '.join([f'{col} = VALUES({col})' for col in db_df.columns if col != 'case_number'])
    sql = f"""
    INSERT INTO {TARGET_TABLE} ({insert_cols})
    VALUES ({values_placeholders})
    ON DUPLICATE KEY UPDATE {update_clause}
    """

    # 批量写入
    success_count = 0
    error_count = 0
    with self.engine.connect() as conn:
        with conn.begin():
            for i in range(0, len(db_df), BATCH_SIZE):
                batch = db_df.iloc[i:i + BATCH_SIZE]
                
                # 转换为字典列表，确保没有 NaN
                batch_records = []
                for _, row in batch.iterrows():
                    record = {}
                    for col in db_df.columns:
                        value = row[col]
                        # 额外检查：如果仍有 NaN，转换为空字符串
                        if pd.isna(value):
                            record[col] = ''
                        else:
                            record[col] = value
                    batch_records.append(record)
                
                try:
                    conn.execute(text(sql), batch_records)
                    success_count += len(batch_records)
                    logger.info(f"成功导入批次: {i//BATCH_SIZE + 1}, 记录数: {len(batch_records)}")
                except Exception as e:
                    logger.warning(f"批量写入失败: {e}")
                    error_count += len(batch_records)
                    
    logger.info(f"导入完成: 成功 {success_count}, 失败 {error_count}")

    def export_phones(self):
        """导出有效手机号"""
        logger.info("开始导出嫌疑人手机号...")
        sql = f"SELECT suspect_phone_number FROM {TARGET_TABLE}"
        result = query_sql(sql)
        all_numbers = []

        for row in result:
            phone_str = row[0] if isinstance(row, tuple) else row
            all_numbers.extend(extract_valid_phones(phone_str))

        unique_numbers = list(set(all_numbers))
        logger.info(f"共提取到 {len(unique_numbers)} 个有效手机号")

        output_file = os.path.join(self.file_path, f"outputphone{self.date_query}.xlsx")
        pd.DataFrame(unique_numbers, columns=['suspect_phone_number']).to_excel(output_file, index=False, header=False)
        logger.info(f"手机号已保存至: {output_file}")


# ==================== 主函数 ====================
def main():
    logger.info("== 反欺诈手机号提取器启动 ==")
    processor = FraudDataProcessor()
    try:
        processor.import_data()
        processor.export_phones()
        logger.info("✅ 数据处理完成")
    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}", exc_info=True)


if __name__ == "__main__":
    main()