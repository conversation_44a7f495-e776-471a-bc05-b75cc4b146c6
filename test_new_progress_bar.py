#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的AI智能分析进度条
"""

import sys
import os
import time
import threading

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.idea/Ready/test/50万趋势'))

# 导入AI服务类
from ai_service import AIAnalysisProgressBar

def test_progress_bar_standalone():
    """测试独立的进度条功能"""
    print("🧪 测试新版AI智能分析进度条")
    print("=" * 60)
    
    # 创建进度条实例
    progress_bar = AIAnalysisProgressBar()
    
    # 模拟提示词长度
    prompt_length = 731
    
    # 开始进度显示
    progress_bar.start(prompt_length)
    
    # 模拟各个阶段的进展
    stages_duration = [3, 2, 5, 20]  # 各阶段持续时间
    
    for stage_idx, duration in enumerate(stages_duration):
        print(f"\n开始阶段 {stage_idx + 1}")
        
        # 模拟阶段进展
        for i in range(duration * 2):  # 每0.5秒更新一次
            progress_bar.display_progress()
            time.sleep(0.5)
            
            # 在适当时机切换到下一阶段
            if i >= duration * 2 - 1 and stage_idx < len(stages_duration) - 1:
                progress_bar.next_stage()
                break
    
    # 完成进度条
    progress_bar.complete()

def test_ai_service_integration():
    """测试与AI服务的集成"""
    print("\n🔬 测试AI服务集成")
    print("=" * 60)
    
    # 导入AI服务
    from ai_service import AIService
    
    # 创建AI服务实例
    ai_service = AIService()
    
    # 模拟分析请求
    test_prompt = """
    请分析以下电信诈骗案件数据：
    
    案件概况：
    - 案件类型：网络购物诈骗
    - 涉案金额：50万元
    - 受害人数：15人
    - 案发时间：2024年1月-3月
    
    通话记录分析：
    - 电信运营商分布：电信40%，联通35%，移动25%
    - 通话时长分析：平均通话时长15分钟
    - 通话频次：高峰期为晚上8-10点
    
    请提供专业的案件分析报告。
    """ * 3  # 增加长度以测试进度条
    
    print("开始AI分析测试...")
    
    # 调用AI分析（这会触发新的进度条）
    try:
        result = ai_service.analyze_with_ollama(test_prompt)
        print(f"\n📊 分析结果预览:")
        print(f"结果长度: {len(result)} 字符")
        print(f"结果开头: {result[:200]}...")
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {str(e)}")
        print("这可能是因为Ollama服务未启动，但进度条功能已经展示")

def test_progress_bar_visual_demo():
    """视觉演示进度条效果"""
    print("\n🎨 进度条视觉效果演示")
    print("=" * 60)
    
    progress_bar = AIAnalysisProgressBar()
    progress_bar.start(731)  # 模拟提示词长度
    
    # 演示完整流程
    total_time = 35  # 总演示时间35秒
    update_interval = 0.5
    
    for i in range(int(total_time / update_interval)):
        elapsed = i * update_interval
        
        # 根据时间自动推进阶段
        if elapsed >= 3 and progress_bar.current_stage == 0:
            progress_bar.next_stage()
        elif elapsed >= 5 and progress_bar.current_stage == 1:
            progress_bar.next_stage()
        elif elapsed >= 10 and progress_bar.current_stage == 2:
            progress_bar.next_stage()
            
        progress_bar.display_progress()
        time.sleep(update_interval)
    
    progress_bar.complete()

def main():
    """主测试函数"""
    print("🚀 AI智能分析进度条测试套件")
    print("=" * 60)
    
    # 测试选项
    tests = [
        ("1", "独立进度条测试", test_progress_bar_standalone),
        ("2", "视觉效果演示", test_progress_bar_visual_demo),
        ("3", "AI服务集成测试", test_ai_service_integration),
    ]
    
    print("\n可用测试:")
    for code, name, _ in tests:
        print(f"{code}. {name}")
    
    choice = input("\n请选择测试 (1-3, 或 'all' 运行全部): ").strip().lower()
    
    if choice == 'all':
        for _, name, test_func in tests:
            print(f"\n{'='*20} {name} {'='*20}")
            test_func()
            print("\n" + "="*60)
    else:
        for code, name, test_func in tests:
            if choice == code:
                print(f"\n{'='*20} {name} {'='*20}")
                test_func()
                break
        else:
            print("❌ 无效选择")
            return
    
    print("\n✅ 测试完成!")

if __name__ == "__main__":
    main()
