#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试删除重复提示信息后的干净输出
"""

import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.idea/Ready/test/50万趋势'))

def test_clean_output():
    """测试删除重复提示信息后的干净输出"""
    print("🧪 测试删除重复提示信息后的干净输出")
    print("=" * 60)
    
    try:
        from ai_service import AIAnalysisProgressBar
        
        # 创建进度条实例
        progress_bar = AIAnalysisProgressBar()
        progress_bar.start(772, "趋势分析")
        
        # 模拟完整的分析流程
        total_time = 10  # 总演示时间10秒
        update_interval = 0.3
        
        for i in range(int(total_time / update_interval)):
            elapsed = i * update_interval
            
            # 根据时间自动推进阶段
            if elapsed >= 3 and progress_bar.current_stage == 0:
                progress_bar.next_stage()
            elif elapsed >= 5 and progress_bar.current_stage == 1:
                progress_bar.next_stage()
            elif elapsed >= 7 and progress_bar.current_stage == 2:
                progress_bar.next_stage()
                
            progress_bar.display_progress()
            time.sleep(update_interval)
        
        # 完成进度条
        progress_bar.complete()
        
        print("\n✅ 干净输出测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 测试删除重复提示信息")
    print("=" * 60)
    
    print("\n🗑️ 已删除的重复提示信息:")
    print("❌ 📝 提示词生成成功，长度: 772 字符")
    print("❌ 🤖 开始AI分析，启动Ollama本地模型")
    
    print("\n✅ 现在的干净输出:")
    test_clean_output()
    
    print("\n🎯 最终效果对比:")
    print("\n【修改前 - 有重复提示】:")
    print("🤖 正在执行趋势分析...")
    print("📝 提示词生成成功，长度: 772 字符")
    print("🤖 开始AI分析，启动Ollama本地模型")
    print("--------------------------------------------------")
    print("🤖 正在执行趋势分析...")
    print("✅ 提示词已生成      |█████████████████████████| 100%")
    print("...")
    
    print("\n【修改后 - 干净简洁】:")
    print("--------------------------------------------------")
    print("🤖 正在执行趋势分析...")
    print("✅ 提示词已生成      |█████████████████████████| 100% 用时> 03.0s（提示词长度772字符）")
    print("✅ 正在准备数据      |█████████████████████████| 100% 用时> 02.0s")
    print("✅ 正在启动模型      |█████████████████████████| 100% 用时> 05.0s（启动Ollama本地模型）")
    print("✅ AI分析处理中      |█████████████████████████| 100% 用时> 122.7s")
    print("")
    print("✅ AI分析完成，总耗时: 132.7秒")
    print("--------------------------------------------------")
    
    print("\n🎉 删除重复提示信息完成！")
    print("现在输出更加干净简洁，没有重复信息。")
