"""
数据库访问层
提供安全的数据库操作接口
"""
import logging
from typing import List, Dict, Any, Optional
from contextlib import contextmanager
from sqlalchemy import create_engine, text
from sqlalchemy.engine import Engine
from sqlalchemy.exc import SQLAlchemyError

from .config import config
from .models import QueryResult

# 配置日志
logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, connection_url: Optional[str] = None):
        """
        初始化数据库管理器
        
        Args:
            connection_url: 数据库连接URL，如果为None则使用配置中的URL
        """
        self.connection_url = connection_url or config.db.connection_url
        self.engine: Optional[Engine] = None
        self._initialize_engine()
    
    def _initialize_engine(self):
        """初始化数据库引擎"""
        try:
            engine_config = {
                'pool_size': 10,
                'max_overflow': 20,
                'pool_timeout': 30,
                'pool_recycle': 3600,
                'echo': False  # 设置为True可以看到SQL日志
            }
            
            self.engine = create_engine(self.connection_url, **engine_config)
            logger.info("数据库引擎初始化成功")
        except Exception as e:
            logger.error(f"数据库引擎初始化失败: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        if not self.engine:
            raise RuntimeError("数据库引擎未初始化")
        
        connection = None
        try:
            connection = self.engine.connect()
            yield connection
        except Exception as e:
            logger.error(f"数据库连接错误: {e}")
            if connection:
                connection.rollback()
            raise
        finally:
            if connection:
                connection.close()
    
    def execute_query(self, sql: str, params: Optional[Dict[str, Any]] = None) -> QueryResult:
        """
        执行查询语句
        
        Args:
            sql: SQL查询语句
            params: 查询参数
            
        Returns:
            QueryResult: 查询结果
        """
        if not sql or not sql.strip():
            logger.warning("SQL语句为空")
            return QueryResult(success=False, error_message="SQL语句为空")
        
        try:
            with self.get_connection() as connection:
                result = connection.execute(text(sql), params or {})
                rows = result.fetchall()
                columns = result.keys()
                
                # 将结果转换为字典列表
                data = [dict(zip(columns, row)) for row in rows]
                
                logger.debug(f"查询成功，返回 {len(data)} 条记录")
                return QueryResult(data=data, success=True)
                
        except SQLAlchemyError as e:
            error_msg = f"数据库查询错误: {e}"
            logger.error(error_msg)
            return QueryResult(success=False, error_message=error_msg)
        except Exception as e:
            error_msg = f"查询过程中发生未知错误: {e}"
            logger.error(error_msg)
            return QueryResult(success=False, error_message=error_msg)
    
    def execute_queries(self, queries: List[str], params_list: Optional[List[Dict[str, Any]]] = None) -> List[QueryResult]:
        """
        批量执行查询语句
        
        Args:
            queries: SQL查询语句列表
            params_list: 参数列表，与queries一一对应
            
        Returns:
            List[QueryResult]: 查询结果列表
        """
        if not queries:
            return []
        
        if params_list and len(params_list) != len(queries):
            raise ValueError("参数列表长度与查询列表长度不匹配")
        
        results = []
        for i, query in enumerate(queries):
            params = params_list[i] if params_list else None
            result = self.execute_query(query, params)
            results.append(result)
        
        return results
    
    def execute_insert(self, sql: str, data: Dict[str, Any]) -> bool:
        """
        执行插入语句
        
        Args:
            sql: SQL插入语句
            data: 要插入的数据
            
        Returns:
            bool: 操作是否成功
        """
        if not sql or not sql.strip():
            logger.warning("SQL语句为空")
            return False
        
        if not data:
            logger.warning("插入数据为空")
            return False
        
        try:
            with self.get_connection() as connection:
                connection.execute(text(sql), data)
                connection.commit()
                logger.info("数据插入成功")
                return True
                
        except SQLAlchemyError as e:
            logger.error(f"数据库插入错误: {e}")
            return False
        except Exception as e:
            logger.error(f"插入过程中发生未知错误: {e}")
            return False
    
    def test_connection(self) -> bool:
        """
        测试数据库连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            with self.get_connection() as connection:
                connection.execute(text("SELECT 1"))
                logger.info("数据库连接测试成功")
                return True
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            return False
    
    def get_table_info(self, table_name: str) -> QueryResult:
        """
        获取表结构信息
        
        Args:
            table_name: 表名
            
        Returns:
            QueryResult: 表结构信息
        """
        sql = f"DESC {table_name}"
        return self.execute_query(sql)
    
    def get_table_count(self, table_name: str, condition: str = "") -> int:
        """
        获取表记录数
        
        Args:
            table_name: 表名
            condition: 查询条件（WHERE子句，不包含WHERE关键字）
            
        Returns:
            int: 记录数
        """
        sql = f"SELECT COUNT(*) as count FROM {table_name}"
        if condition:
            sql += f" WHERE {condition}"
        
        result = self.execute_query(sql)
        if result.success and result.data:
            return result.data[0].get('count', 0)
        return 0
    
    def close(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()
            logger.info("数据库连接已关闭")


# 全局数据库管理器实例
db_manager = DatabaseManager()


def get_db() -> DatabaseManager:
    """获取数据库管理器实例"""
    return db_manager


def execute_query(sql: str, params: Optional[Dict[str, Any]] = None) -> QueryResult:
    """
    执行单个查询的便捷函数
    
    Args:
        sql: SQL查询语句
        params: 查询参数
        
    Returns:
        QueryResult: 查询结果
    """
    return db_manager.execute_query(sql, params)


def execute_queries(queries: List[str], params_list: Optional[List[Dict[str, Any]]] = None) -> List[QueryResult]:
    """
    批量执行查询的便捷函数
    
    Args:
        queries: SQL查询语句列表
        params_list: 参数列表
        
    Returns:
        List[QueryResult]: 查询结果列表
    """
    return db_manager.execute_queries(queries, params_list)


def test_database_connection() -> bool:
    """
    测试数据库连接的便捷函数
    
    Returns:
        bool: 连接是否成功
    """
    return db_manager.test_connection()


if __name__ == "__main__":
    # 测试数据库连接
    if test_database_connection():
        print("数据库连接正常")
        
        # 获取主要表的信息
        main_tables = ['anti_fraud_case_new']
        for table_name in main_tables:
            print(f"\n=== 表 {table_name} 的信息 ===")
            
            # 获取表结构
            table_info = db_manager.get_table_info(table_name)
            if table_info.success:
                print("表结构:")
                for field in table_info.data:
                    print(f"  {field['Field']}: {field['Type']}")
            
            # 获取记录数
            count = db_manager.get_table_count(table_name)
            print(f"总记录数: {count}")
    else:
        print("数据库连接失败")
