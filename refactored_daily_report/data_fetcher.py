"""
数据获取模块
负责从数据库获取报告所需的各种数据
"""
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from .database import execute_query, execute_queries
from .queries import QueryTemplates, get_query, format_excluded_apps
from .config import config
from .models import QueryResult

logger = logging.getLogger(__name__)


class DataFetcher:
    """数据获取器"""
    
    def __init__(self, date_query: Optional[str] = None):
        """
        初始化数据获取器
        
        Args:
            date_query: 查询日期，格式：YYYY-MM-DD
        """
        self.date_query = date_query or config.report.date_query
        self.templates = QueryTemplates()
        
    def get_basic_statistics(self) -> Dict[str, QueryResult]:
        """
        获取基础统计数据
        
        Returns:
            Dict[str, QueryResult]: 基础统计数据
        """
        logger.info("开始获取基础统计数据")
        
        queries = {}
        for key, template in self.templates.BASIC_STATS.items():
            queries[key] = get_query(template, date=self.date_query)
        
        # 批量执行查询
        query_list = list(queries.values())
        results = execute_queries(query_list)
        
        # 将结果映射回对应的键
        result_dict = {}
        for i, key in enumerate(queries.keys()):
            result_dict[key] = results[i]
        
        logger.info(f"基础统计数据获取完成，共 {len(result_dict)} 项")
        return result_dict
    
    def get_case_type_statistics(self) -> QueryResult:
        """
        获取案件类型统计数据
        
        Returns:
            QueryResult: 案件类型统计结果
        """
        logger.info("开始获取案件类型统计数据")
        
        query = get_query(self.templates.CASE_TYPE_STATS, date=self.date_query)
        result = execute_query(query)
        
        logger.info(f"案件类型统计数据获取完成，共 {len(result.data) if result.success else 0} 条记录")
        return result
    
    def get_app_statistics(self) -> Dict[str, QueryResult]:
        """
        获取APP统计数据
        
        Returns:
            Dict[str, QueryResult]: APP统计数据
        """
        logger.info("开始获取APP统计数据")
        
        excluded_apps = format_excluded_apps(config.query.excluded_app_names)
        
        queries = {}
        for key, template in self.templates.APP_STATS.items():
            queries[key] = get_query(
                template, 
                date=self.date_query,
                excluded_apps=excluded_apps
            )
        
        # 批量执行查询
        query_list = list(queries.values())
        results = execute_queries(query_list)
        
        # 将结果映射回对应的键
        result_dict = {}
        for i, key in enumerate(queries.keys()):
            result_dict[key] = results[i]
        
        logger.info(f"APP统计数据获取完成，共 {len(result_dict)} 项")
        return result_dict
    
    def get_operator_statistics(self) -> Dict[str, Dict[str, QueryResult]]:
        """
        获取运营商统计数据
        
        Returns:
            Dict[str, Dict[str, QueryResult]]: 运营商统计数据
        """
        logger.info("开始获取运营商统计数据")
        
        result_dict = {}
        
        for operator in config.query.operators.keys():
            operator_data = {}
            
            # 基础统计
            basic_query = get_query(
                self.templates.OPERATOR_STATS["operator_basic"],
                operator=operator,
                date=self.date_query
            )
            operator_data["basic"] = execute_query(basic_query)
            
            # 高金额案件统计
            thresholds = config.query.amount_thresholds
            
            # 50万-100万
            high_50_query = get_query(
                self.templates.OPERATOR_STATS["operator_high_amount"],
                operator=operator,
                date=self.date_query,
                min_amount=thresholds["high_50"],
                max_amount=thresholds["high_100"]
            )
            operator_data["high_50"] = execute_query(high_50_query)
            
            # 100万-1000万
            high_100_query = get_query(
                self.templates.OPERATOR_STATS["operator_high_amount"],
                operator=operator,
                date=self.date_query,
                min_amount=thresholds["high_100"],
                max_amount=thresholds["high_1000"]
            )
            operator_data["high_100"] = execute_query(high_100_query)
            
            # 1000万以上
            high_1000_query = get_query(
                self.templates.OPERATOR_STATS["operator_highest_amount"],
                operator=operator,
                date=self.date_query,
                min_amount=thresholds["high_1000"]
            )
            operator_data["high_1000"] = execute_query(high_1000_query)
            
            result_dict[operator] = operator_data
        
        logger.info(f"运营商统计数据获取完成，共 {len(result_dict)} 个运营商")
        return result_dict
    
    def get_phone_location_data(self, phone_patterns: Dict[str, str]) -> Dict[str, List[QueryResult]]:
        """
        获取手机号归属地数据
        
        Args:
            phone_patterns: 手机号正则表达式模式，格式：{运营商: 正则表达式}
            
        Returns:
            Dict[str, List[QueryResult]]: 手机号归属地数据
        """
        logger.info("开始获取手机号归属地数据")
        
        result_dict = {}
        thresholds = config.query.amount_thresholds
        
        for operator, pattern in phone_patterns.items():
            operator_locations = []
            
            # 50万-100万
            query_50 = get_query(
                self.templates.PHONE_LOCATION_QUERIES["location_by_amount"],
                date=self.date_query,
                min_amount=thresholds["high_50"],
                max_amount=thresholds["high_100"],
                phone_pattern=pattern
            )
            operator_locations.append(execute_query(query_50))
            
            # 100万-1000万
            query_100 = get_query(
                self.templates.PHONE_LOCATION_QUERIES["location_by_amount"],
                date=self.date_query,
                min_amount=thresholds["high_100"],
                max_amount=thresholds["high_1000"],
                phone_pattern=pattern
            )
            operator_locations.append(execute_query(query_100))
            
            # 1000万以上
            query_1000 = get_query(
                self.templates.PHONE_LOCATION_QUERIES["location_highest_amount"],
                date=self.date_query,
                min_amount=thresholds["high_1000"],
                phone_pattern=pattern
            )
            operator_locations.append(execute_query(query_1000))
            
            result_dict[operator] = operator_locations
        
        logger.info(f"手机号归属地数据获取完成，共 {len(result_dict)} 个运营商")
        return result_dict
    
    def get_trend_data(self) -> Dict[str, QueryResult]:
        """
        获取趋势分析数据
        
        Returns:
            Dict[str, QueryResult]: 趋势数据
        """
        logger.info("开始获取趋势分析数据")
        
        queries = {}
        for key, template in self.templates.TREND_QUERIES.items():
            queries[key] = get_query(template, date=self.date_query)
        
        # 批量执行查询
        query_list = list(queries.values())
        results = execute_queries(query_list)
        
        # 将结果映射回对应的键
        result_dict = {}
        for i, key in enumerate(queries.keys()):
            result_dict[key] = results[i]
        
        logger.info(f"趋势分析数据获取完成，共 {len(result_dict)} 项")
        return result_dict
    
    def get_monitoring_data(self) -> QueryResult:
        """
        获取重点监测数据
        
        Returns:
            QueryResult: 重点监测数据
        """
        logger.info("开始获取重点监测数据")
        
        # 将监测号码转换为正则表达式模式
        monitoring_pattern = config.report.intensive_monitoring.replace(",", "|")
        
        query = get_query(
            self.templates.MONITORING_QUERIES["monitoring_cases"],
            date=self.date_query,
            monitoring_pattern=monitoring_pattern
        )
        
        result = execute_query(query)
        logger.info(f"重点监测数据获取完成，共 {len(result.data) if result.success else 0} 条记录")
        return result
    
    def get_high_amount_data(self) -> Dict[str, QueryResult]:
        """
        获取高金额案件数据
        
        Returns:
            Dict[str, QueryResult]: 高金额案件数据
        """
        logger.info("开始获取高金额案件数据")
        
        amount_50 = config.query.amount_thresholds["high_50"]
        
        queries = {
            "total": get_query(
                self.templates.HIGH_AMOUNT_QUERIES["high_amount_cases"],
                date=self.date_query,
                amount=amount_50
            ),
            "with_operator": get_query(
                self.templates.HIGH_AMOUNT_QUERIES["high_amount_with_operator"],
                date=self.date_query,
                amount=amount_50
            ),
            "telecom": get_query(
                self.templates.HIGH_AMOUNT_QUERIES["high_amount_telecom"],
                date=self.date_query,
                amount=amount_50
            ),
            "case_numbers": get_query(
                self.templates.HIGH_AMOUNT_QUERIES["case_numbers_high_amount"],
                date=self.date_query,
                amount=config.query.amount_thresholds["high_1000"]  # 1000万以上的案件编号
            )
        }
        
        # 批量执行查询
        query_list = list(queries.values())
        results = execute_queries(query_list)
        
        # 将结果映射回对应的键
        result_dict = {}
        for i, key in enumerate(queries.keys()):
            result_dict[key] = results[i]
        
        logger.info(f"高金额案件数据获取完成，共 {len(result_dict)} 项")
        return result_dict
    
    def get_chart_data(self, day: int) -> Dict[str, Dict[str, QueryResult]]:
        """
        获取图表数据
        
        Args:
            day: 当前日期的天数，用于判断使用月度还是周度数据
            
        Returns:
            Dict[str, Dict[str, QueryResult]]: 图表数据
        """
        logger.info("开始获取图表数据")
        
        # 根据日期判断使用哪种查询
        query_type = "monthly_trend" if day > 6 else "weekly_trend"
        template = self.templates.CHART_QUERIES[query_type]
        
        result_dict = {}
        for operator in config.query.operators.keys():
            query = get_query(
                template,
                operator=operator,
                date=self.date_query
            )
            result_dict[operator] = execute_query(query)
        
        logger.info(f"图表数据获取完成，共 {len(result_dict)} 个运营商")
        return result_dict
    
    def get_monthly_statistics(self) -> Dict[str, QueryResult]:
        """
        获取月度统计数据
        
        Returns:
            Dict[str, QueryResult]: 月度统计数据
        """
        logger.info("开始获取月度统计数据")
        
        queries = {}
        for key, template in self.templates.MONTHLY_STATS.items():
            queries[key] = get_query(template, date=self.date_query)
        
        # 批量执行查询
        query_list = list(queries.values())
        results = execute_queries(query_list)
        
        # 将结果映射回对应的键
        result_dict = {}
        for i, key in enumerate(queries.keys()):
            result_dict[key] = results[i]
        
        logger.info(f"月度统计数据获取完成，共 {len(result_dict)} 项")
        return result_dict
