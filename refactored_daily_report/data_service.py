"""
数据服务模块
整合数据获取和处理功能，提供高级数据分析接口
"""
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from .data_fetcher import DataFetcher
from .data_processor import DataProcessor
from .models import ReportData
from .config import config

logger = logging.getLogger(__name__)


class DataService:
    """数据服务类"""
    
    def __init__(self, date_query: Optional[str] = None):
        """
        初始化数据服务
        
        Args:
            date_query: 查询日期，格式：YYYY-MM-DD
        """
        self.date_query = date_query or config.report.date_query
        self.fetcher = DataFetcher(self.date_query)
        self.processor = DataProcessor()
    
    def get_phone_patterns(self) -> Dict[str, str]:
        """
        获取手机号正则表达式模式
        这里需要从外部模块获取，暂时返回空字典
        
        Returns:
            Dict[str, str]: 手机号模式字典
        """
        # TODO: 从excel_statistics模块获取手机号模式
        # 这里需要调用get_phone_nums_day函数获取各运营商的手机号模式
        return {
            "电信": "",
            "联通": "",
            "移动": "",
            "广电": "",
            "虚商": ""
        }
    
    def get_new_app_data(self) -> List[Dict[str, Any]]:
        """
        获取新增APP数据
        这里需要从外部模块获取，暂时返回空列表
        
        Returns:
            List[Dict[str, Any]]: 新增APP数据
        """
        # TODO: 从app_statistics模块获取新增APP数据
        # 这里需要调用app_name_statistics函数
        return []
    
    def get_106_data(self) -> int:
        """
        获取106相关数据
        这里需要从外部模块获取，暂时返回0
        
        Returns:
            int: 106相关案件数量
        """
        # TODO: 从re_106_95_96模块获取106数据
        # 这里需要调用get_106_df函数
        return 0
    
    def generate_report_data(self) -> ReportData:
        """
        生成完整的报告数据
        
        Returns:
            ReportData: 完整的报告数据
        """
        logger.info(f"开始生成 {self.date_query} 的报告数据")
        
        try:
            # 1. 获取基础统计数据
            logger.info("获取基础统计数据...")
            basic_data = self.fetcher.get_basic_statistics()
            basic_stats = self.processor.process_basic_statistics(basic_data)
            
            # 2. 获取案件类型统计
            logger.info("获取案件类型统计数据...")
            case_type_result = self.fetcher.get_case_type_statistics()
            case_types = self.processor.process_case_types(case_type_result)
            
            # 3. 获取APP统计数据
            logger.info("获取APP统计数据...")
            app_data = self.fetcher.get_app_statistics()
            new_apps = self.get_new_app_data()
            app_stats, app_total_count, app_total_amount, new_app_count = self.processor.process_app_statistics(app_data, new_apps)
            
            # 4. 获取运营商统计数据
            logger.info("获取运营商统计数据...")
            operator_data = self.fetcher.get_operator_statistics()
            operators = self.processor.process_operator_statistics(operator_data)
            
            # 5. 获取手机号归属地数据
            logger.info("获取手机号归属地数据...")
            phone_patterns = self.get_phone_patterns()
            if any(phone_patterns.values()):  # 只有在有模式的情况下才获取
                location_data = self.fetcher.get_phone_location_data(phone_patterns)
                phone_locations = self.processor.process_phone_locations(location_data)
            else:
                phone_locations = {}
            
            # 6. 获取趋势数据
            logger.info("获取趋势数据...")
            trend_data = self.fetcher.get_trend_data()
            trends = self.processor.process_trend_data(trend_data)
            
            # 更新电信趋势数据（从运营商数据中获取）
            if "电信" in operators:
                trends["telecom"].current_count = operators["电信"].total_count
                trends["telecom"].calculate_trend()
            
            # 7. 获取图表数据
            logger.info("获取图表数据...")
            date_obj = datetime.strptime(self.date_query, "%Y-%m-%d")
            chart_data = self.fetcher.get_chart_data(date_obj.day)
            charts = self.processor.process_chart_data(chart_data)
            
            # 8. 获取重点监测数据
            logger.info("获取重点监测数据...")
            monitoring_result = self.fetcher.get_monitoring_data()
            monitoring = self.processor.process_monitoring_data(monitoring_result)
            
            # 9. 获取高金额案件数据
            logger.info("获取高金额案件数据...")
            high_amount_data = self.fetcher.get_high_amount_data()
            case_numbers, high_50_count, high_50_operator_count, high_50_telecom_count = self.processor.process_high_amount_data(high_amount_data)
            
            # 10. 获取月度统计数据
            logger.info("获取月度统计数据...")
            monthly_data = self.fetcher.get_monthly_statistics()
            monthly_total, monthly_telecom, monthly_telecom_percentage = self.processor.process_monthly_statistics(monthly_data)
            
            # 11. 构建完整报告数据
            logger.info("构建完整报告数据...")
            all_data = {
                "basic_stats": basic_stats,
                "operators": operators,
                "app_stats": app_stats,
                "app_total_count": app_total_count,
                "app_total_amount": app_total_amount,
                "new_app_count": new_app_count,
                "case_types": case_types,
                "phone_locations": phone_locations,
                "trends": trends,
                "charts": charts,
                "monitoring": monitoring,
                "case_numbers": case_numbers,
                "high_50_count": high_50_count,
                "high_50_operator_count": high_50_operator_count,
                "high_50_telecom_count": high_50_telecom_count,
                "monthly_total": monthly_total,
                "monthly_telecom": monthly_telecom,
                "monthly_telecom_percentage": monthly_telecom_percentage
            }
            
            report_data = self.processor.build_report_data(all_data)
            
            logger.info("报告数据生成完成")
            return report_data
            
        except Exception as e:
            logger.error(f"生成报告数据时发生错误: {e}")
            raise
    
    def get_summary_statistics(self) -> Dict[str, Any]:
        """
        获取摘要统计信息
        
        Returns:
            Dict[str, Any]: 摘要统计信息
        """
        logger.info("获取摘要统计信息")
        
        try:
            # 获取基础统计
            basic_data = self.fetcher.get_basic_statistics()
            basic_stats = self.processor.process_basic_statistics(basic_data)
            
            # 获取运营商统计
            operator_data = self.fetcher.get_operator_statistics()
            operators = self.processor.process_operator_statistics(operator_data)
            
            # 构建摘要
            summary = {
                "date": self.date_query,
                "total_cases": basic_stats.total_count,
                "total_amount": basic_stats.total_amount,
                "url_cases": basic_stats.url_count,
                "url_percentage": basic_stats.url_percentage,
                "app_cases": basic_stats.app_count,
                "app_percentage": basic_stats.app_percentage,
                "sms_cases": basic_stats.sms_count,
                "sms_percentage": basic_stats.sms_percentage,
                "international_cases": basic_stats.international_count,
                "operators": {
                    name: {
                        "count": stats.total_count,
                        "amount": stats.total_amount,
                        "percentage": stats.percentage
                    }
                    for name, stats in operators.items()
                }
            }
            
            logger.info("摘要统计信息获取完成")
            return summary
            
        except Exception as e:
            logger.error(f"获取摘要统计信息时发生错误: {e}")
            return {}
    
    def validate_data_completeness(self, report_data: ReportData) -> Dict[str, bool]:
        """
        验证数据完整性
        
        Args:
            report_data: 报告数据
            
        Returns:
            Dict[str, bool]: 验证结果
        """
        logger.info("验证数据完整性")
        
        validation_results = {
            "basic_stats": report_data.case_stats.total_count > 0,
            "operators": len(report_data.operators) > 0,
            "case_types": len(report_data.case_types) > 0,
            "app_stats": len(report_data.app_stats) > 0,
            "trends": report_data.daily_trend.current_count >= 0,
            "monitoring": report_data.monitoring.case_count >= 0
        }
        
        all_valid = all(validation_results.values())
        validation_results["overall"] = all_valid
        
        if not all_valid:
            logger.warning(f"数据完整性验证失败: {validation_results}")
        else:
            logger.info("数据完整性验证通过")
        
        return validation_results
    
    def get_data_quality_report(self) -> Dict[str, Any]:
        """
        获取数据质量报告
        
        Returns:
            Dict[str, Any]: 数据质量报告
        """
        logger.info("生成数据质量报告")
        
        try:
            report_data = self.generate_report_data()
            validation_results = self.validate_data_completeness(report_data)
            
            quality_report = {
                "date": self.date_query,
                "validation_results": validation_results,
                "data_summary": {
                    "total_cases": report_data.case_stats.total_count,
                    "operators_count": len(report_data.operators),
                    "case_types_count": len(report_data.case_types),
                    "app_count": len(report_data.app_stats),
                    "new_apps_count": report_data.new_app_count,
                    "high_amount_cases": len(report_data.high_amount_cases),
                    "monitoring_cases": report_data.monitoring.case_count
                },
                "data_quality_score": sum(validation_results.values()) / len(validation_results) * 100
            }
            
            logger.info(f"数据质量报告生成完成，质量得分: {quality_report['data_quality_score']:.1f}%")
            return quality_report
            
        except Exception as e:
            logger.error(f"生成数据质量报告时发生错误: {e}")
            return {
                "date": self.date_query,
                "error": str(e),
                "data_quality_score": 0
            }


def create_data_service(date_query: Optional[str] = None) -> DataService:
    """
    创建数据服务实例的工厂函数
    
    Args:
        date_query: 查询日期
        
    Returns:
        DataService: 数据服务实例
    """
    return DataService(date_query)


if __name__ == "__main__":
    # 测试数据服务
    service = create_data_service()
    
    # 获取摘要统计
    summary = service.get_summary_statistics()
    print("摘要统计:")
    print(f"日期: {summary.get('date')}")
    print(f"总案件数: {summary.get('total_cases')}")
    print(f"总金额: {summary.get('total_amount')} 亿元")
    
    # 获取数据质量报告
    quality_report = service.get_data_quality_report()
    print(f"\n数据质量得分: {quality_report.get('data_quality_score', 0):.1f}%")
