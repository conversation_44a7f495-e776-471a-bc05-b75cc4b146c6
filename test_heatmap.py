#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试热力图功能
横坐标：APP名称，纵坐标：日期
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime, timedelta
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

def create_sample_data():
    """创建示例数据"""
    # 生成30天的日期
    dates = [datetime.now() - timedelta(days=i) for i in range(29, -1, -1)]
    
    # TOP30 APP名称
    apps = [
        '微信', '支付宝', '抖音', '淘宝', '京东', 
        '美团', '拼多多', '百度', '腾讯视频', '爱奇艺',
        '网易云音乐', 'QQ', '滴滴出行', '饿了么', '知乎',
        '小红书', '快手', '哔哩哔哩', '今日头条', '携程',
        '高德地图', '钉钉', '企业微信', '陌陌', '探探',
        'soul', '世纪佳缘', '珍爱网', '马上金融', '借呗'
    ]
    
    # 生成随机数据
    data = []
    for date in dates:
        for app in apps:
            # 随机生成案件数量，某些APP更容易出现案件
            if app in ['微信', '支付宝', '抖音']:
                case_count = np.random.poisson(3)  # 高风险APP
            elif app in ['淘宝', '京东', '美团']:
                case_count = np.random.poisson(2)  # 中风险APP
            else:
                case_count = np.random.poisson(1)  # 低风险APP
            
            if case_count > 0:  # 只记录有案件的数据
                data.append({
                    'date_col': date,
                    'app_name': app,
                    'case_count': case_count
                })
    
    return pd.DataFrame(data)

def create_heatmap_chart(df):
    """创建热力图 - 横坐标为APP名称，纵坐标为日期"""
    if df is None or df.empty:
        print("⚠️ 无数据可绘制图表")
        return

    # 创建透视表：APP名称为列，日期为行，案件数量为值
    pivot_df = df.pivot_table(
        index='date_col',
        columns='app_name',
        values='case_count',
        fill_value=0
    )

    # 按总案件数排序列（APP）
    column_totals = pivot_df.sum().sort_values(ascending=False)
    pivot_df = pivot_df[column_totals.index]

    # 创建图表
    fig, ax = plt.subplots(figsize=(24, 12))
    fig.patch.set_facecolor('#F8F9FA')

    # 创建热力图
    im = ax.imshow(
        pivot_df.values,
        cmap='YlOrRd',
        aspect='auto',
        interpolation='nearest'
    )

    # 设置坐标轴
    ax.set_xticks(range(len(pivot_df.columns)))
    ax.set_yticks(range(len(pivot_df.index)))
    
    # 设置标签
    ax.set_xticklabels(pivot_df.columns, rotation=45, ha='right', fontsize=10)
    ax.set_yticklabels([d.strftime('%m-%d') for d in pivot_df.index], fontsize=10)

    # 在每个格子中添加数值
    for i in range(len(pivot_df.index)):
        for j in range(len(pivot_df.columns)):
            value = pivot_df.iloc[i, j]
            if value > 0:
                text_color = 'white' if value > pivot_df.values.max() * 0.6 else 'black'
                ax.text(j, i, f'{int(value)}', ha='center', va='center', 
                       color=text_color, fontsize=8, fontweight='bold')

    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax, shrink=0.8)
    cbar.set_label('案件数量', rotation=270, labelpad=20, fontsize=14)

    # 美化图表
    ax.set_title('APP涉案数量热力图 - TOP30\n(横坐标：APP名称，纵坐标：日期)',
                 fontsize=20, fontweight='bold', pad=30, color='#2C3E50')
    ax.set_xlabel('APP名称', fontsize=16, color='#2C3E50')
    ax.set_ylabel('日期', fontsize=16, color='#2C3E50')

    # 移除边框
    for spine in ax.spines.values():
        spine.set_visible(False)

    plt.tight_layout()
    plt.show()

    print(f"📊 数据统计:")
    print(f"  - 日期范围: {len(pivot_df)} 天")
    print(f"  - APP数量: {len(pivot_df.columns)} 个")
    print(f"  - 总案件数: {pivot_df.values.sum()} 起")
    print(f"  - 最高单日单APP案件数: {pivot_df.values.max()} 起")

def main():
    """主函数"""
    print("🔥 APP涉案数量热力图测试")
    print("=" * 50)
    
    # 创建示例数据
    print("📊 正在生成示例数据...")
    df = create_sample_data()
    
    print(f"✅ 数据生成完成，共 {len(df)} 条记录")
    
    # 创建热力图
    print("🔥 正在生成热力图...")
    create_heatmap_chart(df)

if __name__ == "__main__":
    main()
