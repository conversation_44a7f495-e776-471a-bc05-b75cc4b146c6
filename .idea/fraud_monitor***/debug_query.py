#!/usr/bin/env python3
"""
调试数据库查询问题 - 对比不同查询方式的结果
用于找出为什么系统查询缺少8月4日数据
"""

import sys
import pandas as pd
from datetime import datetime, date, timedelta
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from core.database import DatabaseManager
    from sqlalchemy import text
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


def debug_datetime_queries():
    """调试不同时间查询方式"""
    print("🔍 调试时间查询问题")
    print("=" * 60)

    db_manager = DatabaseManager()
    if not db_manager.connect_database():
        print("❌ 数据库连接失败")
        return

    # 测试时间范围
    start_datetime = datetime(2025, 8, 4, 0, 0, 0)
    end_datetime = datetime(2025, 8, 6, 23, 59, 59, 999999)

    print(f"🔍 测试时间范围:")
    print(f"   开始: {start_datetime}")
    print(f"   结束: {end_datetime}")
    print()

    with db_manager.engine.connect() as conn:

        # 方法1：系统当前使用的查询（优化版）
        print("📋 方法1：系统优化查询")
        print("-" * 40)

        query1 = text("""
            SELECT 
                DATE(insert_day) as date,
                COUNT(*) as count,
                MIN(insert_day) as min_time,
                MAX(insert_day) as max_time
            FROM anti_fraud_case_new
            WHERE insert_day >= :start_datetime 
                AND insert_day <= :end_datetime
                AND app_name IS NOT NULL
                AND app_name != ''
                AND app_name != 'NULL'
                AND involved_amount > 0
            GROUP BY DATE(insert_day)
            ORDER BY DATE(insert_day)
        """)

        result1 = conn.execute(query1, {
            'start_datetime': start_datetime,
            'end_datetime': end_datetime
        })

        rows1 = result1.fetchall()

        for row in rows1:
            print(f"   {row[0]}: {row[1]} 条记录, {row[2]} 至 {row[3]}")

        if not rows1:
            print("   ⚠️ 无数据")

        print()

        # 方法2：传统DATE查询
        print("📋 方法2：传统DATE查询")
        print("-" * 40)

        query2 = text("""
            SELECT 
                DATE(insert_day) as date,
                COUNT(*) as count,
                MIN(insert_day) as min_time,
                MAX(insert_day) as max_time
            FROM anti_fraud_case_new
            WHERE DATE(insert_day) BETWEEN '2025-08-04' AND '2025-08-06'
                AND app_name IS NOT NULL
                AND app_name != ''
                AND involved_amount > 0
            GROUP BY DATE(insert_day)
            ORDER BY DATE(insert_day)
        """)

        result2 = conn.execute(query2)
        rows2 = result2.fetchall()

        for row in rows2:
            print(f"   {row[0]}: {row[1]} 条记录, {row[2]} 至 {row[3]}")

        if not rows2:
            print("   ⚠️ 无数据")

        print()

        # 方法3：简化查询（无额外条件）
        print("📋 方法3：简化查询")
        print("-" * 40)

        query3 = text("""
            SELECT 
                DATE(insert_day) as date,
                COUNT(*) as count,
                MIN(insert_day) as min_time,
                MAX(insert_day) as max_time
            FROM anti_fraud_case_new
            WHERE insert_day >= :start_datetime 
                AND insert_day <= :end_datetime
            GROUP BY DATE(insert_day)
            ORDER BY DATE(insert_day)
        """)

        result3 = conn.execute(query3, {
            'start_datetime': start_datetime,
            'end_datetime': end_datetime
        })

        rows3 = result3.fetchall()

        for row in rows3:
            print(f"   {row[0]}: {row[1]} 条记录, {row[2]} 至 {row[3]}")

        if not rows3:
            print("   ⚠️ 无数据")

        print()

        # 方法4：检查8月4日的具体条件
        print("📋 方法4：8月4日条件检查")
        print("-" * 40)

        query4 = text("""
            SELECT 
                '总记录' as type,
                COUNT(*) as count
            FROM anti_fraud_case_new
            WHERE DATE(insert_day) = '2025-08-04'

            UNION ALL

            SELECT 
                'app_name不为空' as type,
                COUNT(*) as count
            FROM anti_fraud_case_new
            WHERE DATE(insert_day) = '2025-08-04'
                AND app_name IS NOT NULL
                AND app_name != ''

            UNION ALL

            SELECT 
                'app_name不为NULL字符串' as type,
                COUNT(*) as count
            FROM anti_fraud_case_new
            WHERE DATE(insert_day) = '2025-08-04'
                AND app_name IS NOT NULL
                AND app_name != ''
                AND app_name != 'NULL'

            UNION ALL

            SELECT 
                'involved_amount>0' as type,
                COUNT(*) as count
            FROM anti_fraud_case_new
            WHERE DATE(insert_day) = '2025-08-04'
                AND app_name IS NOT NULL
                AND app_name != ''
                AND app_name != 'NULL'
                AND involved_amount > 0
        """)

        result4 = conn.execute(query4)
        rows4 = result4.fetchall()

        for row in rows4:
            print(f"   {row[0]}: {row[1]} 条记录")

        print()

        # 方法5：检查时间字段类型和格式
        print("📋 方法5：时间字段检查")
        print("-" * 40)

        query5 = text("""
            SELECT 
                insert_day,
                TYPEOF(insert_day) as type,
                DATE(insert_day) as date_part,
                TIME(insert_day) as time_part
            FROM anti_fraud_case_new
            WHERE DATE(insert_day) = '2025-08-04'
            LIMIT 5
        """)

        try:
            result5 = conn.execute(query5)
            rows5 = result5.fetchall()

            for row in rows5:
                print(f"   原始: {row[0]}, 类型: {row[1]}, 日期: {row[2]}, 时间: {row[3]}")
        except Exception as e:
            print(f"   ⚠️ 时间字段检查失败: {e}")

            # 简化版时间检查
            query5_simple = text("""
                SELECT 
                    insert_day,
                    DATE(insert_day) as date_part
                FROM anti_fraud_case_new
                WHERE DATE(insert_day) = '2025-08-04'
                LIMIT 5
            """)

            result5_simple = conn.execute(query5_simple)
            rows5_simple = result5_simple.fetchall()

            for row in rows5_simple:
                print(f"   原始: {row[0]}, 日期: {row[1]}")


def compare_query_methods():
    """对比系统查询和简易查询的差异"""
    print("\n🔍 对比查询方法差异")
    print("=" * 60)

    db_manager = DatabaseManager()

    # 使用系统的查询方法
    print("📋 系统查询方法:")
    print("-" * 30)

    try:
        # 模拟系统调用
        df_system = db_manager.query_comprehensive_data(days=3)

        if df_system is not None and not df_system.empty:
            date_range = df_system['insert_day']
            print(f"   记录数: {len(df_system)}")
            print(f"   时间范围: {date_range.min()} 至 {date_range.max()}")

            # 按日期分组
            daily_counts = df_system.groupby(df_system['insert_day'].dt.date).size()
            for date, count in daily_counts.items():
                print(f"   {date}: {count} 条记录")
        else:
            print("   ⚠️ 系统查询无数据")

    except Exception as e:
        print(f"   ❌ 系统查询失败: {e}")

    print()

    # 使用直接SQL查询
    print("📋 直接SQL查询:")
    print("-" * 30)

    try:
        with db_manager.engine.connect() as conn:
            direct_query = text("""
                SELECT 
                    case_number,
                    app_name as final_app_name,
                    involved_amount,
                    insert_day,
                    occurrence_area
                FROM anti_fraud_case_new
                WHERE DATE(insert_day) BETWEEN '2025-08-04' AND '2025-08-06'
                    AND app_name IS NOT NULL
                    AND app_name != ''
                    AND involved_amount > 0
                ORDER BY insert_day
            """)

            result = conn.execute(direct_query)
            rows = result.fetchall()

            if rows:
                df_direct = pd.DataFrame(rows,
                                         columns=['case_number', 'final_app_name', 'involved_amount', 'insert_day',
                                                  'occurrence_area'])

                print(f"   记录数: {len(df_direct)}")
                print(f"   时间范围: {df_direct['insert_day'].min()} 至 {df_direct['insert_day'].max()}")

                # 按日期分组
                daily_counts = df_direct.groupby(df_direct['insert_day'].dt.date).size()
                for date, count in daily_counts.items():
                    print(f"   {date}: {count} 条记录")
            else:
                print("   ⚠️ 直接查询无数据")

    except Exception as e:
        print(f"   ❌ 直接查询失败: {e}")


def main():
    """主函数"""
    print("🚀 数据库查询调试工具")
    print("发现问题：8月4日有数据但系统查询缺失")
    print("=" * 60)

    # 调试不同查询方式
    debug_datetime_queries()

    # 对比查询方法
    compare_query_methods()

    print("\n💡 调试完成")
    print("请检查上述输出，找出系统查询遗漏8月4日数据的原因")


if __name__ == "__main__":
    main()