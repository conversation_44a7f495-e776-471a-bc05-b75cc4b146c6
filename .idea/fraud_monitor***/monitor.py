"""
APP涉案监控系统 - 完整修复版主监控控制器
统一时间管理，设置数据库管理器引用，整合显示对齐
修复所有语法错误，提高代码质量，确保资金对比分析正常调用
"""
import pandas as pd
from datetime import datetime, date, timedelta
from typing import Dict, Any, Optional, List
from pathlib import Path

# 核心模块
try:
    from core.database import DatabaseManager
    from core.data_processor import DataProcessor
except ImportError:
    print("⚠️ 核心模块导入失败，请检查 core 目录")
    raise

# 分析器模块
try:
    from analyzers.financial import FinancialAnalyzer
    from analyzers.patterns import PatternDetector
    from analyzers.network import AppExplosionAnalyzer
    from analyzers.risk import RiskAssessmentAnalyzer
except ImportError as e:
    print(f"⚠️ 分析器模块导入失败: {e}")
    raise

# 报告模块
try:
    from reports.generator import ReportGenerator
except ImportError:
    class ReportGenerator:
        def __init__(self):
            pass

        def generate_comprehensive_report(self, df, analysis_results):
            return None

# 配置
try:
    from config import QUERY_CONFIG, DEBUG_CONFIG, PERFORMANCE_CONFIG, TIME_QUERY_CONFIG
except ImportError:
    QUERY_CONFIG = {'default_days': 7}
    DEBUG_CONFIG = {'enable_debug': False}
    PERFORMANCE_CONFIG = {}
    TIME_QUERY_CONFIG = {'default_days': 7}

# 时间管理
try:
    from core.time_query_manager import get_current_time_config, get_current_datetime_range
except ImportError:
    def get_current_time_config():
        return {'start_date': date.today(), 'end_date': date.today(), 'days_count': 1}


    def get_current_datetime_range():
        today = datetime.now()
        return today, today


class FraudMonitor:
    """APP涉案监控系统主控制器 - 完整修复版"""

    def __init__(self):
        print("🔮 系统初始化启动中...")
        print("🚀 APP涉案监控系统v3.0 初始化...")

        try:
            self.db_manager = DatabaseManager()
            self.data_processor = DataProcessor(self.db_manager)
            self.report_generator = ReportGenerator()

            self.financial_analyzer = FinancialAnalyzer()
            self.pattern_detector = PatternDetector()
            self.network_analyzer = AppExplosionAnalyzer()
            self.risk_analyzer = RiskAssessmentAnalyzer()

            # 设置数据库管理器引用到网络分析器
            self.network_analyzer.set_db_manager(self.db_manager)

            print("✅ 系统初始化完成")
            print("✅ 数据库连接正常")
        except Exception as e:
            print(f"❌ 系统初始化失败: {e}")
            raise

    def run_comprehensive_analysis(self, days: int = None, use_cache: bool = True,
                                   generate_report: bool = True) -> Dict[str, Any]:
        """运行完整分析 - 统一使用配置默认天数"""
        if days is None:
            days = TIME_QUERY_CONFIG['default_days']

        print("🚀 启动完整分析")
        print(f"📅 分析周期: {days}天 | 💾 使用缓存: {'是' if use_cache else '否'}")
        print("=" * 60)

        try:
            # 1. 获取和处理数据
            df = self._get_and_process_data(days, use_cache)
            if df is None or df.empty:
                print("❌ 无法获取数据，分析终止")
                return {}

            # 2. 数据概览
            self._print_integrated_overview(df)

            # 3. 执行各项分析
            analysis_results = self._execute_ordered_analyses(df)

            # 4. 生成报告（可选）
            if generate_report:
                report_path = self._generate_comprehensive_report(df, analysis_results)
                if report_path:
                    analysis_results['report_file'] = report_path

            return analysis_results

        except Exception as e:
            print(f"⚠️ 完整分析失败: {e}")
            return {}

    def _get_and_process_data(self, days: int, use_cache: bool) -> Optional[pd.DataFrame]:
        """获取和处理数据"""
        try:
            print("📥 获取数据...")
            df = self.data_processor.get_comprehensive_data(days=days, use_cache=use_cache)

            if df is None or df.empty:
                print("❌ 无数据可用于分析")
                return None

            # 数据质量检查
            self._check_data_quality(df)

            return df

        except Exception as e:
            print(f"⚠️ 数据获取失败: {e}")
            return None

    def _check_data_quality(self, df: pd.DataFrame):
        """检查数据质量"""
        try:
            print(f"📊 数据质量检查:")
            print(f"   原始记录数: {len(df):,} 条")

            # 检查关键字段完整性
            key_fields = ['final_app_name', 'insert_day']
            for field in key_fields:
                if field in df.columns:
                    missing_count = df[field].isna().sum()
                    if missing_count > 0:
                        print(f"   ⚠️ {field} 字段缺失: {missing_count} 条")

            # 检查金额数据质量
            if 'involved_amount' in df.columns:
                valid_amounts = df['involved_amount'].dropna()
                valid_amounts = valid_amounts[valid_amounts > 0]
                print(
                    f"   💰 有效金额记录: {len(valid_amounts)} / {len(df)} ({len(valid_amounts) / len(df) * 100:.1f}%)")

        except Exception as e:
            print(f"⚠️ 数据质量检查失败: {e}")

    def _print_integrated_overview(self, df: pd.DataFrame):
        """打印整合的数据概览"""
        try:
            print("\n📋 数据概览")
            print("=" * 50)

            # 基础统计
            total_records = len(df)
            print(f"📊 总记录数: {total_records:,} 条")

            # 时间范围
            if 'insert_day' in df.columns:
                min_date = df['insert_day'].min()
                max_date = df['insert_day'].max()
                print(f"📅 时间范围: {min_date.strftime('%Y-%m-%d')} 至 {max_date.strftime('%Y-%m-%d')}")

            # APP统计
            if 'final_app_name' in df.columns:
                unique_apps = df['final_app_name'].nunique()
                print(f"📱 涉及APP: {unique_apps:,} 个")

            # 资金概览（整合显示，避免重复）
            if 'involved_amount' in df.columns:
                amount_data = df['involved_amount'].dropna()
                amount_data = amount_data[amount_data > 0]

                if not amount_data.empty:
                    print(f"\n💰 资金概览:")
                    print(f"  • 有效金额记录: {len(amount_data):,} 起")
                    print(f"  • 总涉案金额: {amount_data.sum():,.0f} 元 ({amount_data.sum() / 10000:.1f} 万)")
                    print(f"  • 平均损失: {amount_data.mean():,.0f} 元")
                    print(f"  • 最高损失: {amount_data.max():,.0f} 元")

            # 地域统计
            if 'occurrence_area' in df.columns:
                unique_areas = df['occurrence_area'].nunique()
                print(f"🗺️ 涉及地区: {unique_areas:,} 个")

        except Exception as e:
            print(f"⚠️ 概览显示失败: {e}")

    def _execute_ordered_analyses(self, df: pd.DataFrame) -> Dict[str, Any]:
        """按顺序执行各项分析 - 删除重复模块"""
        analysis_results = {}

        try:
            # 获取当前时间配置用于对比分析
            time_config = get_current_time_config()
            analysis_start = time_config['start_date']
            analysis_end = time_config['end_date']

            # 1. 资金分析（含对比分析）
            print("\n" + "=" * 60)
            if 'involved_amount' in df.columns:
                financial_result = self.financial_analyzer.analyze_with_comparison(
                    df, analysis_start, analysis_end, self.data_processor
                )
                if financial_result:
                    analysis_results['financial_analysis'] = financial_result
                    print("✅ 资金影响分析完成 (含对比分析)")
                else:
                    print("⚠️ 资金分析失败")
            else:
                print("⚠️ 跳过资金分析 - 缺少金额数据")

            # 2. 网络分析（包含新APP综合分析，这是唯一的新APP检测入口）
            print("\n" + "=" * 60)
            network_result = self.network_analyzer.analyze(df)
            if network_result:
                analysis_results['network_analysis'] = network_result

            # 3. 模式检测分析（只保留可疑模式检测，删除新APP检测）
            print("\n" + "=" * 60)
            pattern_result = self.pattern_detector.analyze(df)
            if pattern_result:
                analysis_results['pattern_analysis'] = pattern_result

            # 4. 风险评估
            print("\n" + "=" * 60)
            risk_result = self.risk_analyzer.analyze(df)
            if risk_result:
                analysis_results['risk_analysis'] = risk_result

        except Exception as e:
            print(f"⚠️ 分析执行失败: {e}")

        return analysis_results

    def _generate_comprehensive_report(self, df: pd.DataFrame, analysis_results: Dict[str, Any]) -> Optional[str]:
        """生成完整报告"""
        try:
            if not analysis_results:
                print("⚠️ 无分析结果，跳过报告生成")
                return None

            print("\n📊 正在生成完整报告...")
            report_path = self.report_generator.generate_comprehensive_report(df, analysis_results)

            if report_path:
                print(f"✅ 报告生成成功: {report_path}")
                return report_path
            else:
                print("⚠️ 报告生成失败")
                return None

        except Exception as e:
            print(f"⚠️ 报告生成失败: {e}")
            return None

    def run_new_app_detection(self, days: int = None) -> Dict[str, Any]:
        """运行新APP检测分析"""
        if days is None:
            days = TIME_QUERY_CONFIG['default_days']

        try:
            print("🔍 新APP涉诈检测")
            print("=" * 50)

            df = self.data_processor.get_comprehensive_data(days=days)
            if df is None or df.empty:
                print("❌ 无法获取数据")
                return {}

            result = self.pattern_detector.analyze(df)
            if result:
                print("✅ 新APP检测完成")
                return {'new_app_analysis': result}
            else:
                print("⚠️ 新APP检测失败")
                return {}

        except Exception as e:
            print(f"⚠️ 新APP检测失败: {e}")
            return {}

    def run_app_explosion_analysis(self, days: int = None) -> Dict[str, Any]:
        """运行APP爆发风险分析"""
        if days is None:
            days = TIME_QUERY_CONFIG['default_days']

        try:
            print("🎯 APP爆发风险预测")
            print("=" * 50)

            df = self.data_processor.get_comprehensive_data(days=days)
            if df is None or df.empty:
                print("❌ 无法获取数据")
                return {}

            result = self.network_analyzer.analyze(df)
            if result:
                print("✅ APP爆发风险分析完成")
                return {'explosion_analysis': result}
            else:
                print("⚠️ APP爆发风险分析失败")
                return {}

        except Exception as e:
            print(f"⚠️ APP爆发风险分析失败: {e}")
            return {}

    def run_risk_assessment(self, days: int = None) -> Dict[str, Any]:
        """运行风险评估分析"""
        if days is None:
            days = TIME_QUERY_CONFIG['default_days']

        try:
            print("🚨 高可疑APP检测")
            print("=" * 50)

            df = self.data_processor.get_comprehensive_data(days=days)
            if df is None or df.empty:
                print("❌ 无法获取数据")
                return {}

            result = self.risk_analyzer.analyze(df)
            if result:
                print("✅ 风险评估完成")
                return {'risk_assessment': result}
            else:
                print("⚠️ 风险评估失败")
                return {}

        except Exception as e:
            print(f"⚠️ 风险评估失败: {e}")
            return {}

    def run_victim_profile_analysis(self, days: int = None) -> Dict[str, Any]:
        """运行受害人画像分析"""
        if days is None:
            days = TIME_QUERY_CONFIG['default_days']

        try:
            print("👤 受害人画像分析")
            print("=" * 50)

            df = self.data_processor.get_comprehensive_data(days=days)
            if df is None or df.empty:
                print("❌ 无法获取数据")
                return {}

            # 这里可以调用受害人画像分析器
            # 暂时使用风险分析器的部分功能
            result = self.risk_analyzer.analyze(df)
            if result:
                # print("✅ 受害人画像分析完成")
                return {'victim_profile': result}
            else:
                print("⚠️ 受害人画像分析失败")
                return {}

        except Exception as e:
            print(f"⚠️ 受害人画像分析失败: {e}")
            return {}

    def generate_statistical_charts(self, days: int = None) -> Dict[str, Any]:
        """生成统计图表"""
        if days is None:
            days = TIME_QUERY_CONFIG['default_days']

        try:
            print("📊 App涉诈统计图表")
            print("=" * 50)

            df = self.data_processor.get_comprehensive_data(days=days)
            if df is None or df.empty:
                print("❌ 无法获取数据")
                return {}

            # 执行完整分析以获取所有数据
            analysis_results = self._execute_ordered_analyses(df)

            # 生成图表报告
            report_path = self._generate_comprehensive_report(df, analysis_results)

            if report_path:
                print(f"✅ 统计图表生成完成: {report_path}")
                return {
                    'charts_generated': True,
                    'report_path': report_path,
                    'analysis_results': analysis_results
                }
            else:
                print("⚠️ 图表生成失败")
                return {}

        except Exception as e:
            print(f"⚠️ 图表生成失败: {e}")
            return {}

    def run_financial_analysis(self, days: int = None) -> Dict[str, Any]:
        """运行资金分析"""
        if days is None:
            days = TIME_QUERY_CONFIG['default_days']

        try:
            print("💰 资金分析")
            print("=" * 50)

            df = self.data_processor.get_comprehensive_data(days=days)
            if df is None or df.empty:
                print("❌ 无法获取数据")
                return {}

            # 获取当前时间配置用于对比分析
            try:
                time_config = get_current_time_config()
                analysis_start = time_config['start_date']
                analysis_end = time_config['end_date']

                # 使用带对比的资金分析
                result = self.financial_analyzer.analyze_with_comparison(
                    df, analysis_start, analysis_end, self.data_processor
                )
            except ImportError:
                # 回退到普通分析
                result = self.financial_analyzer.analyze(df)

            if result:
                print("✅ 资金分析完成")
                return {'financial_analysis': result}
            else:
                print("⚠️ 资金分析失败")
                return {}

        except Exception as e:
            print(f"⚠️ 资金分析失败: {e}")
            return {}

    def _run_merged_suspicious_detection(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """运行统一的可疑模式检测 - 只调用一个方法避免重复"""
        try:
            # 只调用 patterns.py 中的检测方法，避免重复
            # 因为 patterns.py 的方法更完整，包含了所有检测逻辑
            suspicious_alerts = self.pattern_detector.detect_suspicious_patterns(df)

            # 转换为统一的字典格式
            all_alerts = []
            for alert in suspicious_alerts:
                if hasattr(alert, '__dict__'):
                    all_alerts.append({
                        'type': getattr(alert, 'type', '可疑模式'),
                        'app_name': getattr(alert, 'app_name', '未知'),
                        'severity': getattr(alert, 'severity', '中'),
                        'description': getattr(alert, 'description', '检测到可疑模式')
                    })
                elif isinstance(alert, dict):
                    all_alerts.append(alert)

            return all_alerts

        except Exception as e:
            print(f"⚠️ 可疑检测执行失败: {e}")
            return []

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            # 检查数据库连接
            database_connected = self.db_manager is not None and hasattr(self.db_manager, 'connection')

            # 检查各组件状态
            components = {
                'database_manager': 'ready' if self.db_manager else 'not_ready',
                'data_processor': 'ready' if self.data_processor else 'not_ready',
                'financial_analyzer': 'ready' if self.financial_analyzer else 'not_ready',
                'pattern_detector': 'ready' if self.pattern_detector else 'not_ready',
                'network_analyzer': 'ready' if self.network_analyzer else 'not_ready',
                'risk_analyzer': 'ready' if self.risk_analyzer else 'not_ready',
                'report_generator': 'ready' if self.report_generator else 'not_ready'
            }

            return {
                'database_connected': database_connected,
                'components': components,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}

    def close(self):
        """关闭系统资源"""
        try:
            if hasattr(self.db_manager, 'close'):
                self.db_manager.close()
            print("✅ 系统资源已释放")
        except Exception as e:
            print(f"⚠️ 资源释放时出现错误: {e}")