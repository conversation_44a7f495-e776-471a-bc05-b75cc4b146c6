"""
APP涉案监控系统 - 修复版交互式菜单
修复图表保存为空白文件的问题，确保正确保存图表内容
"""
import sys
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import numpy as np
import pandas as pd
from datetime import datetime
from pathlib import Path
from monitor import FraudMonitor
from config import EXPORT_DIR, COLORS, COLOR_PALETTE, CHART_CONFIG, TIME_QUERY_CONFIG


class FinalInteractiveMenu:
    """最终修复版交互式菜单系统 - 修复图表保存问题"""

    def __init__(self):
        self.monitor = None
        self.running = True
        self.export_dir = EXPORT_DIR
        self.colors = COLORS
        self.color_palette = COLOR_PALETTE

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
        plt.rcParams['axes.unicode_minus'] = False

    def start(self):
        """启动交互式菜单"""
        self.print_welcome()

        try:
            self.monitor = FraudMonitor()

            while self.running:
                self.show_main_menu()
                choice = self.get_user_input("请选择操作 (0-9): ")
                self.handle_menu_choice(choice)

        except KeyboardInterrupt:
            print("\n\n👋 感谢使用APP涉案监控系统！")
        except Exception as e:
            print(f"\n❌ 系统错误: {e}")
        finally:
            if self.monitor:
                self.monitor.close()

    def print_welcome(self):
        """打印欢迎信息"""
        welcome = f"""
==================================
🛡️ APP涉案监控系统 v3.0 - 智能分析版
默认分析周期: {TIME_QUERY_CONFIG['default_days']}天
==================================
        """
        print(welcome)

    def show_main_menu(self):
        """显示主菜单"""
        # 显示当前时间设置
        current_time_desc = self._get_current_time_description()

        menu = f"""
🎯 功能菜单 | 当前分析周期: {current_time_desc}
  1. 💰 App涉诈资金分析
  2. 🔍 新APP涉诈检测
  3. 🎯 APP爆发风险预测
  4. 🚨 高可疑APP检测
  5. 👤 受害人画像分析
  6. 🗺️ 受害人地域分布
  7. 🚀 App涉诈完整分析
  8. 📊 App涉诈统计图表
  9. ⚙️ 系统设置
  0. ❌ 退出系统
        """
        print(menu)

    def get_user_input(self, prompt: str) -> str:
        """获取用户输入"""
        try:
            return input(f"\n{prompt}").strip()
        except (EOFError, KeyboardInterrupt):
            raise KeyboardInterrupt

    def handle_menu_choice(self, choice: str):
        """处理菜单选择"""
        handlers = {
            '1': self.handle_financial_analysis,
            '2': self.handle_new_app_detection,
            '3': self.handle_app_explosion_prediction,
            '4': self.handle_suspicious_app_monitoring,
            '5': self.handle_victim_profile_analysis,
            '6': self.handle_geographic_analysis,
            '7': self.handle_comprehensive_analysis,
            '8': self.handle_statistical_charts,
            '9': self.handle_system_settings,
            '0': self.handle_exit
        }

        handler = handlers.get(choice)
        if handler:
            try:
                handler()
            except Exception as e:
                print(f"\n❌ 操作失败: {e}")
                input("\n按回车键继续...")
        else:
            print("\n❌ 无效选择，请重新输入")
            input("按回车键继续...")

    def handle_financial_analysis(self):
        """处理资金分析 - 使用统一时间管理"""
        print("\n💰 资金分析")
        print("━" * 60)

        try:
            days = self._get_analysis_days()
            time_desc = self._get_current_time_description()

            print(f"📅 分析周期: {time_desc}")
            print("🔄 正在执行资金分析...")

            result = self.monitor.run_financial_analysis(days=days)
            if result and result.get('financial_analysis'):
                print("✅ 资金分析完成")
            else:
                print("⚠️ 资金分析执行失败")

        except Exception as e:
            print(f"⚠️ 分析过程中出现错误: {e}")

        input("\n按回车键继续...")

    def handle_new_app_detection(self):
        """处理新APP检测 - 使用统一时间管理"""
        print("\n🔍 新APP检测")
        print("━" * 60)

        try:
            days = self._get_analysis_days()
            time_desc = self._get_current_time_description()

            print(f"📅 分析周期: {time_desc}")
            print("🔄 正在执行新APP检测...")

            df = self.monitor.data_processor.get_comprehensive_data(days=days)
            if df is None or df.empty:
                print("❌ 无法获取数据")
                input("\n按回车键继续...")
                return

            # 只调用一次检测方法，避免重复显示
            result = self.monitor.network_analyzer.detect_new_apps_top50(df, days_threshold=days)

            if result:
                print(f"✅ 新APP检测完成，发现 {len(result)} 个新APP")
                # 显示增长速度监控
                self._display_growth_monitoring(df)
            else:
                print("✅ 新APP检测完成，未发现新APP")

        except Exception as e:
            print(f"⚠️ 分析过程中出现错误: {e}")

        input("\n按回车键继续...")

    def handle_app_explosion_prediction(self):
        """处理APP爆发风险预测 - 使用统一时间管理"""
        print("\n🎯 APP爆发风险预测")
        print("━" * 60)

        try:
            days = self._get_analysis_days()
            time_desc = self._get_current_time_description()

            print(f"📅 分析周期: {time_desc}")
            print("🔄 正在执行APP爆发风险预测...")

            df = self.monitor.data_processor.get_comprehensive_data(days=days)
            if df is None or df.empty:
                print("❌ 无法获取数据")
                input("\n按回车键继续...")
                return

            result = self.monitor.network_analyzer.predict_explosion_risk(df)

            if result:
                print(f"✅ APP爆发风险预测完成，发现 {len(result)} 个风险APP")
            else:
                print("✅ APP爆发风险预测完成，暂无高风险预测")

        except Exception as e:
            print(f"⚠️ 分析过程中出现错误: {e}")

        input("\n按回车键继续...")

    def handle_suspicious_app_monitoring(self):
        """处理可疑APP检测 - 清理版，去除重复显示"""
        print("\n🚨 可疑APP检测")
        print("━" * 60)

        try:
            days = self._get_analysis_days()
            time_desc = self._get_current_time_description()

            print(f"📅 分析周期: {time_desc}")
            print("🔄 正在执行可疑APP检测...")

            df = self.monitor.data_processor.get_comprehensive_data(days=days)
            if df is None or df.empty:
                print("❌ 无法获取数据")
                input("\n按回车键继续...")
                return

            # 直接调用模式检测器，避免重复
            suspicious_alerts = self.monitor.pattern_detector.detect_suspicious_patterns(df)

            # 简洁的结果显示
            if suspicious_alerts:
                print(f"\n✅ 检测完成，发现 {len(suspicious_alerts)} 个可疑问题")
            else:
                print(f"\n✅ 检测完成，未发现可疑问题")

        except Exception as e:
            print(f"⚠️ 分析过程中出现错误: {e}")

        input("\n按回车键继续...")

    def handle_victim_profile_analysis(self):
        """处理受害人画像分析 - 使用统一时间管理"""
        print("\n👤 受害人画像分析")
        print("━" * 60)

        try:
            days = self._get_analysis_days()
            time_desc = self._get_current_time_description()

            print(f"📅 分析周期: {time_desc}")
            print("🔄 正在执行受害人画像分析...")

            result = self.monitor.run_victim_profile_analysis(days=days)
            if result and result.get('victim_profile'):
                print("✅ 受害人画像分析完成")
            else:
                print("⚠️ 无足够的受害人年龄数据进行分析")

        except Exception as e:
            print(f"⚠️ 分析过程中出现错误: {e}")

        input("\n按回车键继续...")

    def handle_geographic_analysis(self):
        """处理受害人地域分布分析 - 使用统一时间管理"""
        print("\n🗺️ 受害人地域分布分析")
        print("━" * 60)

        try:
            days = self._get_analysis_days()
            time_desc = self._get_current_time_description()

            print(f"📅 分析周期: {time_desc}")
            print("🔄 正在执行地域分布分析...")

            result = self.monitor.run_geographic_analysis(days=days)
            if result:
                print("✅ 地域分布分析完成")
                # 主动显示地域分析结果，避免重复
                self._print_geographic_summary_once(result)
            else:
                print("⚠️ 数据中缺少地域信息")

        except Exception as e:
            print(f"⚠️ 分析过程中出现错误: {e}")

        input("\n按回车键继续...")

    def handle_comprehensive_analysis(self):
        """处理完整分析 - 使用统一时间管理"""
        print("\n🚀 完整分析")
        print("━" * 60)

        try:
            days = self._get_analysis_days()
            time_desc = self._get_current_time_description()

            print(f"📅 分析周期: {time_desc}")

            start_time = datetime.now()
            results = self.monitor.run_comprehensive_analysis(
                days=days,
                use_cache=True,
                generate_report=True
            )
            end_time = datetime.now()

            if results:
                execution_time = (end_time - start_time).total_seconds()
                print(f"\n✅ 完整分析完成！耗时: {execution_time:.2f}秒")
                self._print_comprehensive_summary_ordered(results)
            else:
                print("❌ 分析失败")

        except Exception as e:
            print(f"⚠️ 分析过程中出现错误: {e}")

        input("\n按回车键继续...")

    def handle_statistical_charts(self):
        """处理统计图表 - 使用统一时间管理"""
        print("\n📊 统计图表")
        print("━" * 60)

        submenu = """
选择图表类型:
1. 📈 统计趋势图   - APP涉案数量时间趋势(TOP10)
2. 📊 堆叠柱状图   - APP案件数按日期堆叠展示(TOP20)  
3. 📋 排名条形图   - APP涉案数量排名(TOP50)
4. 📉 App涉诈统计信息
        """
        print(submenu)

        choice = self.get_user_input("请选择 (1-4): ")
        days = self._get_analysis_days()

        # 获取统计数据
        stats = self.monitor.get_statistical_data(days=days)
        if not stats:
            print("❌ 无法获取数据")
            input("按回车键继续...")
            return

        try:
            if choice == '1':
                self._create_trend_chart(stats, days)
            elif choice == '2':
                self._create_stacked_chart(stats, days)
            elif choice == '3':
                self._create_ranking_chart(stats)
            elif choice == '4':
                self._handle_statistical_info(stats)
            else:
                print("❌ 无效选择")
                return
        except Exception as e:
            print(f"⚠️ 图表生成过程中出现错误: {e}")

        input("\n按回车键继续...")

    def handle_system_settings(self):
        """处理系统设置 - 包含时间管理"""
        print("\n⚙️ 系统设置")
        print("━" * 60)

        submenu = """
选择管理操作:
1. 📅 时间查询设置        - 设置数据查询时间范围
2. 📊 系统状态检查        - 数据库连接、组件状态
3. ⚡️ 数据库性能检查      - 查询性能、索引效率
4. 🔧 创建数据库索引      - 优化查询性能
5. 🗑️ 清理缓存          - 清理系统缓存文件
6. 💾 缓存统计          - 查看缓存使用情况
7. 🔙 返回主菜单        - 返回上级菜单
        """
        print(submenu)

        choice = self.get_user_input("请选择 (1-7): ")

        try:
            if choice == '1':
                self._handle_time_settings()
            elif choice == '2':
                self._handle_system_status_check()
            elif choice == '3':
                self._handle_performance_check()
            elif choice == '4':
                self._handle_create_indexes()
            elif choice == '5':
                self._handle_clear_cache()
            elif choice == '6':
                self._handle_cache_stats()
            elif choice == '7':
                return
            else:
                print("❌ 无效选择")
        except Exception as e:
            print(f"❌ 操作失败: {e}")

        input("\n按回车键继续...")

    def handle_exit(self):
        """处理退出"""
        confirm = self.get_user_input("确定要退出系统吗？(y/n): ").lower()
        if confirm in ['y', 'yes']:
            self.running = False
            print("\n👋 感谢使用APP涉案监控系统！")

    # 辅助方法
    def _get_analysis_days(self) -> int:
        """获取分析天数 - 统一从时间管理器"""
        try:
            from core.time_query_manager import get_days_count
            return get_days_count()
        except ImportError:
            return TIME_QUERY_CONFIG['default_days']

    def _get_current_time_description(self) -> str:
        """获取当前时间描述"""
        try:
            from core.time_query_manager import get_time_description
            return get_time_description()
        except ImportError:
            return f"默认{TIME_QUERY_CONFIG['default_days']}天"

    def _display_growth_monitoring(self, df: pd.DataFrame):
        """显示增长速度监控结果"""
        try:
            growth_results = self.monitor.network_analyzer.monitor_growth_speed(df)
            if growth_results:
                print(f"\n📈 增长速度监控结果已显示在上方")
            else:
                print("\n📈 增长速度监控: 未发现异常增长APP")
        except Exception as e:
            print(f"⚠️ 增长速度监控显示失败: {e}")

    def _print_geographic_summary_once(self, result: dict):
        """打印地域分析摘要"""
        print(f"\n🗺️ 地域分布统计:")
        print(f"   涉及省份: {result['total_areas']} 个")
        print(f"   有效案件数: {result['total_cases']} 起")

        # 显示TOP15省份
        area_dist = result.get('area_distribution', {})
        if area_dist:
            print(f"\n   TOP15 高发省份:")
            for i, (area, count) in enumerate(list(area_dist.items())[:15], 1):
                percentage = (count / result['total_cases']) * 100
                print(f"     {i:2d}. {area}: {count}起 ({percentage:.1f}%)")

    def _print_comprehensive_summary_ordered(self, results: dict):
        """按照指定顺序打印完整分析摘要"""
        print(f"\n📋 完整分析摘要:")
        print("=" * 60)

        # 新APP检测
        new_apps = results.get('new_apps_detection', [])
        if new_apps:
            risk_stats = {'高': 0, '中': 0, '低': 0}
            for app in new_apps:
                risk_level = app.get('risk_level', '低')
                risk_stats[risk_level] = risk_stats.get(risk_level, 0) + 1
            print(f"🆕 新APP检测: 发现 {len(new_apps)}个新APP (高风险{risk_stats['高']}个)")
        else:
            print(f"🆕 新APP检测: 未发现新APP")

        # 统一的可疑检测结果 (合并原来的两个重复项)
        suspicious_patterns = results.get('suspicious_patterns', [])
        print(f"🚨 可疑风险检测: {'发现可疑问题' if suspicious_patterns else '未发现异常'}")

        # APP增长速度监控
        growth_results = results.get('growth_monitoring', [])
        print(f"📈 APP增长速度监控: {'发现异常增长' if growth_results else '正常'}")

        # APP爆发风险预测
        explosion_prediction = results.get('explosion_prediction', [])
        print(f"🎯 APP爆发风险预测: {'存在爆发风险' if explosion_prediction else '风险较低'}")

        # 新APP综合分析摘要
        if 'network_analysis' in results:
            network = results['network_analysis']
            total_alerts = len(network.get('comprehensive_alerts', []))
            print(f"📊 新APP综合分析: 生成{total_alerts}个综合预警")

        # 受害人画像分析
        if 'victim_profile' in results and results['victim_profile']:
            profile = results['victim_profile']
            print(f"👤 受害人画像分析: 平均年龄{profile.avg_age:.1f}岁，共{profile.total_victims}人")

        # APP风险排名分析
        app_risk_ranking = results.get('app_risk_ranking', [])
        if app_risk_ranking:
            high_risk_apps = len([app for app in app_risk_ranking if app.get('risk_level') == '高'])
            print(f"🏆 APP风险排名: 分析{len(app_risk_ranking)}个APP，高风险{high_risk_apps}个")

        # 报告文件
        if 'report_file' in results and results['report_file']:
            print(f"📊 分析报告: {results['report_file']}")

    def _handle_statistical_info(self, stats: dict):
        """处理统计信息显示"""
        print("\n📋 统计信息")
        print("━" * 60)

        try:
            self._print_statistical_summary(stats)
        except Exception as e:
            print(f"⚠️ 统计过程中出现错误: {e}")

    def _handle_time_settings(self):
        """处理时间设置"""
        try:
            from core.time_query_manager import show_time_selection_menu, get_time_summary

            print("\n📅 时间查询设置")
            print("━" * 40)

            current_summary = get_time_summary()
            print(f"当前设置: {current_summary}")

            if show_time_selection_menu():
                new_summary = get_time_summary()
                print(f"\n✅ 时间查询设置已更新")
                print(f"新设置: {new_summary}")
            else:
                print("🔙 时间查询设置未更改")

        except ImportError:
            print("❌ 时间查询管理器不可用")

    def _handle_system_status_check(self):
        """处理系统状态检查"""
        try:
            status = self.monitor.get_system_status()
            print(f"\n📋 系统状态:")
            print(f"   数据库连接: {'✅ 正常' if status.get('database_connected', False) else '❌ 异常'}")
            print(f"   默认分析周期: {status.get('default_analysis_days', TIME_QUERY_CONFIG['default_days'])}天")

            components = status.get('components', {})
            for component, state in components.items():
                status_icon = "✅" if state == "ready" else "❌"
                print(f"   {component}: {status_icon}")

            # 显示数据库信息
            if 'database_info' in status:
                db_info = status['database_info']
                print(f"\n💾 数据库信息:")
                if 'total_size_mb' in db_info:
                    print(f"   总大小: {db_info['total_size_mb']:.1f} MB")
                if 'tables' in db_info:
                    for table in db_info['tables']:
                        print(f"   {table['name']}: {table.get('size_mb', 0):.1f} MB ({table.get('rows', 0):,} 行)")

            # 显示性能信息
            if 'performance' in status:
                perf = status['performance']
                print(f"\n📈 性能统计:")
                print(f"   总查询数: {perf.get('total_queries', 0)}")
                print(f"   平均查询时间: {perf.get('average_query_time', 0):.3f}秒")
                print(f"   缓存命中率: {perf.get('cache_hit_rate', 0):.1f}%")

        except Exception as e:
            print(f"❌ 系统状态检查失败: {e}")

    def _handle_performance_check(self):
        """处理性能检查"""
        try:
            print("正在检查数据库性能...")
            self.monitor.db_manager.check_optimized_query_performance()
        except Exception as e:
            print(f"❌ 性能检查失败: {e}")

    def _handle_create_indexes(self):
        """处理索引创建"""
        try:
            print("正在创建数据库索引...")
            success = self.monitor.db_manager.create_optimized_database_indexes()
            print("✅ 索引创建完成" if success else "❌ 索引创建失败")
        except Exception as e:
            print(f"❌ 索引创建失败: {e}")

    def _handle_clear_cache(self):
        """处理缓存清理"""
        try:
            print("正在清理缓存...")
            if hasattr(self.monitor.data_processor, 'clear_cache'):
                self.monitor.data_processor.clear_cache()
            if hasattr(self.monitor.db_manager, 'clear_cache'):
                self.monitor.db_manager.clear_cache()
            print("✅ 缓存清理完成")
        except Exception as e:
            print(f"❌ 缓存清理失败: {e}")

    def _handle_cache_stats(self):
        """处理缓存统计"""
        try:
            print("💾 缓存使用情况:")
            if hasattr(self.monitor.data_processor, '_memory_cache'):
                cache_info = self.monitor.data_processor._memory_cache
                print(f"   数据处理器缓存: {len(cache_info)} 项")
            if hasattr(self.monitor.db_manager, 'query_cache'):
                db_cache_info = self.monitor.db_manager.query_cache
                print(f"   数据库查询缓存: {len(db_cache_info)} 项")
        except Exception as e:
            print(f"❌ 缓存统计获取失败: {e}")

    def _create_trend_chart(self, stats, days):
        """创建趋势图 - 修复保存问题"""
        print("\n📈 生成统计趋势图...")

        df = self.monitor.data_processor.get_comprehensive_data(days=days)
        if df is None or df.empty:
            print("❌ 无法获取原始数据")
            return

        daily_app_stats = df.groupby([df['insert_day'].dt.date, 'final_app_name']).size().reset_index()
        daily_app_stats.columns = ['date', 'app_name', 'case_count']

        top_apps = df['final_app_name'].value_counts().head(10).index

        pivot_df = daily_app_stats[daily_app_stats['app_name'].isin(top_apps)].pivot_table(
            index='date', columns='app_name', values='case_count', fill_value=0
        )

        # 创建图表 - 确保不会被立即清除
        fig, ax = plt.subplots(figsize=CHART_CONFIG['figure_size']['trend_chart'])
        fig.patch.set_facecolor(self.colors['background'])

        for i, app_name in enumerate(pivot_df.columns):
            color = self.color_palette[i % len(self.color_palette)]
            ax.plot(pivot_df.index, pivot_df[app_name], marker='o', linewidth=2,
                    markersize=6, label=app_name, color=color, alpha=0.8)

        ax.set_title('TOP10 APP涉案数量趋势图', fontsize=18, fontweight='bold', pad=20)
        ax.set_xlabel('日期', fontsize=14)
        ax.set_ylabel('案件数量', fontsize=14)
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=CHART_CONFIG['grid_alpha'])

        plt.tight_layout()

        # 显示图表
        plt.show(block=False)  # 非阻塞显示

        # 询问是否保存
        save_choice = self.get_user_input("是否保存图表？(y/n, 默认n): ").lower()
        if save_choice in ['y', 'yes']:
            success = self._save_chart_properly("APP涉案趋势图", fig)
            if success:
                print("✅ 图表保存成功")
            else:
                print("❌ 图表保存失败")

        # 关闭图表
        plt.close(fig)

    def _create_stacked_chart(self, stats, days):
        """创建堆叠柱状图 - 修复保存问题"""
        print("\n📊 生成堆叠柱状图...")

        df = self.monitor.data_processor.get_comprehensive_data(days=days)
        if df is None or df.empty:
            print("❌ 无法获取原始数据")
            return

        top_apps = df['final_app_name'].value_counts().head(20).index

        app_daily_stats = df[df['final_app_name'].isin(top_apps)].groupby(
            ['final_app_name', df['insert_day'].dt.date]
        ).size().reset_index()
        app_daily_stats.columns = ['app_name', 'date', 'case_count']

        pivot_df = app_daily_stats.pivot_table(
            index='app_name', columns='date', values='case_count', fill_value=0
        )

        pivot_df = pivot_df.loc[pivot_df.sum(axis=1).sort_values(ascending=False).index]

        # 创建图表
        fig, ax = plt.subplots(figsize=CHART_CONFIG['figure_size']['stacked_chart'])
        fig.patch.set_facecolor(self.colors['background'])

        bottom = np.zeros(len(pivot_df))
        dates = pivot_df.columns

        for i, date in enumerate(dates):
            values = pivot_df[date].values
            ax.bar(pivot_df.index, values, bottom=bottom,
                   label=date.strftime('%m-%d'),
                   color=self.color_palette[i % len(self.color_palette)], alpha=0.8)
            bottom += values

        ax.set_title('TOP20 APP涉案数量堆叠柱状图', fontsize=18, fontweight='bold', pad=20)
        ax.set_xlabel('APP名称', fontsize=14)
        ax.set_ylabel('案件数量', fontsize=14)

        # x轴APP名称水平横置（不旋转）
        ax.tick_params(axis='x', rotation=0)

        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', ncol=2, fontsize=10)
        ax.grid(True, alpha=CHART_CONFIG['grid_alpha'], axis='y')

        plt.tight_layout()

        # 显示图表
        plt.show(block=False)

        # 询问是否保存
        save_choice = self.get_user_input("是否保存图表？(y/n, 默认n): ").lower()
        if save_choice in ['y', 'yes']:
            success = self._save_chart_properly("APP涉案堆叠柱状图", fig)
            if success:
                print("✅ 图表保存成功")
            else:
                print("❌ 图表保存失败")

        # 关闭图表
        plt.close(fig)

    def _create_ranking_chart(self, stats):
        """创建排名条形图 - 修复保存问题和排序问题"""
        print("\n📋 生成排名条形图...")

        top_apps = stats.get('top_apps', {})
        if not top_apps:
            print("❌ 无TOP APP数据")
            return

        top_50 = dict(list(top_apps.items())[:50])

        # 创建图表
        fig, ax = plt.subplots(figsize=(14, max(16, len(top_50) * 0.4)))
        fig.patch.set_facecolor(self.colors['background'])

        # 确保数据是严格按照降序排列的（由多到少）
        sorted_items = sorted(top_50.items(), key=lambda x: x[1], reverse=True)
        apps = [item[0] for item in sorted_items]
        counts = [item[1] for item in sorted_items]

        # 为了让最大值显示在顶部，我们需要反转位置顺序
        y_positions = list(range(len(apps)))

        # 绘制水平条形图
        bars = ax.barh(y_positions, counts, color=self.colors['primary'], alpha=0.8)

        # 设置y轴标签 - 最大值在顶部（反转apps列表）
        ax.set_yticks(y_positions)
        ax.set_yticklabels(list(reversed(apps)))  # 反转标签，使最大值在顶部

        # 在条形图上添加数值标签
        for i, (bar, value) in enumerate(zip(bars, counts)):
            ax.text(bar.get_width() + max(counts) * 0.01, bar.get_y() + bar.get_height() / 2,
                    f'{int(value)}', ha='left', va='center', fontsize=10)

        ax.set_title(f'APP涉案数量排名 - TOP{len(top_50)} (由多到少排序)', fontsize=18, fontweight='bold', pad=20)
        ax.set_xlabel('案件数量', fontsize=14)
        ax.set_ylabel('APP名称', fontsize=14)
        ax.grid(True, alpha=CHART_CONFIG['grid_alpha'], axis='x')

        # 确保最大值在最上方
        ax.invert_yaxis()  # 反转y轴，确保最大值在顶部

        plt.tight_layout()

        # 显示图表
        plt.show(block=False)

        # 询问是否保存
        save_choice = self.get_user_input("是否保存图表？(y/n, 默认n): ").lower()
        if save_choice in ['y', 'yes']:
            success = self._save_chart_properly("APP涉案排名图", fig)
            if success:
                print("✅ 图表保存成功")
            else:
                print("❌ 图表保存失败")

        # 关闭图表
        plt.close(fig)

    def _save_chart_properly(self, chart_name: str, fig) -> bool:
        """正确保存图表 - 修复空白文件问题"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{chart_name}_{timestamp}.png"
            filepath = self.export_dir / filename

            # 确保目录存在
            self.export_dir.mkdir(parents=True, exist_ok=True)

            # 保存图表 - 使用完整参数确保内容正确保存
            fig.savefig(
                filepath,
                dpi=CHART_CONFIG['dpi'],
                bbox_inches='tight',
                facecolor='white',
                edgecolor='none',
                format='png',
                pad_inches=0.2,
                transparent=False
            )

            # 验证文件是否成功创建且不为空
            if filepath.exists() and filepath.stat().st_size > 1024:  # 至少1KB
                print(f"✅ 图表已保存到: {filepath}")
                print(f"📁 文件大小: {filepath.stat().st_size / 1024:.1f} KB")
                return True
            else:
                print(f"❌ 保存的文件为空或太小")
                return False

        except Exception as e:
            print(f"❌ 图表保存失败: {e}")
            return False

    def _print_statistical_summary(self, stats: dict):
        """打印统计信息摘要"""
        basic = stats.get('basic_stats', {})
        financial = stats.get('financial_stats', {})
        top_apps = stats.get('top_apps', {})

        print("\n📊 APP涉案统计信息")
        print("=" * 60)

        print(f"📈 统计周期: {basic.get('date_range', 0)} 天")
        print(f"📱 涉及APP: {basic.get('unique_apps', 0)} 个")
        print(f"📊 案件总数: {basic.get('total_cases', 0)} 起")
        print(f"📊 日均案件: {basic.get('daily_average', 0):.1f} 起")

        if financial:
            print(f"💰 涉案总金额: {financial.get('total_amount', 0):,.2f} 元")
            print(f"💸 平均损失: {financial.get('average_amount', 0):,.2f} 元")
            print(f"💎 最高损失: {financial.get('max_amount', 0):,.2f} 元")

        if top_apps:
            print(f"\n🏆 TOP50 APP排名:")
            print("-" * 60)
            for i, (app, count) in enumerate(list(top_apps.items())[:50], 1):
                print(f"{i:2d}. {app}: {count} 起")


def main():
    """最终修复版交互式菜单入口"""
    menu = FinalInteractiveMenu()
    menu.start()


if __name__ == "__main__":
    main()