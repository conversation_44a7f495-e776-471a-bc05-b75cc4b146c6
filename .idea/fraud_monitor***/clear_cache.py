#!/usr/bin/env python3
"""
缓存清理脚本 - 解决时间范围不一致问题
当时间管理器设置发生变化时，需要清理旧的缓存数据
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from core.database import DatabaseManager
    from core.data_processor import DataProcessor
    from core.time_query_manager import get_current_time_config, get_time_summary
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)


def clear_all_caches():
    """清理所有缓存"""
    print("🧹 开始清理系统缓存...")
    print("=" * 50)

    try:
        # 初始化组件
        db_manager = DatabaseManager()
        data_processor = DataProcessor(db_manager)

        # 清理数据处理器缓存
        if hasattr(data_processor, 'clear_cache'):
            data_processor.clear_cache()
            print("✅ 数据处理器缓存已清理")

        # 清理数据库查询缓存
        if hasattr(db_manager, 'clear_cache'):
            db_manager.clear_cache()
            print("✅ 数据库查询缓存已清理")

        # 显示当前时间设置
        try:
            time_config = get_current_time_config()
            time_summary = get_time_summary()
            print(f"\n📅 当前时间设置: {time_summary}")
            print(f"📅 具体范围: {time_config['start_date']} 至 {time_config['end_date']}")
        except Exception as e:
            print(f"⚠️ 无法获取时间设置: {e}")

        print("\n✅ 所有缓存清理完成")
        print("💡 建议: 现在重新运行分析，将使用最新的时间设置")

    except Exception as e:
        print(f"❌ 缓存清理失败: {e}")
        return False

    return True


def show_cache_status():
    """显示缓存状态"""
    print("💾 缓存状态检查...")
    print("=" * 50)

    try:
        db_manager = DatabaseManager()
        data_processor = DataProcessor(db_manager)

        # 数据处理器缓存状态
        if hasattr(data_processor, '_memory_cache'):
            cache_items = len(data_processor._memory_cache)
            print(f"📦 数据处理器缓存: {cache_items} 项")

            if cache_items > 0:
                print("缓存详情:")
                for key, item in data_processor._memory_cache.items():
                    timestamp = item.get('timestamp', 'Unknown')
                    size_mb = item.get('size_mb', 0)
                    print(f"  - {key}: {timestamp}, {size_mb:.2f}MB")

        # 数据库缓存状态
        if hasattr(db_manager, 'query_cache'):
            db_cache_items = len(db_manager.query_cache)
            print(f"🗄️ 数据库查询缓存: {db_cache_items} 项")

        # 缓存统计
        if hasattr(data_processor, 'get_cache_stats'):
            stats = data_processor.get_cache_stats()
            print(f"\n📊 缓存统计:")
            print(f"  - 缓存命中率: {stats.get('hit_rate', 0):.1f}%")
            print(f"  - 总请求数: {stats.get('total_requests', 0)}")
            print(f"  - 缓存命中: {stats.get('hits', 0)}")
            print(f"  - 缓存未命中: {stats.get('misses', 0)}")
            print(f"  - 内存使用: {stats.get('memory_usage_mb', 0):.2f}MB")

    except Exception as e:
        print(f"❌ 缓存状态检查失败: {e}")


def main():
    """主函数"""
    print("🚀 缓存管理工具")
    print("=" * 50)

    if len(sys.argv) > 1:
        command = sys.argv[1].lower()

        if command == 'clear':
            clear_all_caches()
        elif command == 'status':
            show_cache_status()
        elif command == 'help':
            print("使用方法:")
            print("  python clear_cache.py clear   - 清理所有缓存")
            print("  python clear_cache.py status  - 显示缓存状态")
            print("  python clear_cache.py help    - 显示帮助信息")
        else:
            print(f"❌ 未知命令: {command}")
            print("使用 'python clear_cache.py help' 查看帮助")
    else:
        # 默认操作：显示状态然后询问是否清理
        show_cache_status()

        print("\n" + "=" * 50)
        response = input("是否要清理所有缓存? (y/N): ").strip().lower()

        if response in ['y', 'yes', '是']:
            clear_all_caches()
        else:
            print("🔙 已取消缓存清理")


if __name__ == "__main__":
    main()