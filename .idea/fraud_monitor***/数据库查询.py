#!/usr/bin/env python3
"""
简易数据库查询工具 - 快速验证数据情况
用于检查特定日期的数据分布、APP统计等
"""

import sys
import pandas as pd
from datetime import datetime, date, timedelta
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from core.database import DatabaseManager
    from config import DATABASE_CONFIG
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)


class SimpleQueryTool:
    """简易查询工具"""

    def __init__(self):
        print("🔌 连接数据库...")
        self.db_manager = DatabaseManager()
        if not self.db_manager.connect_database():
            print("❌ 数据库连接失败")
            sys.exit(1)
        print("✅ 数据库连接成功")

    def query_date_range_stats(self, start_date: str, end_date: str):
        """查询日期范围统计"""
        print(f"\n📊 查询日期范围: {start_date} 至 {end_date}")
        print("=" * 50)

        try:
            from sqlalchemy import text

            # 按日期统计
            daily_query = text("""
                SELECT 
                    DATE(insert_day) as date,
                    COUNT(*) as total_cases,
                    COUNT(DISTINCT app_name) as unique_apps,
                    SUM(CASE WHEN involved_amount > 0 THEN 1 ELSE 0 END) as valid_amount_cases,
                    SUM(CASE WHEN involved_amount > 0 THEN involved_amount ELSE 0 END) as total_amount,
                    AVG(CASE WHEN involved_amount > 0 THEN involved_amount ELSE NULL END) as avg_amount,
                    MIN(insert_day) as earliest_time,
                    MAX(insert_day) as latest_time
                FROM anti_fraud_case_new 
                WHERE DATE(insert_day) BETWEEN :start_date AND :end_date
                GROUP BY DATE(insert_day)
                ORDER BY DATE(insert_day)
            """)

            with self.db_manager.engine.connect() as conn:
                result = conn.execute(daily_query, {
                    'start_date': start_date,
                    'end_date': end_date
                })

                rows = result.fetchall()

                if not rows:
                    print("⚠️ 指定日期范围内无数据")
                    return

                print(
                    f"{'日期':<12} {'总案件':<8} {'APP数':<8} {'有效金额':<10} {'总金额(万)':<12} {'平均金额':<10} {'时间范围':<20}")
                print("-" * 90)

                total_cases = 0
                total_amount = 0

                for row in rows:
                    date_str = row[0].strftime('%m-%d') if row[0] else 'N/A'
                    total_cases_day = row[1] or 0
                    unique_apps = row[2] or 0
                    valid_amount_cases = row[3] or 0
                    total_amount_day = row[4] or 0
                    avg_amount = row[5] or 0
                    earliest = row[6].strftime('%H:%M') if row[6] else 'N/A'
                    latest = row[7].strftime('%H:%M') if row[7] else 'N/A'
                    time_range = f"{earliest}-{latest}"

                    print(f"{date_str:<12} {total_cases_day:<8} {unique_apps:<8} {valid_amount_cases:<10} "
                          f"{total_amount_day / 10000:<12.1f} {avg_amount:<10.0f} {time_range:<20}")

                    total_cases += total_cases_day
                    total_amount += total_amount_day

                print("-" * 90)
                print(f"{'总计':<12} {total_cases:<8} {'':<8} {'':<10} {total_amount / 10000:<12.1f}")

        except Exception as e:
            print(f"❌ 查询失败: {e}")

    def query_single_date(self, target_date: str):
        """查询单个日期详情"""
        print(f"\n🔍 查询日期详情: {target_date}")
        print("=" * 50)

        try:
            from sqlalchemy import text

            # 单日详细统计
            detail_query = text("""
                SELECT 
                    COUNT(*) as total_cases,
                    COUNT(DISTINCT app_name) as unique_apps,
                    COUNT(DISTINCT occurrence_area) as unique_areas,
                    SUM(CASE WHEN involved_amount > 0 THEN 1 ELSE 0 END) as valid_amount_cases,
                    SUM(CASE WHEN involved_amount > 0 THEN involved_amount ELSE 0 END) as total_amount,
                    AVG(CASE WHEN involved_amount > 0 THEN involved_amount ELSE NULL END) as avg_amount,
                    MAX(CASE WHEN involved_amount > 0 THEN involved_amount ELSE NULL END) as max_amount,
                    MIN(insert_day) as earliest_time,
                    MAX(insert_day) as latest_time,
                    COUNT(DISTINCT HOUR(insert_day)) as active_hours
                FROM anti_fraud_case_new 
                WHERE DATE(insert_day) = :target_date
            """)

            with self.db_manager.engine.connect() as conn:
                result = conn.execute(detail_query, {'target_date': target_date})
                row = result.fetchone()

                if not row or row[0] == 0:
                    print(f"⚠️ {target_date} 无数据")
                    return

                print(f"📊 基础统计:")
                print(f"  • 总案件数: {row[0]:,} 起")
                print(f"  • 涉及APP: {row[1]:,} 个")
                print(f"  • 涉及地区: {row[2]:,} 个")
                print(f"  • 有效金额案件: {row[3]:,} 起")
                print(f"  • 总涉案金额: {(row[4] or 0):,.0f} 元 ({(row[4] or 0) / 10000:.1f} 万)")
                print(f"  • 平均金额: {(row[5] or 0):,.0f} 元")
                print(f"  • 最高金额: {(row[6] or 0):,.0f} 元")
                print(f"  • 时间跨度: {row[7]} 至 {row[8]}")
                print(f"  • 活跃小时数: {row[9]} 小时")

                # TOP APP统计
                app_query = text("""
                    SELECT app_name, COUNT(*) as case_count,
                           SUM(CASE WHEN involved_amount > 0 THEN involved_amount ELSE 0 END) as total_amount
                    FROM anti_fraud_case_new 
                    WHERE DATE(insert_day) = :target_date
                    GROUP BY app_name
                    ORDER BY case_count DESC
                    LIMIT 10
                """)

                app_result = conn.execute(app_query, {'target_date': target_date})
                app_rows = app_result.fetchall()

                if app_rows:
                    print(f"\n🏆 TOP10 APP:")
                    print(f"{'排名':<4} {'APP名称':<25} {'案件数':<8} {'金额(万)':<10}")
                    print("-" * 50)
                    for i, app_row in enumerate(app_rows, 1):
                        app_name = app_row[0][:24] if app_row[0] else 'Unknown'
                        case_count = app_row[1]
                        amount_wan = (app_row[2] or 0) / 10000
                        print(f"{i:<4} {app_name:<25} {case_count:<8} {amount_wan:<10.1f}")

        except Exception as e:
            print(f"❌ 查询失败: {e}")

    def query_recent_days(self, days: int = 7):
        """查询最近N天概况"""
        end_date = date.today()
        start_date = end_date - timedelta(days=days - 1)

        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')

        print(f"\n📅 最近{days}天数据概况")
        self.query_date_range_stats(start_str, end_str)

    def query_app_stats(self, app_name: str, days: int = 7):
        """查询特定APP统计"""
        print(f"\n🔍 APP统计: {app_name} (最近{days}天)")
        print("=" * 50)

        try:
            from sqlalchemy import text

            app_query = text("""
                SELECT 
                    DATE(insert_day) as date,
                    COUNT(*) as case_count,
                    SUM(CASE WHEN involved_amount > 0 THEN involved_amount ELSE 0 END) as total_amount,
                    AVG(CASE WHEN involved_amount > 0 THEN involved_amount ELSE NULL END) as avg_amount,
                    COUNT(DISTINCT occurrence_area) as areas
                FROM anti_fraud_case_new 
                WHERE app_name = :app_name
                AND insert_day >= DATE_SUB(NOW(), INTERVAL :days DAY)
                GROUP BY DATE(insert_day)
                ORDER BY DATE(insert_day) DESC
            """)

            with self.db_manager.engine.connect() as conn:
                result = conn.execute(app_query, {
                    'app_name': app_name,
                    'days': days
                })

                rows = result.fetchall()

                if not rows:
                    print(f"⚠️ 最近{days}天无 '{app_name}' 相关数据")
                    return

                print(f"{'日期':<12} {'案件数':<8} {'金额(万)':<10} {'平均金额':<10} {'地区数':<8}")
                print("-" * 50)

                total_cases = 0
                total_amount = 0

                for row in rows:
                    date_str = row[0].strftime('%m-%d')
                    case_count = row[1]
                    amount_wan = (row[2] or 0) / 10000
                    avg_amount = row[3] or 0
                    areas = row[4]

                    print(f"{date_str:<12} {case_count:<8} {amount_wan:<10.1f} {avg_amount:<10.0f} {areas:<8}")

                    total_cases += case_count
                    total_amount += (row[2] or 0)

                print("-" * 50)
                print(f"总计: {total_cases} 起案件，{total_amount / 10000:.1f} 万元")

        except Exception as e:
            print(f"❌ 查询失败: {e}")

    def interactive_mode(self):
        """交互模式"""
        print("\n🚀 简易数据库查询工具")
        print("=" * 50)

        while True:
            print("\n选择查询类型:")
            print("1. 📅 查询日期范围统计")
            print("2. 🔍 查询单个日期详情")
            print("3. 📊 查询最近N天概况")
            print("4. 🔍 查询特定APP统计")
            print("5. ⚡ 快速检查（最近3天）")
            print("0. ❌ 退出")

            try:
                choice = input("\n请选择 (0-5): ").strip()

                if choice == '0':
                    print("👋 再见！")
                    break
                elif choice == '1':
                    start = input("开始日期 (YYYY-MM-DD): ").strip()
                    end = input("结束日期 (YYYY-MM-DD): ").strip()
                    self.query_date_range_stats(start, end)
                elif choice == '2':
                    target = input("目标日期 (YYYY-MM-DD): ").strip()
                    self.query_single_date(target)
                elif choice == '3':
                    days = int(input("查询最近几天 (默认7): ").strip() or 7)
                    self.query_recent_days(days)
                elif choice == '4':
                    app_name = input("APP名称: ").strip()
                    days = int(input("查询最近几天 (默认7): ").strip() or 7)
                    self.query_app_stats(app_name, days)
                elif choice == '5':
                    print("⚡ 快速检查最近3天数据...")
                    self.query_recent_days(3)

                    # 检查是否有8月4日数据
                    print("\n🔍 特别检查8月4日数据...")
                    self.query_single_date('2025-08-04')
                else:
                    print("❌ 无效选择")

            except KeyboardInterrupt:
                print("\n👋 用户中断，退出")
                break
            except ValueError as e:
                print(f"❌ 输入错误: {e}")
            except Exception as e:
                print(f"❌ 执行错误: {e}")


def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 命令行模式
        tool = SimpleQueryTool()
        command = sys.argv[1].lower()

        if command == 'recent':
            days = int(sys.argv[2]) if len(sys.argv) > 2 else 7
            tool.query_recent_days(days)
        elif command == 'date':
            if len(sys.argv) < 3:
                print("用法: python simple_query.py date YYYY-MM-DD")
                return
            tool.query_single_date(sys.argv[2])
        elif command == 'range':
            if len(sys.argv) < 4:
                print("用法: python simple_query.py range YYYY-MM-DD YYYY-MM-DD")
                return
            tool.query_date_range_stats(sys.argv[2], sys.argv[3])
        elif command == 'app':
            if len(sys.argv) < 3:
                print("用法: python simple_query.py app APP名称 [天数]")
                return
            app_name = sys.argv[2]
            days = int(sys.argv[3]) if len(sys.argv) > 3 else 7
            tool.query_app_stats(app_name, days)
        elif command == 'check':
            # 快速检查模式
            tool.query_recent_days(3)
            print("\n" + "=" * 50)
            tool.query_single_date('2025-08-04')
        else:
            print("❌ 未知命令")
            print("可用命令: recent, date, range, app, check")
    else:
        # 交互模式
        tool = SimpleQueryTool()
        tool.interactive_mode()


if __name__ == "__main__":
    main()