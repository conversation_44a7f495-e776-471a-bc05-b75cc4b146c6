"""
APP涉案监控系统 - 最终修复版新APP分析器
支持从anti_app_name表查询首次发现时间，使用统一显示对齐
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
from .base import NetworkAnalyzer, PatternAnalyzer, TrendAnalyzer, RiskAnalyzer
from config import TIME_QUERY_CONFIG


class AppExplosionAnalyzer(NetworkAnalyzer, PatternAnalyzer):
    """最终修复版APP分析器 - 支持真实首次发现时间"""

    def __init__(self):
        super().__init__("AppExplosionAnalyzer")
        self.db_manager = None  # 将在需要时设置

    def set_db_manager(self, db_manager):
        """设置数据库管理器引用"""
        self.db_manager = db_manager

    def analyze_simplified(self, df: pd.DataFrame) -> Dict[str, Any]:
        """执行简化版综合分析"""
        if not self.validate_data(df, ['final_app_name', 'insert_day']):
            return {}

        self.print_section_header("🆕 新APP综合分析")

        results = {
            'new_apps_detection': self.detect_new_apps_top50(df),
            'growth_monitoring': self.monitor_growth_speed(df),
            'explosion_prediction': self.predict_explosion_risk(df),
        }

        self._print_analysis_summary(results)
        return results

    def analyze(self, df: pd.DataFrame) -> Dict[str, Any]:
        """原有分析方法 - 兼容性保留"""
        return self.analyze_simplified(df)

    def detect_new_apps_top50(self, df: pd.DataFrame, days_threshold: int = TIME_QUERY_CONFIG['default_days']) -> List[
        Dict[str, Any]]:
        """检测新APP TOP50 - 使用真实首次发现时间和统一显示"""
        if not self.validate_data(df, ['final_app_name', 'insert_day']):
            return []

        print(f"🔍 新APP检测 (最近{days_threshold}天)")
        print("=" * 180)

        try:
            # 获取所有APP的真实首次发现时间
            unique_apps = df['final_app_name'].unique().tolist()

            if self.db_manager:
                print("📋 查询APP真实首次发现时间...")
                app_first_times = self.db_manager.query_app_first_appearance(unique_apps)
            else:
                print("⚠️ 数据库管理器不可用，使用当前数据集的首次出现时间")
                app_first_times = {}

            # 检测最近出现的新APP
            threshold_date = datetime.now() - timedelta(days=days_threshold)
            new_apps_data = []

            for app_name in unique_apps:
                # 使用真实首次发现时间或当前数据集的首次出现时间
                if app_name in app_first_times:
                    first_seen = app_first_times[app_name]
                    if isinstance(first_seen, str):
                        first_seen = pd.to_datetime(first_seen)
                else:
                    # 回退到当前数据集的首次出现时间
                    app_cases = df[df['final_app_name'] == app_name]
                    first_seen = app_cases['insert_day'].min()

                # 检查是否为新APP
                if first_seen >= threshold_date:
                    app_cases = df[df['final_app_name'] == app_name]
                    case_count = len(app_cases)

                    # 金额统计
                    total_amount = 0
                    avg_amount = 0
                    if 'involved_amount' in app_cases.columns:
                        amounts = app_cases['involved_amount'].dropna()
                        amounts = amounts[amounts > 0]
                        if not amounts.empty:
                            total_amount = amounts.sum()
                            avg_amount = amounts.mean()

                    # 地域扩散统计
                    area_count = 0
                    if 'occurrence_area' in app_cases.columns:
                        area_count = app_cases['occurrence_area'].nunique()

                    # 增长速度统计
                    days_active = (datetime.now() - first_seen).days + 1
                    daily_growth = case_count / max(days_active, 1)

                    # 评估风险等级
                    risk_level = self._assess_new_app_risk(case_count, total_amount, area_count, daily_growth)

                    new_apps_data.append({
                        'app_name': app_name,
                        'first_seen': first_seen,
                        'case_count': case_count,
                        'total_amount': total_amount,
                        'avg_amount': avg_amount,
                        'area_count': area_count,
                        'daily_growth': daily_growth,
                        'days_active': days_active,
                        'risk_level': risk_level
                    })

            if not new_apps_data:
                print(f"✅ 最近{days_threshold}天未发现新APP")
                return []

            total_new_apps = len(new_apps_data)
            print(f"🔍 发现 {total_new_apps} 个新APP")

            # 按风险等级和案件数排序
            new_apps_data.sort(key=lambda x: (
                {'高': 3, '中': 2, '低': 1}.get(x['risk_level'], 0),
                x['case_count']
            ), reverse=True)

            # 使用统一显示对齐工具
            try:
                from utils.alignment_display import print_aligned_new_apps
                print_aligned_new_apps(new_apps_data, max_display=50)
            except ImportError:
                # 回退到原始显示方式
                self._print_new_apps_fallback(new_apps_data[:50], total_new_apps)

            return new_apps_data

        except Exception as e:
            print(f"❌ 新APP检测失败: {e}")
            return []

    def monitor_growth_speed(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """APP增长速度监控 - 使用统一显示"""
        if not self.validate_data(df, ['final_app_name', 'insert_day']):
            return []

        print(f"\n📈 APP增长速度监控")
        print("=" * 160)

        growth_analysis = []

        # 获取最近30天的APP进行分析
        recent_date = datetime.now() - timedelta(days=30)
        recent_apps = df[df['insert_day'] >= recent_date]['final_app_name'].unique()

        for app_name in recent_apps:
            app_data = df[df['final_app_name'] == app_name].sort_values('insert_day')
            if len(app_data) < 10:  # 数据太少，跳过
                continue

            # 计算详细增长指标
            growth_metrics = self._calculate_growth_metrics(app_data)

            # 判断是否异常增长
            if self._is_abnormal_growth(growth_metrics):
                monitoring_basis = self._get_monitoring_basis(growth_metrics)

                growth_analysis.append({
                    'app_name': app_name,
                    'daily_growth_rate': growth_metrics['daily_growth_rate'] * 100,
                    'acceleration': growth_metrics['acceleration'],
                    'total_cases': len(app_data),
                    'growth_pattern': growth_metrics['pattern'],
                    'monitoring_basis': monitoring_basis,
                    'alert_level': 'critical' if growth_metrics['acceleration'] > 5 else 'high'
                })

        # 按加速度排序
        growth_analysis.sort(key=lambda x: x['acceleration'], reverse=True)

        if growth_analysis:
            # 使用统一显示对齐工具
            try:
                from utils.alignment_display import print_aligned_growth_monitoring
                print_aligned_growth_monitoring(growth_analysis, max_display=20)
            except ImportError:
                # 回退到原始显示方式
                self._print_growth_monitoring_fallback(growth_analysis[:20])

            print(f"\n📊 增长速度监控说明:")
            print(f"   🔍 监控方式: 分析APP案件数变化趋势和增长加速度")
            print(f"   📈 计算方式: 日增长率 = (最近3天平均 - 前期3天平均) / 前期3天平均")
            print(f"   ⚡ 加速度: 案件数增长的二阶导数，反映增长速度变化")
            print(f"   🎯 判断标准: 日增长率>150% 或 加速度>2 视为异常快速增长")
        else:
            print("✅ 未发现异常增长APP")

        return growth_analysis

    def predict_explosion_risk(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """APP爆发风险预测 - 使用统一显示"""
        if not self.validate_data(df, ['final_app_name', 'insert_day']):
            return []

        print(f"\n🎯 APP爆发风险预测")
        print("=" * 150)

        explosion_predictions = []

        # 获取最近14天出现的APP
        recent_date = datetime.now() - timedelta(days=14)
        recent_apps_data = df[df['insert_day'] >= recent_date]

        for app_name in recent_apps_data['final_app_name'].unique():
            app_data = recent_apps_data[recent_apps_data['final_app_name'] == app_name]
            if len(app_data) < 5:
                continue

            # 简化的风险评估
            features = self._extract_simple_features(app_data)
            risk_score = self._calculate_explosion_risk_score(features)

            if risk_score > 0.5:  # 风险阈值
                explosion_predictions.append({
                    'app_name': app_name,
                    'explosion_probability': min(risk_score, 1.0),
                    'similar_pattern': '高增长模式',
                    'prediction_basis': self._get_prediction_basis(features),
                    'key_features': self._get_key_features(features),
                    'predicted_timeframe': '1-2周内',
                    'risk_level': 'critical' if risk_score > 0.8 else 'high'
                })

        # 按爆发概率排序
        explosion_predictions.sort(key=lambda x: x['explosion_probability'], reverse=True)

        if explosion_predictions:
            # 使用统一显示对齐工具
            try:
                from utils.alignment_display import print_aligned_explosion_prediction
                print_aligned_explosion_prediction(explosion_predictions, max_display=20)
            except ImportError:
                # 回退到原始显示方式
                self._print_explosion_prediction_fallback(explosion_predictions[:20])

            print(f"\n🔮 爆发风险预测说明:")
            print(f"   🎯 预测方法: 基于案件数增长特征和活跃度评估")
            print(f"   📊 分析依据: 案件数增长轨迹、时间分布特征")
            print(f"   🔍 特征识别: 初期快速增长、案件数密集爆发")
            print(f"   ⚠️ 风险等级: 概率>80%为critical，50-80%为high")
        else:
            print("✅ 暂无高风险预测")

        return explosion_predictions

    def _detect_case_spikes_improved(self, df: pd.DataFrame, current_time: datetime) -> List[Dict[str, Any]]:
        """改进的案件数激增检测 - 统一描述口径"""
        alerts = []

        try:
            # 固定使用3天和7天的逻辑
            recent_days = 3
            history_days = 7

            # 最近3天数据
            recent_start_date = current_time - timedelta(days=recent_days)
            recent_cases = df[df['insert_day'] >= recent_start_date]

            # 近7天历史数据（排除最近3天）
            history_start_date = current_time - timedelta(days=history_days + recent_days)
            history_end_date = current_time - timedelta(days=recent_days)
            history_cases = df[(df['insert_day'] >= history_start_date) & (df['insert_day'] < history_end_date)]

            print(f"   最近3天数据: {len(recent_cases)} 条")
            print(f"   历史7天数据: {len(history_cases)} 条")

            if len(recent_cases) > 0 and len(history_cases) > 0:
                recent_app_counts = recent_cases['final_app_name'].value_counts()
                history_app_counts = history_cases['final_app_name'].value_counts()

                # 计算历史7天日均
                history_daily_avg = history_app_counts / history_days

                from config import ALERT_THRESHOLDS
                significant_apps = recent_app_counts[recent_app_counts >= ALERT_THRESHOLDS['new_app_cases']].index

                for app in significant_apps:
                    recent_count = recent_app_counts[app]
                    historical_daily_avg = history_daily_avg.get(app, 0)

                    if historical_daily_avg > 0:
                        expected_recent_count = historical_daily_avg * recent_days
                        spike_ratio = recent_count / expected_recent_count

                        if spike_ratio > ALERT_THRESHOLDS['case_spike']:
                            severity = '高' if spike_ratio > 5 else '中'
                            alerts.append({
                                'type': '案件数激增',
                                'app_name': app,
                                'severity': severity,
                                'description': f'最近3天案件数({recent_count})起较历史(近7天)日均({historical_daily_avg:.1f})增长{spike_ratio:.1f}倍'
                            })
                    elif recent_count >= 10:  # 新出现的高频APP
                        alerts.append({
                            'type': '新高频APP',
                            'app_name': app,
                            'severity': '高',
                            'description': f'最近3天案件数({recent_count})起超出风险阈值'
                        })

        except Exception as e:
            print(f"⚠️ 案件激增检测失败: {e}")

        return alerts

    def _print_suspicious_results(self, alerts: List[Dict[str, Any]]):
        """打印可疑APP监测结果 - 使用统一显示"""
        if alerts:
            # 使用统一显示对齐工具
            try:
                from utils.alignment_display import print_aligned_suspicious_patterns
                print_aligned_suspicious_patterns(alerts, max_display=50)
            except ImportError:
                # 回退到原始显示方式
                self._print_suspicious_results_fallback(alerts)
        else:
            print("✅ 未发现明显的可疑APP风险")

    # 回退显示方法
    def _print_new_apps_fallback(self, new_apps_data: List[Dict[str, Any]], total_count: int):
        """新APP显示回退方法"""
        print(f"📋 TOP50 新APP (按风险等级和案件数排序，共{total_count}个):")
        print("=" * 180)
        print(
            "序号 APP名称                      风险等级   案件数  日均案件   涉案金额(万)     平均金额(万)     地区数  首次发现时间")
        print("-" * 180)

        for i, app in enumerate(new_apps_data, 1):
            risk_emoji = {'高': '🔴', '中': '🟡', '低': '🟢'}.get(app['risk_level'], '⚪')
            daily_avg = app['case_count'] / max(app['days_active'], 1)
            total_amount_wan = app['total_amount'] / 10000
            avg_amount_wan = app['avg_amount'] / 10000
            first_seen_str = app['first_seen'].strftime('%m-%d')

            print(
                f"{i:>2}   {app['app_name']:<28} {risk_emoji}{app['risk_level']:<5}      {app['case_count']:>3}     {daily_avg:>6.1f}      {total_amount_wan:>10.2f}        {avg_amount_wan:>10.2f}        {app['area_count']:>3}    {first_seen_str}")

        # 风险统计
        risk_stats = {'高': 0, '中': 0, '低': 0}
        for app in new_apps_data:
            risk_stats[app['risk_level']] = risk_stats.get(app['risk_level'], 0) + 1

        print(
            f"\n📊 风险等级分布: 高风险 {risk_stats['高']}个 | 中风险 {risk_stats['中']}个 | 低风险 {risk_stats['低']}个")

    def _print_growth_monitoring_fallback(self, growth_analysis: List[Dict[str, Any]]):
        """增长监控显示回退方法"""
        print(f"📈 发现 {len(growth_analysis)} 个异常增长APP:")
        print("=" * 160)
        print("序号 APP名称                      增长模式     日增长率(%)  加速度   总案件数  预警等级   监控依据")
        print("-" * 160)

        for i, app in enumerate(growth_analysis, 1):
            pattern_emoji = {'explosive': '💥', 'rapid': '🚀', 'steady': '📈', 'stable': '➡️'}.get(
                app['growth_pattern'], '❓')
            alert_emoji = {'critical': '🔴', 'high': '🟡'}.get(app['alert_level'], '⚪')
            monitoring_basis = app['monitoring_basis'][:30]

            print(
                f"{i:>2}   {app['app_name']:<28} {pattern_emoji}{app['growth_pattern']:<7}     {app['daily_growth_rate']:>8.1f}     {app['acceleration']:>5.2f}        {app['total_cases']:>3}  {alert_emoji}{app['alert_level']:<6}   {monitoring_basis}")

    def _print_explosion_prediction_fallback(self, predictions: List[Dict[str, Any]]):
        """爆发预测显示回退方法"""
        print(f"🎯 发现 {len(predictions)} 个爆发风险APP:")
        print("=" * 150)
        print(
            "序号 APP名称                      爆发概率   相似模式       预测依据           关键特征           预测时间")
        print("-" * 150)

        for i, pred in enumerate(predictions, 1):
            prob_str = f"{pred['explosion_probability']:.1%}"
            similar_pattern = pred['similar_pattern'][:12]
            prediction_basis = pred['prediction_basis'][:16]
            key_features = pred['key_features'][:16]
            timeframe = pred['predicted_timeframe']

            print(
                f"{i:>2}   {pred['app_name']:<28} {prob_str:>6}     {similar_pattern:<12}   {prediction_basis:<16}   {key_features:<16}   {timeframe}")

    def _print_suspicious_results_fallback(self, alerts: List[Dict[str, Any]]):
        """可疑APP监测结果显示回退方法"""
        total_alerts = len(alerts)
        # 按严重程度和类型排序
        severity_order = {'高': 3, '中': 2, '低': 1}
        alerts.sort(key=lambda x: (
            severity_order.get(x['severity'], 0),
            x['type'],
            x['app_name']
        ), reverse=True)

        print(f"🚨 发现 {total_alerts} 个可疑APP风险:")
        print("=" * 140)
        print("序号 类型         APP名称                      严重程度   描述")
        print("-" * 140)

        for i, alert in enumerate(alerts[:50], 1):
            severity_emoji = {'高': '🔴', '中': '🟡', '低': '🟢'}.get(alert['severity'], '⚪')
            description = alert['description'][:60] + "..." if len(alert['description']) > 63 else alert['description']

            print(
                f"{i:>2}   {alert['type']:<10} {alert['app_name']:<28} {severity_emoji}{alert['severity']:<5}      {description}")

        # 统计信息
        high_alerts = len([alert for alert in alerts if alert['severity'] == '高'])
        medium_alerts = len([alert for alert in alerts if alert['severity'] == '中'])
        low_alerts = len([alert for alert in alerts if alert['severity'] == '低'])

        print(f"\n📊 风险等级分布: 高风险 {high_alerts}个 | 中风险 {medium_alerts}个 | 低风险 {low_alerts}个")

    # 保持所有辅助方法不变
    def _assess_new_app_risk(self, case_count: int, total_amount: float,
                             area_count: int, daily_growth: float) -> str:
        """评估新APP风险等级"""
        risk_score = 0

        if case_count >= 50:
            risk_score += 40
        elif case_count >= 20:
            risk_score += 30
        elif case_count >= 10:
            risk_score += 20
        elif case_count >= 5:
            risk_score += 10

        if total_amount >= 1000000:
            risk_score += 30
        elif total_amount >= 500000:
            risk_score += 20
        elif total_amount >= 100000:
            risk_score += 10

        if daily_growth >= 10:
            risk_score += 20
        elif daily_growth >= 5:
            risk_score += 15
        elif daily_growth >= 2:
            risk_score += 10

        if area_count >= 10:
            risk_score += 10
        elif area_count >= 5:
            risk_score += 5

        if risk_score >= 70:
            return '高'
        elif risk_score >= 40:
            return '中'
        else:
            return '低'

    def _calculate_growth_metrics(self, app_data: pd.DataFrame) -> Dict[str, Any]:
        """计算增长指标"""
        daily_counts = app_data.groupby(app_data['insert_day'].dt.date).size()

        metrics = {
            'daily_growth_rate': 0,
            'acceleration': 0,
            'pattern': 'stable'
        }

        if len(daily_counts) >= 7:
            recent_avg = daily_counts.tail(3).mean()
            early_avg = daily_counts.head(3).mean()
            if early_avg > 0:
                metrics['daily_growth_rate'] = (recent_avg - early_avg) / early_avg

            if len(daily_counts) >= 5:
                diffs = daily_counts.diff().dropna()
                if len(diffs) >= 2:
                    acceleration = diffs.diff().dropna().mean()
                    metrics['acceleration'] = abs(acceleration)

            if metrics['daily_growth_rate'] > 3:
                metrics['pattern'] = 'explosive'
            elif metrics['daily_growth_rate'] > 1.5:
                metrics['pattern'] = 'rapid'
            elif metrics['daily_growth_rate'] > 0.5:
                metrics['pattern'] = 'steady'

        return metrics

    def _is_abnormal_growth(self, metrics: Dict[str, Any]) -> bool:
        """判断是否异常增长"""
        return (
                metrics['daily_growth_rate'] > 1.5 or
                metrics['acceleration'] > 2 or
                metrics['pattern'] in ['explosive', 'rapid']
        )

    def _get_monitoring_basis(self, metrics: Dict[str, Any]) -> str:
        """获取监控依据说明"""
        basis_parts = []

        if metrics['daily_growth_rate'] > 1.5:
            basis_parts.append(f"日增长率{metrics['daily_growth_rate'] * 100:.1f}%")

        if metrics['acceleration'] > 2:
            basis_parts.append(f"加速度{metrics['acceleration']:.1f}")

        if metrics['pattern'] in ['explosive', 'rapid']:
            basis_parts.append(f"增长模式:{metrics['pattern']}")

        return " | ".join(basis_parts) if basis_parts else "案件数时间序列分析"

    def _extract_simple_features(self, app_data: pd.DataFrame) -> Dict[str, Any]:
        """提取简化APP特征"""
        features = {}
        time_span = (app_data['insert_day'].max() - app_data['insert_day'].min()).days + 1
        features['time_span'] = time_span
        features['daily_avg'] = len(app_data) / max(time_span, 1)
        features['case_count'] = len(app_data)
        return features

    def _calculate_explosion_risk_score(self, features: Dict[str, Any]) -> float:
        """计算爆发风险评分"""
        score = 0
        daily_avg = features.get('daily_avg', 0)
        case_count = features.get('case_count', 0)

        if daily_avg >= 10:
            score += 0.4
        elif daily_avg >= 5:
            score += 0.3
        elif daily_avg >= 2:
            score += 0.2

        if case_count >= 50:
            score += 0.4
        elif case_count >= 20:
            score += 0.3
        elif case_count >= 10:
            score += 0.2

        return min(score, 1.0)

    def _get_prediction_basis(self, features: Dict[str, Any]) -> str:
        """获取预测依据"""
        daily_avg = features.get('daily_avg', 0)
        if daily_avg >= 5:
            return "高频案件发生"
        elif daily_avg >= 2:
            return "中频案件发生"
        else:
            return "增长趋势分析"

    def _get_key_features(self, features: Dict[str, Any]) -> str:
        """获取关键特征"""
        daily_avg = features.get('daily_avg', 0)
        return f"日均{daily_avg:.1f}起"

    def _detect_amount_anomalies(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """检测金额异常"""
        alerts = []
        if 'involved_amount' not in df.columns:
            return alerts

        try:
            amount_df = df.dropna(subset=['involved_amount'])
            amount_df = amount_df[amount_df['involved_amount'] > 0]

            if len(amount_df) == 0:
                return alerts

            high_amount_threshold = 500000
            high_amount_cases = amount_df[amount_df['involved_amount'] >= high_amount_threshold]

            if not high_amount_cases.empty:
                app_high_amounts = high_amount_cases.groupby('final_app_name').size()
                for app, count in app_high_amounts.items():
                    if count >= 2:
                        max_amount = high_amount_cases[high_amount_cases['final_app_name'] == app][
                            'involved_amount'].max()
                        alerts.append({
                            'type': '异常高额',
                            'app_name': app,
                            'severity': '高' if max_amount >= 1000000 else '中',
                            'description': f'{app} 出现{count}起高额案件，最高{max_amount:,.0f}元'
                        })
        except Exception as e:
            print(f"⚠️ 金额异常检测失败: {e}")
        return alerts

    def _detect_hyperactive_apps(self, df: pd.DataFrame, current_time: datetime) -> List[Dict[str, Any]]:
        """检测异常活跃APP - 修改为3天统计"""
        alerts = []
        try:
            # 修改为最近3天
            recent_3days = current_time - timedelta(days=3)
            recent_data = df[df['insert_day'] >= recent_3days]

            if len(recent_data) == 0:
                return alerts

            app_activity = recent_data.groupby('final_app_name').size()
            hyperactive_apps = app_activity[app_activity >= 30]  # 3天内30起以上

            for app, count in hyperactive_apps.items():
                daily_avg = count / 3  # 3天日均
                alerts.append({
                    'type': '异常活跃',
                    'app_name': app,
                    'severity': '高' if daily_avg >= 20 else '中',
                    'description': f'最近3天异常活跃，日均{daily_avg:.1f}起案件，共{count}起'
                })
        except Exception as e:
            print(f"⚠️ 异常活跃检测失败: {e}")
        return alerts

    def _print_analysis_summary(self, results: Dict[str, Any]):
        """打印分析摘要"""
        new_apps_count = len(results.get('new_apps_detection', []))
        growth_alerts_count = len(results.get('growth_monitoring', []))
        explosion_predictions_count = len(results.get('explosion_prediction', []))
        suspicious_count = len(results.get('suspicious_monitoring', []))

        print(f"\n📊 新APP综合分析摘要:")
        print(f"   🆕 新发现APP: {new_apps_count} 个")
        print(f"   📈 异常增长APP: {growth_alerts_count} 个")
        print(f"   🎯 爆发风险APP: {explosion_predictions_count} 个")
        print(f"   🚨 可疑APP风险: {suspicious_count} 个")

        try:
            from core.time_query_manager import get_time_description
            description = get_time_description()
            print(f"   📅 分析时间范围: {description}")
        except ImportError:
            print(f"   📅 分析时间范围: 默认{TIME_QUERY_CONFIG['default_days']}天")