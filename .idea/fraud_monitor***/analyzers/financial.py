"""
APP涉案监控系统 - 资金分析器 (最终修复版)
分析涉案金额、损失等级、APP资金影响等，增加对比分析功能
解决对比分析无数据问题，使用更灵活的数据获取策略
"""
import pandas as pd
import numpy as np
from datetime import date, timedelta
from typing import Dict, List, Any, Optional
from .base import StatisticalAnalyzer, RiskAnalyzer
from core.models import FinancialAnalysis, DataModelFactory
from config import AMOUNT_LEVELS, ALERT_THRESHOLDS


class FinancialAnalyzer(StatisticalAnalyzer, RiskAnalyzer):
    """资金分析器 - 分析涉案金额和资金影响，支持对比分析"""

    def __init__(self):
        super().__init__("FinancialAnalyzer")
        self._cached_data = None  # 缓存数据避免重复查询
        self.comparison_data = {}  # 存储对比分析数据

    def analyze(self, df: pd.DataFrame) -> Optional[FinancialAnalysis]:
        """执行资金影响分析"""
        if not self.validate_data(df, ['involved_amount']):
            return None

        self.print_section_header("💰 资金影响分析")

        # 过滤有效金额数据
        amount_data = df['involved_amount'].dropna()
        amount_data = amount_data[amount_data > 0]

        if amount_data.empty:
            print("⚠️ 无有效金额数据")
            return None

        # 执行各项分析
        basic_stats = self._analyze_basic_statistics(amount_data)
        level_analysis = self._analyze_amount_levels(df)
        app_analysis = self._analyze_app_amounts(df)
        trend_analysis = self._analyze_amount_trends(df)

        # 创建财务分析结果
        financial_result = DataModelFactory.create_financial_analysis(df)

        # 输出分析结果
        self._print_analysis_results(basic_stats, level_analysis, app_analysis)

        return financial_result

    def analyze_with_comparison(self, df: pd.DataFrame, analysis_week_start: date,
                                analysis_week_end: date, data_processor=None) -> Optional[FinancialAnalysis]:
        """执行带对比的资金分析 - 最终修复版本，解决无数据问题"""
        if not self.validate_data(df, ['involved_amount']):
            return None

        self.print_section_header("💰 资金影响分析 (含对比)")

        # 存储数据处理器引用
        self._data_processor = data_processor

        # 过滤有效金额数据
        amount_data = df['involved_amount'].dropna()
        amount_data = amount_data[amount_data > 0]

        if amount_data.empty:
            print("⚠️ 无有效金额数据")
            return None

        # 执行基础分析
        basic_stats = self._analyze_basic_statistics(amount_data)
        level_analysis = self._analyze_amount_levels(df)
        app_analysis = self._analyze_app_amounts(df)

        # 修复对比分析：使用更灵活的策略
        self._analyze_comparisons_enhanced(basic_stats, analysis_week_start, analysis_week_end)

        # 创建财务分析结果，并附加对比数据
        financial_result = DataModelFactory.create_financial_analysis(df)

        # 将对比数据附加到结果中
        if hasattr(financial_result, '__dict__'):
            financial_result.comparison_data = self.comparison_data
        else:
            # 如果无法直接附加属性，创建一个新的字典结构
            financial_result = {
                'basic_analysis': financial_result,
                'comparison_data': self.comparison_data
            }

        # 输出分析结果
        self._print_analysis_results(basic_stats, level_analysis, app_analysis)

        return financial_result

    def _analyze_comparisons_enhanced(self, current_stats: Dict[str, Any],
                                      analysis_week_start: date, analysis_week_end: date):
        """增强的对比分析 - 使用多种策略确保能获取到对比数据"""
        try:
            # 计算所有需要的时间范围
            today = date.today()

            # 策略1：优先使用精确的前一周时间范围
            prev_week_start = analysis_week_start - timedelta(days=7)
            prev_week_end = analysis_week_end - timedelta(days=7)

            # 策略2：如果精确范围可能无数据，尝试更大的范围
            # 前一周的扩展范围（前一周的前后各1天）
            extended_prev_start = prev_week_start - timedelta(days=1)
            extended_prev_end = prev_week_end + timedelta(days=1)

            # 本月时间范围
            current_month_start = date(today.year, today.month, 1)
            current_month_end = analysis_week_end

            # 上月同期时间范围
            if today.month == 1:
                prev_month = 12
                prev_year = today.year - 1
            else:
                prev_month = today.month - 1
                prev_year = today.year

            prev_month_start = date(prev_year, prev_month, 1)
            try:
                import calendar
                last_day_prev_month = calendar.monthrange(prev_year, prev_month)[1]
                analysis_day = analysis_week_end.day
                prev_month_end_day = min(analysis_day, last_day_prev_month)
                prev_month_end = date(prev_year, prev_month, prev_month_end_day)
            except:
                prev_month_end = prev_month_start + timedelta(days=analysis_week_end.day - 1)

            # print(f"🔍 对比分析时间范围计算:")
            # print(f"   当前分析期: {analysis_week_start} 至 {analysis_week_end}")
            # print(f"   前一周: {prev_week_start} 至 {prev_week_end}")
            # print(f"   前一周(扩展): {extended_prev_start} 至 {extended_prev_end}")
            # print(f"   本月: {current_month_start} 至 {current_month_end}")
            # print(f"   上月同期: {prev_month_start} 至 {prev_month_end}")

            # 检查数据处理器是否可用
            if not self._data_processor:
                print("⚠️ 数据处理器不可用，跳过对比分析")
                self.comparison_data = {'weekly': {}, 'monthly': {}}
                return

            # 策略3：使用数据处理器的对比数据获取方法
            if hasattr(self._data_processor, 'get_comparison_data'):
                # print("🔍 使用数据处理器的对比数据获取方法...")

                # 周度对比数据获取
                weekly_comparison_data = self._data_processor.get_comparison_data(
                    analysis_week_start, analysis_week_end,
                    prev_week_start, prev_week_end
                )

                # 月度对比数据获取
                monthly_comparison_data = self._data_processor.get_comparison_data(
                    current_month_start, current_month_end,
                    prev_month_start, prev_month_end
                )

                # 处理周度对比
                weekly_comparison = self._process_comparison_data(
                    current_stats,
                    weekly_comparison_data.get('previous', pd.DataFrame()),
                    analysis_week_start, analysis_week_end,
                    prev_week_start, prev_week_end,
                    "周度"
                )

                # 处理月度对比
                monthly_comparison = self._process_comparison_data_monthly(
                    monthly_comparison_data.get('current', pd.DataFrame()),
                    monthly_comparison_data.get('previous', pd.DataFrame()),
                    current_month_start, current_month_end,
                    prev_month_start, prev_month_end,
                    "月度"
                )

            else:
                # 回退策略：逐个获取数据
                print("🔍 使用回退策略获取对比数据...")

                # 尝试获取前一周数据
                prev_week_df = self._try_get_data_by_range(prev_week_start, prev_week_end, "前一周")
                if prev_week_df.empty:
                    # 尝试扩展范围
                    prev_week_df = self._try_get_data_by_range(extended_prev_start, extended_prev_end, "前一周(扩展)")

                # 获取月度对比数据
                current_month_df = self._try_get_data_by_range(current_month_start, current_month_end, "本月")
                prev_month_df = self._try_get_data_by_range(prev_month_start, prev_month_end, "上月同期")

                # 处理对比数据
                weekly_comparison = self._process_comparison_data(
                    current_stats, prev_week_df,
                    analysis_week_start, analysis_week_end,
                    prev_week_start, prev_week_end,
                    "周度"
                )

                monthly_comparison = self._process_comparison_data_monthly(
                    current_month_df, prev_month_df,
                    current_month_start, current_month_end,
                    prev_month_start, prev_month_end,
                    "月度"
                )

            # 保存对比数据
            self.comparison_data = {
                'weekly': weekly_comparison,
                'monthly': monthly_comparison
            }

            # 显示对比分析结果
            self._print_comparison_results(weekly_comparison, monthly_comparison)

        except Exception as e:
            print(f"⚠️ 对比分析失败: {e}")
            self.comparison_data = {'weekly': {}, 'monthly': {}}

    def _try_get_data_by_range(self, start_date: date, end_date: date, period_name: str) -> pd.DataFrame:
        """尝试获取指定范围的数据"""
        try:
            if hasattr(self._data_processor, 'get_data_by_date_range'):
                df = self._data_processor.get_data_by_date_range(start_date, end_date)
            else:
                # 计算天数并使用常规方法
                days = (end_date - start_date).days + 1
                df = self._data_processor.get_comprehensive_data(days=days)

                # 手动过滤日期范围
                if df is not None and not df.empty and 'insert_day' in df.columns:
                    df = self._filter_data_by_date_range(df, start_date, end_date)

            if df is not None and not df.empty:
                print(f"✅ {period_name}数据: {len(df)} 条记录")
                return df
            else:
                print(f"⚠️ {period_name}无数据")
                return pd.DataFrame()

        except Exception as e:
            print(f"⚠️ {period_name}数据获取失败: {e}")
            return pd.DataFrame()

    def _process_comparison_data(self, current_stats: Dict[str, Any], prev_df: pd.DataFrame,
                                 current_start: date, current_end: date,
                                 prev_start: date, prev_end: date,
                                 period_type: str) -> Dict[str, Any]:
        """处理对比数据并生成对比结果"""
        try:
            if prev_df.empty:
                print(f"⚠️ {period_type}对比: 对比期无数据")
                return {}

            # 计算对比期统计
            prev_amount_data = prev_df['involved_amount'].dropna()
            prev_amount_data = prev_amount_data[prev_amount_data > 0]

            if prev_amount_data.empty:
                print(f"⚠️ {period_type}对比: 对比期无有效金额数据")
                return {}

            prev_stats = {
                'total_amount': prev_amount_data.sum(),
                'avg_amount': prev_amount_data.mean(),
                'case_count': len(prev_amount_data)
            }

            return {
                'current': current_stats,
                'previous': prev_stats,
                'current_period': f"{current_start.strftime('%m月%d日')} - {current_end.strftime('%m月%d日')}",
                'previous_period': f"{prev_start.strftime('%m月%d日')} - {prev_end.strftime('%m月%d日')}",
                'period_type': period_type
            }

        except Exception as e:
            print(f"⚠️ {period_type}对比数据处理失败: {e}")
            return {}

    def _process_comparison_data_monthly(self, current_month_df: pd.DataFrame, prev_month_df: pd.DataFrame,
                                         current_start: date, current_end: date,
                                         prev_start: date, prev_end: date,
                                         period_type: str) -> Dict[str, Any]:
        """处理月度对比数据"""
        try:
            if current_month_df.empty or prev_month_df.empty:
                print(f"⚠️ {period_type}对比: 缺少月度数据")
                return {}

            # 计算本月统计
            current_amount_data = current_month_df['involved_amount'].dropna()
            current_amount_data = current_amount_data[current_amount_data > 0]

            # 计算上月同期统计
            prev_amount_data = prev_month_df['involved_amount'].dropna()
            prev_amount_data = prev_amount_data[prev_amount_data > 0]

            if current_amount_data.empty or prev_amount_data.empty:
                print(f"⚠️ {period_type}对比: 缺少有效金额数据")
                return {}

            current_month_stats = {
                'total_amount': current_amount_data.sum(),
                'avg_amount': current_amount_data.mean(),
                'case_count': len(current_amount_data)
            }

            prev_month_stats = {
                'total_amount': prev_amount_data.sum(),
                'avg_amount': prev_amount_data.mean(),
                'case_count': len(prev_amount_data)
            }

            return {
                'current': current_month_stats,
                'previous': prev_month_stats,
                'current_period': f"{current_start.strftime('%m月%d日')} - {current_end.strftime('%m月%d日')}",
                'previous_period': f"{prev_start.strftime('%m月%d日')} - {prev_end.strftime('%m月%d日')}",
                'period_type': period_type
            }

        except Exception as e:
            print(f"⚠️ {period_type}对比数据处理失败: {e}")
            return {}

    def _print_comparison_results(self, weekly_comparison: Dict[str, Any], monthly_comparison: Dict[str, Any]):
        """打印对比分析结果"""
        # 打印周度对比
        if weekly_comparison:
            print(f"\n📊 周度对比分析")
            print("-" * 40)
            self._print_single_comparison(weekly_comparison)
        else:
            print(f"\n📊 周度对比分析")
            print("-" * 40)
            print("⚠️ 周度对比无法进行 - 缺少对比期数据")

        # 打印月度对比
        if monthly_comparison:
            print(f"\n📅 月度对比分析")
            print("-" * 40)
            self._print_single_comparison(monthly_comparison)
        else:
            print(f"\n📅 月度对比分析")
            print("-" * 40)
            print("⚠️ 月度对比无法进行 - 缺少对比期数据")

    def _print_single_comparison(self, comparison_data: Dict[str, Any]):
        """打印单个对比分析结果"""
        try:
            current = comparison_data.get('current', {})
            previous = comparison_data.get('previous', {})
            current_period = comparison_data.get('current_period', '当前期')
            previous_period = comparison_data.get('previous_period', '对比期')

            print(f"当前期: {current_period}")
            print(f"对比期: {previous_period}")

            # 计算并显示变化
            total_change = self._calculate_change(current.get('total_amount', 0), previous.get('total_amount', 0))
            avg_change = self._calculate_change(current.get('avg_amount', 0), previous.get('avg_amount', 0))
            count_change = self._calculate_change(current.get('case_count', 0), previous.get('case_count', 0))

            print(
                f"📈 总金额变化: {self._format_comparison(current.get('total_amount', 0), previous.get('total_amount', 0), total_change, '元')}")
            print(
                f"📊 平均金额变化: {self._format_comparison(current.get('avg_amount', 0), previous.get('avg_amount', 0), avg_change, '元')}")
            print(
                f"📝 案件数变化: {self._format_comparison(current.get('case_count', 0), previous.get('case_count', 0), count_change, '起')}")

        except Exception as e:
            print(f"⚠️ 对比结果显示失败: {e}")

    def _filter_data_by_date_range(self, df: pd.DataFrame, start_date: date, end_date: date) -> pd.DataFrame:
        """根据日期范围过滤数据"""
        try:
            if df is None or df.empty or 'insert_day' not in df.columns:
                return pd.DataFrame()

            start_datetime = pd.to_datetime(start_date)
            end_datetime = pd.to_datetime(end_date) + pd.Timedelta(hours=23, minutes=59, seconds=59)
            filtered_df = df[(df['insert_day'] >= start_datetime) & (df['insert_day'] <= end_datetime)]
            return filtered_df
        except Exception as e:
            print(f"⚠️ 数据过滤失败: {e}")
            return pd.DataFrame()

    def _calculate_change(self, current: float, previous: float) -> float:
        """计算变化百分比"""
        if previous == 0:
            return float('inf') if current > 0 else 0
        return ((current - previous) / previous) * 100

    def _format_comparison(self, current: float, previous: float, change: float, unit: str) -> str:
        """格式化对比显示"""
        if change == float('inf'):
            return f"当前: {current:,.0f}{unit} vs 对比期: 0{unit} (🆕 新增)"
        elif change > 0:
            return f"当前: {current:,.0f}{unit} vs 对比期: {previous:,.0f}{unit} (📈 +{change:.1f}%)"
        elif change < 0:
            return f"当前: {current:,.0f}{unit} vs 对比期: {previous:,.0f}{unit} (📉 {change:.1f}%)"
        else:
            return f"当前: {current:,.0f}{unit} vs 对比期: {previous:,.0f}{unit} (➡️ 持平)"

    def _analyze_basic_statistics(self, amount_data: pd.Series) -> Dict[str, Any]:
        """分析基础统计信息"""
        try:
            stats = {
                'total_amount': amount_data.sum(),
                'avg_amount': amount_data.mean(),
                'median_amount': amount_data.median(),
                'max_amount': amount_data.max(),
                'min_amount': amount_data.min(),
                'case_count': len(amount_data),
                'std_amount': amount_data.std()
            }

            print(f"💰 总涉案金额: {stats['total_amount']:,.0f} 元 ({stats['total_amount'] / 10000:.1f} 万)")
            print(f"📊 平均损失: {stats['avg_amount']:,.0f} 元")
            print(f"📍 中位数损失: {stats['median_amount']:,.0f} 元")
            print(f"📈 最高损失: {stats['max_amount']:,.0f} 元")
            print(f"📉 最低损失: {stats['min_amount']:,.0f} 元")
            print(f"📝 有效案件数: {stats['case_count']:,} 起")

            return stats

        except Exception as e:
            print(f"⚠️ 基础统计分析失败: {e}")
            return {}

    def _analyze_amount_levels(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析金额等级分布"""
        try:
            if 'involved_amount' not in df.columns:
                return {}

            amount_data = df['involved_amount'].dropna()
            amount_data = amount_data[amount_data > 0]

            if amount_data.empty:
                return {}

            levels = {
                '小额(<1万)': len(amount_data[amount_data < 10000]),
                '中额(1-10万)': len(amount_data[(amount_data >= 10000) & (amount_data < 100000)]),
                '大额(10-50万)': len(amount_data[(amount_data >= 100000) & (amount_data < 500000)]),
                '巨额(50-100万)': len(amount_data[(amount_data >= 500000) & (amount_data < 1000000)]),
                '特大额(≥100万)': len(amount_data[amount_data >= 1000000])
            }

            total_cases = sum(levels.values())

            print(f"\n💎 金额等级分布:")
            for level, count in levels.items():
                percentage = (count / total_cases * 100) if total_cases > 0 else 0
                print(f"  {level}: {count:,} 起 ({percentage:.1f}%)")

            return levels

        except Exception as e:
            print(f"⚠️ 金额等级分析失败: {e}")
            return {}

    def _analyze_app_amounts(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """分析APP资金影响排名"""
        try:
            if 'final_app_name' not in df.columns or 'involved_amount' not in df.columns:
                return []

            # 按APP聚合金额统计
            app_stats = df.groupby('final_app_name').agg({
                'involved_amount': ['count', 'sum', 'mean', 'max']
            }).round(2)

            app_stats.columns = ['案件数', '总金额', '平均金额', '最高金额']
            app_stats = app_stats.sort_values('总金额', ascending=False)

            # 过滤有效数据
            app_stats = app_stats[app_stats['总金额'] > 0]

            print(f"\n🏆 TOP10 高损失APP:")
            print(f"{'排名':<4} {'APP名称':<20} {'案件数':<8} {'总金额(万)':<12} {'平均金额':<12}")
            print("-" * 65)

            top_apps = []
            for i, (app_name, row) in enumerate(app_stats.head(10).iterrows(), 1):
                total_amount_wan = row['总金额'] / 10000
                print(f"{i:<4} {app_name:<20} {row['案件数']:<8} {total_amount_wan:<12.1f} {row['平均金额']:<12.0f}")

                top_apps.append({
                    'rank': i,
                    'app_name': app_name,
                    'case_count': int(row['案件数']),
                    'total_amount': row['总金额'],
                    'avg_amount': row['平均金额'],
                    'max_amount': row['最高金额']
                })

            return top_apps

        except Exception as e:
            print(f"⚠️ APP资金分析失败: {e}")
            return []

    def _analyze_amount_trends(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析金额趋势（可选扩展）"""
        try:
            # 这里可以添加时间序列趋势分析
            # 例如按日期分组分析金额变化趋势
            return {}
        except Exception as e:
            print(f"⚠️ 趋势分析失败: {e}")
            return {}

    def _print_analysis_results(self, basic_stats: Dict[str, Any],
                                level_analysis: Dict[str, Any],
                                app_analysis: List[Dict[str, Any]]):
        """输出分析结果摘要"""
        try:
            print(f"\n📋 资金分析摘要:")
            print(f"  • 总涉案金额: {basic_stats.get('total_amount', 0) / 10000:.1f} 万元")
            print(f"  • 有效案件数: {basic_stats.get('case_count', 0):,} 起")
            print(f"  • 平均损失: {basic_stats.get('avg_amount', 0):,.0f} 元")

            # 高风险APP提醒
            if app_analysis:
                top_app = app_analysis[0]
                print(f"  • 最高损失APP: {top_app['app_name']} ({top_app['total_amount'] / 10000:.1f}万元)")

            # 大额案件提醒
            if level_analysis:
                high_amount_cases = level_analysis.get('巨额(50-100万)', 0) + level_analysis.get('特大额(≥100万)', 0)
                if high_amount_cases > 0:
                    print(f"  ⚠️ 高额案件(≥50万): {high_amount_cases} 起，需重点关注")

        except Exception as e:
            print(f"⚠️ 结果输出失败: {e}")