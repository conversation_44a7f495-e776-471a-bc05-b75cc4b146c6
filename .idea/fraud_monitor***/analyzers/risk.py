"""
APP涉案监控系统 - 简化版风险分析器
专注受害人画像分析和APP风险排名，移除无意义的综合风险评估
"""
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional
from .base import RiskAnalyzer, StatisticalAnalyzer
from core.models import VictimProfile, DataModelFactory
from config import AGE_GROUPS, ALERT_THRESHOLDS


class SimplifiedRiskAssessmentAnalyzer(RiskAnalyzer, StatisticalAnalyzer):
    """简化版风险评估分析器 - 专注核心功能"""

    def __init__(self):
        super().__init__("SimplifiedRiskAssessmentAnalyzer")

    def analyze(self, df: pd.DataFrame) -> Dict[str, Any]:
        """执行简化版风险分析 - 移除综合风险评估"""
        if not self.validate_data(df, ['final_app_name']):
            return {}

        results = {
            'victim_profile': self.analyze_victim_profile(df),
            'app_risk_ranking': self.analyze_app_risk_ranking(df)
            # 移除: 'risk_assessment': self.generate_risk_assessment(df)  # 用户认为无意义
        }

        return results

    def analyze_victim_profile(self, df: pd.DataFrame) -> Optional[VictimProfile]:
        """分析受害人画像 - 保留并优化"""
        if not self.validate_data(df, ['victim_age']):
            return None

        self.print_section_header("👤 受害人画像分析")

        age_data = df['victim_age'].dropna()
        if age_data.empty:
            print("⚠️ 无有效年龄数据")
            return None

        # 基础年龄统计
        avg_age = age_data.mean()
        median_age = age_data.median()
        min_age = age_data.min()
        max_age = age_data.max()

        print(f"👥 年龄统计:")
        print(f"  平均年龄: {avg_age:.1f}岁")
        print(f"  中位数年龄: {median_age:.1f}岁")
        print(f"  年龄范围: {min_age:.0f}-{max_age:.0f}岁")

        # 年龄段分布分析
        age_distribution = self._analyze_age_distribution_optimized(df, age_data)

        # APP与年龄关联分析
        self._analyze_app_age_correlation(df)

        # 地域与年龄关联分析
        self._analyze_area_age_correlation(df)

        # 创建受害人画像
        victim_profile = DataModelFactory.create_victim_profile(df)

        return victim_profile

    def _analyze_age_distribution_optimized(self, df: pd.DataFrame, age_data: pd.Series) -> Dict[str, int]:
        """优化的年龄段分布分析"""
        try:
            # 使用向量化处理创建年龄段
            conditions = [
                age_data < AGE_GROUPS['bins'][1],
                (age_data >= AGE_GROUPS['bins'][1]) & (age_data < AGE_GROUPS['bins'][2]),
                (age_data >= AGE_GROUPS['bins'][2]) & (age_data < AGE_GROUPS['bins'][3]),
                (age_data >= AGE_GROUPS['bins'][3]) & (age_data < AGE_GROUPS['bins'][4]),
                (age_data >= AGE_GROUPS['bins'][4]) & (age_data < AGE_GROUPS['bins'][5]),
                age_data >= AGE_GROUPS['bins'][5]
            ]

            df_with_age = df.dropna(subset=['victim_age']).copy()
            df_with_age['age_group'] = np.select(conditions, AGE_GROUPS['labels'], default='未知')

            age_dist = df_with_age['age_group'].value_counts()

            print(f"\n👥 年龄段分布:")
            # 按年龄顺序显示
            for age_group in AGE_GROUPS['labels']:
                if age_group in age_dist.index:
                    count = age_dist[age_group]
                    percentage = (count / len(age_data)) * 100
                    print(f"  {age_group}: {count}人 ({percentage:.1f}%)")

            return age_dist.to_dict()

        except Exception as e:
            print(f"⚠️ 年龄分布分析失败: {e}")
            return {}

    def _analyze_app_age_correlation(self, df: pd.DataFrame):
        """分析APP与受害人年龄的关联"""
        if 'final_app_name' not in df.columns or 'victim_age' not in df.columns:
            return

        try:
            app_age_analysis = df.dropna(subset=['final_app_name', 'victim_age']).groupby('final_app_name')[
                'victim_age'].agg(['mean', 'count', 'std']).round(1)

            # 只显示案件数>=5的APP
            app_age_analysis = app_age_analysis[app_age_analysis['count'] >= 5]
            app_age_analysis = app_age_analysis.sort_values('mean')

            if not app_age_analysis.empty:
                print(f"\n📱 不同APP受害人年龄特征 (案件数≥5):")
                print(f"{'APP名称':<20} {'平均年龄':<8} {'案件数':<6} {'年龄标准差':<8}")
                print("-" * 50)

                for app, row in app_age_analysis.head(10).iterrows():
                    std_desc = "较集中" if row['std'] < 10 else "分散" if row['std'] > 15 else "一般"
                    print(f"{app:<20} {row['mean']:.1f}岁    {int(row['count']):<6} {row['std']:.1f}({std_desc})")

        except Exception as e:
            print(f"⚠️ APP年龄关联分析失败: {e}")

    def _analyze_area_age_correlation(self, df: pd.DataFrame):
        """分析地域与受害人年龄的关联"""
        if 'occurrence_area' not in df.columns or 'victim_age' not in df.columns:
            return

        try:
            area_age_analysis = df.dropna(subset=['occurrence_area', 'victim_age']).groupby('occurrence_area')[
                'victim_age'].agg(['mean', 'count']).round(1)

            # 只显示案件数>=10的地区
            area_age_analysis = area_age_analysis[area_age_analysis['count'] >= 10]
            area_age_analysis = area_age_analysis.sort_values('mean')

            if not area_age_analysis.empty:
                print(f"\n🗺️ 不同地区受害人平均年龄 (案件数≥10):")
                for area, row in area_age_analysis.head(8).iterrows():
                    print(f"  {area}: {row['mean']:.1f}岁 ({int(row['count'])}起案件)")

        except Exception as e:
            print(f"⚠️ 地域年龄关联分析失败: {e}")

    def analyze_app_risk_ranking(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """分析APP风险排名 - 优化版本"""
        if not self.validate_data(df, ['final_app_name']):
            return []

        print("\n🏆 APP风险排名分析")
        print("-" * 40)

        app_risks = []

        # 按APP统计风险指标
        app_groups = df.groupby('final_app_name')

        print("📊 正在分析APP风险指标...")

        for app_name, app_data in app_groups:
            if len(app_data) < 3:  # 案件数太少，跳过
                continue

            # 收集APP指标
            case_count = len(app_data)

            # 金额指标
            if 'involved_amount' in app_data.columns:
                amounts = app_data['involved_amount'].dropna()
                amounts = amounts[amounts > 0]
                total_amount = amounts.sum() if not amounts.empty else 0
                avg_amount = amounts.mean() if not amounts.empty else 0
                max_amount = amounts.max() if not amounts.empty else 0
            else:
                total_amount = avg_amount = max_amount = 0

            # 时间指标
            if 'insert_day' in app_data.columns:
                date_range = (app_data['insert_day'].max() - app_data['insert_day'].min()).days + 1
                daily_avg = case_count / max(date_range, 1)
            else:
                daily_avg = 0

            # 地域扩散指标
            if 'occurrence_area' in app_data.columns:
                area_count = app_data['occurrence_area'].nunique()
                area_spread_ratio = area_count / case_count if case_count > 0 else 0
            else:
                area_count = 0
                area_spread_ratio = 0

            # 受害人年龄多样性
            if 'victim_age' in app_data.columns:
                age_data = app_data['victim_age'].dropna()
                age_diversity = age_data.std() if len(age_data) > 1 else 0
            else:
                age_diversity = 0

            # 计算综合风险评分
            risk_score = self._calculate_comprehensive_app_risk_score(
                case_count, total_amount, avg_amount, daily_avg,
                area_count, area_spread_ratio, age_diversity
            )
            risk_level = self._get_app_risk_level(risk_score)

            app_risks.append({
                'app_name': app_name,
                'case_count': case_count,
                'total_amount': total_amount,
                'avg_amount': avg_amount,
                'max_amount': max_amount,
                'daily_avg': daily_avg,
                'area_count': area_count,
                'area_spread_ratio': area_spread_ratio,
                'age_diversity': age_diversity,
                'risk_score': risk_score,
                'risk_level': risk_level
            })

        # 按风险评分排序
        app_risks.sort(key=lambda x: x['risk_score'], reverse=True)

        # 输出TOP15风险APP
        print("TOP15 高风险APP:")
        print(f"{'排名':<4} {'APP名称':<20} {'风险':<4} {'评分':<5} {'案件数':<6} {'日均':<6} {'地区':<4}")
        print("-" * 65)

        for i, app_risk in enumerate(app_risks[:15], 1):
            risk_emoji = {'高': '🔴', '中': '🟡', '低': '🟢'}.get(app_risk['risk_level'], '⚪')
            print(f"{i:<4} {app_risk['app_name']:<20} {risk_emoji} {app_risk['risk_score']:<5.1f} "
                  f"{app_risk['case_count']:<6} {app_risk['daily_avg']:<6.1f} {app_risk['area_count']:<4}")

        return app_risks

    def _calculate_comprehensive_app_risk_score(self, case_count: int, total_amount: float,
                                                avg_amount: float, daily_avg: float,
                                                area_count: int, area_spread_ratio: float,
                                                age_diversity: float) -> float:
        """计算综合APP风险评分 - 增强版"""
        score = 0

        # 案件数风险 (0-30分)
        if case_count >= 100:
            score += 30
        elif case_count >= 50:
            score += 25
        elif case_count >= 20:
            score += 18
        elif case_count >= 10:
            score += 12
        elif case_count >= 5:
            score += 8

        # 金额风险 (0-25分)
        if avg_amount >= 100000:
            score += 25
        elif avg_amount >= 50000:
            score += 18
        elif avg_amount >= 20000:
            score += 12
        elif avg_amount >= 10000:
            score += 8

        # 活跃度风险 (0-20分)
        if daily_avg >= 10:
            score += 20
        elif daily_avg >= 5:
            score += 15
        elif daily_avg >= 2:
            score += 10
        elif daily_avg >= 1:
            score += 6

        # 地域扩散风险 (0-15分)
        if area_count >= 20:
            score += 15
        elif area_count >= 10:
            score += 10
        elif area_count >= 5:
            score += 6

        # 受害人多样性风险 (0-10分) - 年龄跨度大说明影响面广
        if age_diversity >= 20:
            score += 10
        elif age_diversity >= 15:
            score += 7
        elif age_diversity >= 10:
            score += 4

        return min(score, 100)

    def _get_app_risk_level(self, score: float) -> str:
        """根据评分获取APP风险等级"""
        if score >= 70:
            return '高'
        elif score >= 40:
            return '中'
        else:
            return '低'

    def analyze_victim_demographics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析受害人人口统计学特征"""
        if not self.validate_data(df, ['victim_age']):
            return {}

        demographics = {}

        try:
            age_data = df['victim_age'].dropna()
            if not age_data.empty:
                demographics['age_statistics'] = {
                    'count': len(age_data),
                    'mean': float(age_data.mean()),
                    'median': float(age_data.median()),
                    'std': float(age_data.std()),
                    'min': float(age_data.min()),
                    'max': float(age_data.max()),
                    'q25': float(age_data.quantile(0.25)),
                    'q75': float(age_data.quantile(0.75))
                }

                # 年龄段风险分析
                age_risk_analysis = self._analyze_age_risk_levels(age_data)
                demographics['age_risk_analysis'] = age_risk_analysis

            # 地域人口特征分析
            if 'occurrence_area' in df.columns:
                area_demographics = self._analyze_area_demographics(df)
                demographics['area_demographics'] = area_demographics

            return demographics

        except Exception as e:
            print(f"⚠️ 人口统计分析失败: {e}")
            return {}

    def _analyze_age_risk_levels(self, age_data: pd.Series) -> Dict[str, Any]:
        """分析年龄段风险等级"""
        try:
            age_ranges = {
                '高风险(老年)': (60, 100),
                '中高风险(中老年)': (50, 60),
                '中等风险(中年)': (30, 50),
                '低中风险(青年)': (18, 30),
                '低风险(未成年)': (0, 18)
            }

            risk_distribution = {}
            total_count = len(age_data)

            for risk_level, (min_age, max_age) in age_ranges.items():
                count = len(age_data[(age_data >= min_age) & (age_data < max_age)])
                percentage = (count / total_count) * 100 if total_count > 0 else 0
                risk_distribution[risk_level] = {
                    'count': count,
                    'percentage': percentage
                }

            return risk_distribution

        except Exception as e:
            print(f"⚠️ 年龄风险分析失败: {e}")
            return {}

    def _analyze_area_demographics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析地域人口特征"""
        try:
            if 'occurrence_area' not in df.columns or 'victim_age' not in df.columns:
                return {}

            area_demo = df.dropna(subset=['occurrence_area', 'victim_age']).groupby('occurrence_area')[
                'victim_age'].agg(['count', 'mean', 'std']).round(2)

            # 只保留案件数>=5的地区
            area_demo = area_demo[area_demo['count'] >= 5]

            # 转换为字典格式
            result = {}
            for area, stats in area_demo.iterrows():
                result[area] = {
                    'case_count': int(stats['count']),
                    'avg_age': float(stats['mean']),
                    'age_std': float(stats['std']) if not pd.isna(stats['std']) else 0.0
                }

            return result

        except Exception as e:
            print(f"⚠️ 地域人口分析失败: {e}")
            return {}

    def get_risk_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """获取风险摘要 - 简化版"""
        if not self.validate_data(df, ['final_app_name']):
            return {}

        try:
            summary = {
                'total_cases': len(df),
                'unique_apps': df['final_app_name'].nunique()
            }

            # 受害人摘要
            if 'victim_age' in df.columns:
                age_data = df['victim_age'].dropna()
                if not age_data.empty:
                    summary['victim_summary'] = {
                        'total_victims': len(age_data),
                        'avg_age': float(age_data.mean()),
                        'age_range': f"{age_data.min():.0f}-{age_data.max():.0f}岁"
                    }

            # APP风险摘要
            app_risks = self.analyze_app_risk_ranking(df)
            if app_risks:
                high_risk_apps = [app for app in app_risks if app['risk_level'] == '高']
                summary['app_risk_summary'] = {
                    'total_analyzed_apps': len(app_risks),
                    'high_risk_apps': len(high_risk_apps),
                    'top_risk_app': app_risks[0]['app_name'] if app_risks else None
                }

            return summary

        except Exception as e:
            print(f"⚠️ 风险摘要生成失败: {e}")
            return {}


# 为兼容性保持原类名
class RiskAssessmentAnalyzer(SimplifiedRiskAssessmentAnalyzer):
    """风险评估分析器 - 兼容性包装器"""
    pass