"""
APP涉案监控系统 - 基础分析器
定义所有分析器的基础接口和通用方法
"""
from abc import ABC, abstractmethod
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from core.models import AnalysisResults, DataModelFactory
from config import ALERT_THRESHOLDS, RISK_ASSESSMENT


class BaseAnalyzer(ABC):
    """分析器基类 - 定义统一接口"""

    def __init__(self, name: str = None):
        self.name = name or self.__class__.__name__
        self.results = {}
        self.data = None

    @abstractmethod
    def analyze(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        执行分析 - 抽象方法，子类必须实现

        Args:
            df: 案件数据DataFrame

        Returns:
            Dict: 分析结果
        """
        pass

    def set_data(self, df: pd.DataFrame):
        """设置分析数据"""
        self.data = df
        return self

    def get_results(self) -> Dict[str, Any]:
        """获取分析结果"""
        return self.results

    def validate_data(self, df: pd.DataFrame, required_columns: List[str]) -> bool:
        """
        验证数据完整性

        Args:
            df: 数据DataFrame
            required_columns: 必需的字段列表

        Returns:
            bool: 验证是否通过
        """
        if df is None or df.empty:
            print(f"❌ {self.name}: 数据为空")
            return False

        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"❌ {self.name}: 缺少必要字段 {missing_columns}")
            return False

        return True

    def print_section_header(self, title: str):
        """打印分析章节标题"""
        print(f"\n{title}")
        print("=" * len(title.encode('utf-8')))

    def print_subsection_header(self, title: str):
        """打印分析小节标题"""
        print(f"\n{title}")
        print("-" * len(title.encode('utf-8')))


class StatisticalAnalyzer(BaseAnalyzer):
    """统计分析器基类 - 提供通用统计方法"""

    def get_basic_stats(self, df: pd.DataFrame, column: str) -> Dict[str, Any]:
        """获取基础统计信息"""
        if column not in df.columns:
            return {}

        data = df[column].dropna()
        if data.empty:
            return {}

        return {
            'count': len(data),
            'mean': float(data.mean()) if data.dtype in ['int64', 'float64'] else None,
            'median': float(data.median()) if data.dtype in ['int64', 'float64'] else None,
            'std': float(data.std()) if data.dtype in ['int64', 'float64'] else None,
            'min': float(data.min()) if data.dtype in ['int64', 'float64'] else None,
            'max': float(data.max()) if data.dtype in ['int64', 'float64'] else None,
            'unique_count': data.nunique()
        }

    def get_distribution(self, df: pd.DataFrame, column: str, top_n: int = 10) -> Dict[str, int]:
        """获取分布统计"""
        if column not in df.columns:
            return {}

        return df[column].value_counts().head(top_n).to_dict()

    def calculate_growth_rate(self, df: pd.DataFrame, date_column: str,
                              target_column: str, days: int = 7) -> float:
        """计算增长率"""
        if not all(col in df.columns for col in [date_column, target_column]):
            return 0.0

        current_time = datetime.now()
        recent_date = current_time - timedelta(days=days)
        previous_date = current_time - timedelta(days=days * 2)

        recent_data = df[df[date_column] >= recent_date]
        previous_data = df[(df[date_column] >= previous_date) &
                           (df[date_column] < recent_date)]

        recent_count = len(recent_data)
        previous_count = len(previous_data)

        if previous_count == 0:
            return float('inf') if recent_count > 0 else 0.0

        return (recent_count - previous_count) / previous_count


class TrendAnalyzer(BaseAnalyzer):
    """趋势分析器基类 - 提供时间序列分析方法"""

    def analyze_time_series(self, df: pd.DataFrame, date_column: str,
                            value_column: str = None) -> Dict[str, Any]:
        """分析时间序列趋势"""
        if not self.validate_data(df, [date_column]):
            return {}

        # 按日期分组统计
        if value_column and value_column in df.columns:
            daily_stats = df.groupby(df[date_column].dt.date).agg({
                value_column: ['count', 'sum', 'mean']
            }).round(2)
        else:
            daily_stats = df.groupby(df[date_column].dt.date).size()

        return {
            'daily_stats': daily_stats,
            'total_days': len(daily_stats),
            'avg_per_day': daily_stats.mean() if hasattr(daily_stats, 'mean') else 0
        }

    def detect_spikes(self, df: pd.DataFrame, date_column: str,
                      threshold_multiplier: float = 2.0) -> List[Dict[str, Any]]:
        """检测数据激增"""
        if not self.validate_data(df, [date_column]):
            return []

        daily_counts = df.groupby(df[date_column].dt.date).size()
        mean_count = daily_counts.mean()
        std_count = daily_counts.std()

        threshold = mean_count + threshold_multiplier * std_count

        spikes = []
        for date, count in daily_counts.items():
            if count > threshold:
                spikes.append({
                    'date': date.strftime('%Y-%m-%d'),
                    'count': count,
                    'threshold': threshold,
                    'spike_ratio': count / mean_count
                })

        return spikes


class RiskAnalyzer(BaseAnalyzer):
    """风险分析器基类 - 提供风险评估方法"""

    def calculate_risk_score(self, metrics: Dict[str, float]) -> float:
        """计算风险评分"""
        score = 0
        weights = RISK_ASSESSMENT['score_weights']

        # 新APP风险
        if 'new_apps_high_risk' in metrics:
            score += min(metrics['new_apps_high_risk'] * 10, weights['new_apps'])

        # 预警风险
        if 'high_severity_alerts' in metrics:
            score += min(metrics['high_severity_alerts'] * 15, weights['alerts'])

        # 资金风险
        if 'avg_amount' in metrics and metrics['avg_amount'] > 50000:
            score += weights['financial'] / 2
        if 'high_risk_cases' in metrics and metrics['high_risk_cases'] > 5:
            score += weights['financial'] / 2

        # 案件量风险
        if 'total_cases' in metrics:
            if metrics['total_cases'] > 100:
                score += weights['case_volume'] / 2
            if metrics['total_cases'] > 500:
                score += weights['case_volume'] / 2

        return min(score, 100)

    def get_risk_level(self, score: float) -> str:
        """根据评分获取风险等级"""
        thresholds = RISK_ASSESSMENT['risk_levels']

        if score >= thresholds['high']:
            return '高风险'
        elif score >= thresholds['medium']:
            return '中风险'
        elif score >= thresholds['low']:
            return '低风险'
        else:
            return '正常'

    def assess_app_risk(self, case_count: int, total_amount: float, days: int) -> str:
        """评估单个APP风险等级"""
        daily_cases = case_count / max(days, 1)
        daily_amount = total_amount / max(days, 1)

        thresholds = RISK_ASSESSMENT['app_risk_thresholds']

        if (daily_cases >= thresholds['daily_cases_high'] or
                daily_amount >= thresholds['daily_amount_high']):
            return '高'
        elif (daily_cases >= thresholds['daily_cases_medium'] or
              daily_amount >= thresholds['daily_amount_medium']):
            return '中'
        else:
            return '低'


class PatternAnalyzer(BaseAnalyzer):
    """模式分析器基类 - 提供模式识别方法"""

    def find_similar_items(self, items: List[str], threshold: float = 0.8) -> List[List[str]]:
        """查找相似项目"""
        from difflib import SequenceMatcher

        if len(items) == 0:
            return []

        similar_groups = []
        processed = set()

        for i, item1 in enumerate(items):
            if item1 in processed:
                continue

            similar_group = [item1]
            processed.add(item1)

            for j, item2 in enumerate(items[i + 1:], i + 1):
                if item2 in processed:
                    continue

                similarity = SequenceMatcher(None, item1, item2).ratio()
                if similarity >= threshold:
                    similar_group.append(item2)
                    processed.add(item2)

            if len(similar_group) >= 2:
                similar_groups.append(similar_group)

        return similar_groups

    def detect_anomalies(self, df: pd.DataFrame, column: str,
                         method: str = 'iqr') -> pd.Series:
        """检测异常值"""
        if column not in df.columns:
            return pd.Series([], dtype=bool)

        data = df[column].dropna()
        if data.empty:
            return pd.Series([False] * len(df), index=df.index)

        if method == 'iqr':
            Q1 = data.quantile(0.25)
            Q3 = data.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR

            return (df[column] < lower_bound) | (df[column] > upper_bound)

        elif method == 'zscore':
            mean = data.mean()
            std = data.std()
            z_scores = abs((df[column] - mean) / std)
            return z_scores > 3

        return pd.Series([False] * len(df), index=df.index)


class NetworkAnalyzer(BaseAnalyzer):
    """网络分析器基类 - 提供关系分析方法"""

    def calculate_similarity_matrix(self, df: pd.DataFrame, groupby_col: str,
                                    value_cols: List[str]) -> pd.DataFrame:
        """计算相似度矩阵"""
        if not all(col in df.columns for col in [groupby_col] + value_cols):
            return pd.DataFrame()

        # 创建透视表
        pivot_data = df.groupby([groupby_col] + value_cols).size().unstack(fill_value=0)

        if pivot_data.empty:
            return pd.DataFrame()

        # 计算余弦相似度
        from sklearn.metrics.pairwise import cosine_similarity
        similarity_matrix = cosine_similarity(pivot_data.values)

        return pd.DataFrame(
            similarity_matrix,
            index=pivot_data.index,
            columns=pivot_data.index
        )

    def find_clusters(self, similarity_matrix: pd.DataFrame,
                      threshold: float = 0.8) -> List[List[str]]:
        """基于相似度矩阵查找集群"""
        clusters = []
        processed = set()

        for i, item1 in enumerate(similarity_matrix.index):
            if item1 in processed:
                continue

            cluster = [item1]
            processed.add(item1)

            for j, item2 in enumerate(similarity_matrix.index):
                if i != j and item2 not in processed:
                    if similarity_matrix.iloc[i, j] > threshold:
                        cluster.append(item2)
                        processed.add(item2)

            if len(cluster) >= 2:
                clusters.append(cluster)

        return clusters


class AnalyzerPipeline:
    """分析器管道 - 协调多个分析器执行"""

    def __init__(self):
        self.analyzers = []
        self.results = AnalysisResults()

    def add_analyzer(self, analyzer: BaseAnalyzer):
        """添加分析器"""
        self.analyzers.append(analyzer)
        return self

    def run_all(self, df: pd.DataFrame) -> AnalysisResults:
        """运行所有分析器"""
        print("🚀 开始执行分析管道...")

        for analyzer in self.analyzers:
            try:
                print(f"📊 执行 {analyzer.name}...")
                result = analyzer.analyze(df)

                # 根据分析器类型存储结果
                if hasattr(self.results, analyzer.name.lower()):
                    setattr(self.results, analyzer.name.lower(), result)

            except Exception as e:
                print(f"❌ {analyzer.name} 执行失败: {e}")
                continue

        print("✅ 分析管道执行完成")
        return self.results

    def get_summary(self) -> Dict[str, Any]:
        """获取分析摘要"""
        return self.results.to_dict()