"""
APP涉案监控系统 - 更新版模式检测分析器
修复案件数激增统计维度：最近3天对比近7天历史平均
"""
import pandas as pd
import numpy as np
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import defaultdict
from .base import PatternAnalyzer, TrendAnalyzer, RiskAnalyzer
from core.models import NewAppAlert, SuspiciousPattern
from config import ALERT_THRESHOLDS, QUERY_CONFIG, INVALID_APP_PATTERNS, TIME_QUERY_CONFIG


class PatternDetector(PatternAnalyzer, TrendAnalyzer, RiskAnalyzer):
    """模式检测器 - 修复案件数激增统计维度"""

    def __init__(self):
        super().__init__("PatternDetector")

    def analyze(self, df: pd.DataFrame) -> Dict[str, Any]:
        """执行模式检测分析 - 主要入口"""
        if not self.validate_data(df, ['final_app_name', 'insert_day']):
            return {}

        results = {
            'suspicious_patterns': self.detect_suspicious_patterns(df)
        }

        return results

    def detect_new_apps(self, df: pd.DataFrame, days_threshold: int = TIME_QUERY_CONFIG['default_days'],
                        display_limit: int = 50) -> List[NewAppAlert]:
        """检测新出现的APP - 统一使用配置默认天数"""
        if not self.validate_data(df, ['final_app_name', 'insert_day']):
            return []

        self.print_section_header(f"🆕 新APP检测 (最近{days_threshold}天)")

        # 计算每个APP首次出现的时间
        app_first_seen = df.dropna(subset=['final_app_name']).groupby('final_app_name')['insert_day'].min()

        # 检测最近出现的APP
        threshold_date = datetime.now() - timedelta(days=days_threshold)
        new_apps = app_first_seen[app_first_seen >= threshold_date]

        if new_apps.empty:
            print(f"✅ 最近{days_threshold}天未发现新APP")
            return []

        total_new_apps = len(new_apps)
        print(f"🔍 发现 {total_new_apps} 个新APP")

        # 分析新APP的案件情况
        new_app_alerts = []
        for app_name, first_seen in new_apps.items():
            app_cases = df[df['final_app_name'] == app_name]
            case_count = len(app_cases)

            # 金额统计
            if 'involved_amount' in app_cases.columns:
                amounts = app_cases['involved_amount'].dropna()
                amounts = amounts[amounts > 0]
                total_amount = amounts.sum() if not amounts.empty else 0
                avg_amount = amounts.mean() if not amounts.empty else 0
            else:
                total_amount = avg_amount = 0

            # 地域扩散统计
            if 'occurrence_area' in app_cases.columns:
                area_count = app_cases['occurrence_area'].nunique()
            else:
                area_count = 0

            # 增长速度统计
            days_active = (datetime.now() - first_seen).days + 1
            daily_growth = case_count / max(days_active, 1)

            # 评估风险等级
            risk_level = self._assess_new_app_risk(case_count, total_amount, area_count, daily_growth)

            new_app_alerts.append(NewAppAlert(
                app_name=app_name,
                first_seen=first_seen,
                case_count=case_count,
                total_amount=total_amount,
                avg_amount=avg_amount if not pd.isna(avg_amount) else 0,
                risk_level=risk_level
            ))

        # 按风险等级和案件数排序
        new_app_alerts.sort(key=lambda x: (
            {'高': 3, '中': 2, '低': 1}.get(x.risk_level, 0),
            x.case_count
        ), reverse=True)

        # 限制显示数量
        display_apps = new_app_alerts[:display_limit]

        # 使用统一对齐显示
        try:
            from utils.alignment_display import print_aligned_new_apps

            # 转换为字典格式
            display_data = []
            for app_alert in display_apps:
                display_data.append({
                    'app_name': app_alert.app_name,
                    'risk_level': app_alert.risk_level,
                    'case_count': app_alert.case_count,
                    'total_amount': app_alert.total_amount,
                    'avg_amount': app_alert.avg_amount,
                    'first_seen': app_alert.first_seen,
                    'days_active': (datetime.now() - app_alert.first_seen).days + 1,
                    'area_count': 0  # 默认值，实际会在网络分析器中计算
                })

            print_aligned_new_apps(display_data, max_display=display_limit)
        except ImportError:
            # 回退到原始显示方式
            self._print_new_apps_fallback(display_apps, total_new_apps, display_limit)

        return new_app_alerts

    def detect_suspicious_patterns(self, df: pd.DataFrame,
                                   display_limit: int = 50) -> List[SuspiciousPattern]:
        """检测可疑模式和异常 - 修复案件数激增统计维度"""
        if not self.validate_data(df, ['final_app_name', 'insert_day']):
            return []

        self.print_section_header("🚨 可疑模式检测")

        alerts = []
        current_time = datetime.now()

        # 预先计算常用数据
        print("📊 预处理数据...")
        valid_df = df.dropna(subset=['insert_day']).copy()
        if 'final_app_name' in valid_df.columns:
            valid_df = valid_df.dropna(subset=['final_app_name'])

        print(f"   有效数据: {len(valid_df)} 条")

        # 1. 检测案件数异常增长的APP - 修复统计维度
        print("🔍 检测案件数异常增长...")
        case_spike_alerts = self._detect_case_spikes_fixed(valid_df, current_time)
        alerts.extend(case_spike_alerts)

        # 2. 检测金额异常的案件
        print("🔍 检测金额异常...")
        amount_anomaly_alerts = self._detect_amount_anomalies_optimized(valid_df)
        alerts.extend(amount_anomaly_alerts)

        # 3. 检测时间集中爆发模式
        print("🔍 检测时间集中爆发...")
        time_burst_alerts = self._detect_time_burst_patterns(valid_df, current_time)
        alerts.extend(time_burst_alerts)

        # 4. 检测异常活跃APP
        print("🔍 检测异常活跃APP...")
        hyperactive_alerts = self._detect_hyperactive_apps(valid_df, current_time)
        alerts.extend(hyperactive_alerts)

        # 输出结果
        self._print_suspicious_patterns_results(alerts, display_limit)

        return alerts

    def _detect_case_spikes_fixed(self, df: pd.DataFrame, current_time: datetime) -> List[SuspiciousPattern]:
        """修复的案件数激增检测 - 统一描述口径"""
        alerts = []

        try:
            # 使用固定的3天和7天计算逻辑
            recent_days = 3
            history_days = 7

            # 最近3天数据
            recent_start = current_time - timedelta(days=recent_days)
            recent_cases = df[df['insert_day'] >= recent_start]

            # 历史7天数据（不包括最近3天）
            history_start = current_time - timedelta(days=history_days + recent_days)
            history_end = current_time - timedelta(days=recent_days)
            history_cases = df[(df['insert_day'] >= history_start) & (df['insert_day'] < history_end)]

            if len(recent_cases) > 0 and len(history_cases) > 0:
                recent_app_counts = recent_cases['final_app_name'].value_counts()
                history_app_counts = history_cases['final_app_name'].value_counts()

                # 计算历史7天日均
                history_daily_avg = history_app_counts / history_days

                from config import ALERT_THRESHOLDS
                significant_apps = recent_app_counts[recent_app_counts >= ALERT_THRESHOLDS['new_app_cases']].index

                for app in significant_apps:
                    recent_count = recent_app_counts[app]
                    historical_daily_avg = history_daily_avg.get(app, 0)

                    if historical_daily_avg > 0:
                        # 计算最近3天相对于历史日均的增长倍数
                        expected_3days = historical_daily_avg * recent_days
                        spike_ratio = recent_count / expected_3days

                        if spike_ratio > ALERT_THRESHOLDS['case_spike']:
                            severity = '高' if spike_ratio > 5 else '中'
                            alerts.append(SuspiciousPattern(
                                type='案件数激增',
                                app_name=app,
                                severity=severity,
                                description=f'最近3天案件数({recent_count})起较历史(近7天)日均({historical_daily_avg:.1f})增长{spike_ratio:.1f}倍'
                            ))
                    elif recent_count >= 10:  # 新出现的高频APP
                        alerts.append(SuspiciousPattern(
                            type='新高频APP',
                            app_name=app,
                            severity='高',
                            description=f'最近3天案件数({recent_count})起超出风险阈值'
                        ))

        except Exception as e:
            print(f"⚠️ 案件激增检测失败: {e}")

        return alerts

    def _assess_new_app_risk(self, case_count: int, total_amount: float,
                             area_count: int, daily_growth: float) -> str:
        """评估新APP风险等级"""
        risk_score = 0

        # 案件数风险
        if case_count >= 20:
            risk_score += 40
        elif case_count >= 10:
            risk_score += 25
        elif case_count >= 5:
            risk_score += 15

        # 金额风险
        if total_amount >= 500000:  # 50万+
            risk_score += 30
        elif total_amount >= 100000:  # 10万+
            risk_score += 20
        elif total_amount >= 50000:  # 5万+
            risk_score += 10

        # 增长速度风险
        if daily_growth >= 5:
            risk_score += 20
        elif daily_growth >= 2:
            risk_score += 10
        elif daily_growth >= 1:
            risk_score += 5

        # 扩散范围风险
        if area_count >= 10:
            risk_score += 10
        elif area_count >= 5:
            risk_score += 5

        # 风险等级判定
        if risk_score >= 70:
            return '高'
        elif risk_score >= 35:
            return '中'
        else:
            return '低'

    def _detect_amount_anomalies_optimized(self, df: pd.DataFrame) -> List[SuspiciousPattern]:
        """优化的金额异常检测"""
        alerts = []

        if 'involved_amount' not in df.columns:
            return alerts

        try:
            amount_df = df.dropna(subset=['involved_amount'])
            amount_df = amount_df[amount_df['involved_amount'] > 0]

            if len(amount_df) == 0:
                return alerts

            # 获取案件数足够的APP进行分析
            app_case_counts = amount_df['final_app_name'].value_counts()
            significant_apps = app_case_counts[app_case_counts >= 8].index[:30]

            for app in significant_apps:
                app_amounts = amount_df[amount_df['final_app_name'] == app]['involved_amount']

                if len(app_amounts) >= 8:
                    # 使用更稳健的异常值检测方法
                    Q1 = app_amounts.quantile(0.25)
                    Q3 = app_amounts.quantile(0.75)
                    IQR = Q3 - Q1

                    if IQR > 0:
                        # 使用IQR方法检测异常值
                        upper_bound = Q3 + 2.0 * IQR
                        high_amount_cases = app_amounts[app_amounts > upper_bound]

                        if len(high_amount_cases) >= 2:
                            max_amount = high_amount_cases.max()
                            median_amount = app_amounts.median()
                            anomaly_ratio = max_amount / median_amount if median_amount > 0 else 0

                            severity = '高' if max_amount > ALERT_THRESHOLDS['high_risk_amount'] * 2 else '中'
                            alerts.append(SuspiciousPattern(
                                type='异常高额',
                                app_name=app,
                                severity=severity,
                                description=f'{app} 出现{len(high_amount_cases)}起异常高额案件，最高{max_amount:,.0f}元(中位数{median_amount:,.0f}元的{anomaly_ratio:.1f}倍)'
                            ))

        except Exception as e:
            print(f"⚠️ 金额异常检测失败: {e}")

        return alerts

    def _detect_time_burst_patterns(self, df: pd.DataFrame, current_time: datetime) -> List[SuspiciousPattern]:
        """检测时间集中爆发模式"""
        alerts = []

        try:
            # 检测单日爆发
            daily_counts = df.groupby([df['insert_day'].dt.date, 'final_app_name']).size().reset_index()
            daily_counts.columns = ['date', 'app_name', 'count']

            # 寻找单日案件数异常高的APP
            high_daily_counts = daily_counts[daily_counts['count'] >= 15]

            for _, row in high_daily_counts.iterrows():
                app_name = row['app_name']
                date = row['date']
                count = row['count']

                # 检查这个APP的历史单日最高记录
                app_daily_history = daily_counts[daily_counts['app_name'] == app_name]['count']
                if len(app_daily_history) > 1:
                    avg_daily = app_daily_history.mean()
                    if count > avg_daily * 3:
                        alerts.append(SuspiciousPattern(
                            type='单日爆发',
                            app_name=app_name,
                            severity='高' if count >= 30 else '中',
                            description=f'{app_name} 在{date}单日出现{count}起案件，超过日均({avg_daily:.1f})的{count / avg_daily:.1f}倍'
                        ))

        except Exception as e:
            print(f"⚠️ 时间爆发检测失败: {e}")

        return alerts

    def _detect_hyperactive_apps(self, df: pd.DataFrame, current_time: datetime) -> List[SuspiciousPattern]:
        """检测异常活跃APP - 修改为3天统计"""
        alerts = []

        try:
            # 分析最近3天的APP活跃度
            recent_3days = current_time - timedelta(days=3)
            recent_data = df[df['insert_day'] >= recent_3days]

            if len(recent_data) == 0:
                return alerts

            app_activity = recent_data.groupby('final_app_name').size()

            # 识别异常活跃的APP（3天内超过30起）
            hyperactive_threshold = 30
            hyperactive_apps = app_activity[app_activity >= hyperactive_threshold]

            for app_name, total_cases in hyperactive_apps.items():
                daily_avg = total_cases / 3
                severity = '高' if daily_avg >= 20 else '中'
                alerts.append(SuspiciousPattern(
                    type='异常活跃',
                    app_name=app_name,
                    severity=severity,
                    description=f'最近3天异常活跃，日均{daily_avg:.1f}起案件，共{total_cases}起'
                ))

        except Exception as e:
            print(f"⚠️ 异常活跃检测失败: {e}")

        return alerts

    def _print_suspicious_patterns_results(self, alerts: List[SuspiciousPattern],
                                           display_limit: int):
        """打印可疑模式检测结果 - 使用统一对齐显示"""
        if alerts:
            # 按严重程度和类型排序
            severity_order = {'高': 3, '中': 2, '低': 1}
            alerts.sort(key=lambda x: (
                severity_order.get(x.severity, 0),
                x.type,
                x.app_name
            ), reverse=True)

            # 使用统一对齐显示
            try:
                from utils.alignment_display import print_aligned_suspicious_patterns

                # 转换为字典格式
                alert_data = []
                for alert in alerts:
                    alert_data.append({
                        'type': alert.type,
                        'app_name': alert.app_name,
                        'severity': alert.severity,
                        'description': alert.description
                    })

                print_aligned_suspicious_patterns(alert_data, max_display=display_limit)
            except ImportError:
                # 回退到原始显示方式
                self._print_suspicious_patterns_fallback(alerts, display_limit)
        else:
            print("✅ 未发现明显的可疑模式")

    def _print_new_apps_fallback(self, new_app_alerts: List[NewAppAlert], total_count: int, display_limit: int):
        """新APP显示回退方法"""
        if total_count > display_limit:
            print(f"📋 显示TOP{display_limit}个新APP (按风险等级和案件数排序，共{total_count}个):")
        else:
            print(f"📋 显示全部{total_count}个新APP (按风险等级排序):")

        print("=" * 100)

        # 表头 - 确保左对齐
        header = f"{'序号':<4} {'APP名称':<25} {'风险等级':<8} {'案件数':<8} {'日均案件':<10} {'涉案金额(万)':<12}"
        print(header)
        print("-" * 100)

        for i, app_alert in enumerate(new_app_alerts, 1):
            risk_emoji = {'高': '🔴', '中': '🟡', '低': '🟢'}.get(app_alert.risk_level, '⚪')
            daily_avg = app_alert.case_count / max((datetime.now() - app_alert.first_seen).days + 1, 1)
            total_amount_wan = app_alert.total_amount / 10000

            # 数据行 - 确保左对齐
            row = f"{i:<4} {app_alert.app_name:<25} {risk_emoji}{app_alert.risk_level:<7} " \
                  f"{app_alert.case_count:<8} {daily_avg:<10.1f} {total_amount_wan:<12.1f}"
            print(row)

        # 风险统计
        risk_stats = {'高': 0, '中': 0, '低': 0}
        for app in new_app_alerts:
            risk_stats[app.risk_level] = risk_stats.get(app.risk_level, 0) + 1

        print(
            f"\n📊 风险等级分布: 高风险 {risk_stats['高']}个 | 中风险 {risk_stats['中']}个 | 低风险 {risk_stats['低']}个")

    def _print_suspicious_patterns_fallback(self, alerts: List[SuspiciousPattern], display_limit: int):
        """可疑模式显示回退方法"""
        total_alerts = len(alerts)
        display_alerts = alerts[:display_limit]

        if total_alerts > display_limit:
            print(f"🚨 发现 {total_alerts} 个可疑模式，显示TOP{display_limit}个:")
        else:
            print(f"🚨 发现 {total_alerts} 个可疑模式:")

        print("=" * 120)

        # 表头 - 确保左对齐
        header = f"{'序号':<4} {'类型':<12} {'APP名称':<25} {'严重程度':<8} {'描述':<50}"
        print(header)
        print("-" * 120)

        for i, alert in enumerate(display_alerts, 1):
            severity_emoji = {'高': '🔴', '中': '🟡', '低': '🟢'}.get(alert.severity, '⚪')
            description = alert.description[:47] + "..." if len(alert.description) > 50 else alert.description

            # 数据行 - 确保左对齐
            row = f"{i:<4} {alert.type:<12} {alert.app_name:<25} " \
                  f"{severity_emoji}{alert.severity:<7} {description:<50}"
            print(row)

        # 统计信息
        if total_alerts > display_limit:
            remaining_alerts = alerts[display_limit:]
            remaining_high = len([alert for alert in remaining_alerts if alert.severity == '高'])
            remaining_medium = len([alert for alert in remaining_alerts if alert.severity == '中'])
            remaining_low = len([alert for alert in remaining_alerts if alert.severity == '低'])
            print(
                f"\n📊 未显示的{total_alerts - display_limit}个预警: 高 {remaining_high}个 | 中 {remaining_medium}个 | 低 {remaining_low}个")

        # 类型统计
        type_stats = {}
        for alert in alerts:
            type_stats[alert.type] = type_stats.get(alert.type, 0) + 1

        print(f"\n📈 预警类型分布:")
        for alert_type, count in sorted(type_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"   {alert_type}: {count}个")

    # ================================
    # 兼容性方法 - 支持地理集中检测（如果需要）
    # ================================
    def detect_geographic_concentration(self, df: pd.DataFrame) -> Dict[str, Any]:
        """检测地理集中度 - 兼容性方法"""
        if 'occurrence_area' not in df.columns:
            return {}

        try:
            # 地区分布统计
            area_counts = df['occurrence_area'].value_counts()
            total_cases = len(df)

            # 识别高集中度地区
            concentration_results = {}
            for area, count in area_counts.items():
                percentage = (count / total_cases) * 100
                if percentage >= 25:  # 超过25%认为高集中
                    risk_level = '高' if percentage >= 50 else '中'
                    concentration_results[area] = {
                        'case_count': count,
                        'percentage': percentage,
                        'risk_level': risk_level
                    }

            return concentration_results

        except Exception as e:
            print(f"⚠️ 地理集中检测失败: {e}")
            return {}

    # ================================
    # 保留的通用工具方法
    # ================================
    def get_pattern_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """获取模式检测摘要"""
        if not self.validate_data(df, ['final_app_name', 'insert_day']):
            return {}

        try:
            # 执行分析
            results = self.analyze(df)

            new_apps = results.get('new_apps', [])
            suspicious_patterns = results.get('suspicious_patterns', [])

            summary = {
                'total_apps_analyzed': df['final_app_name'].nunique(),
                'new_apps_found': len(new_apps),
                'suspicious_patterns_found': len(suspicious_patterns),
                'analysis_period_days': (df['insert_day'].max() - df['insert_day'].min()).days + 1
            }

            # 新APP风险分布
            if new_apps:
                risk_distribution = {'高': 0, '中': 0, '低': 0}
                for app in new_apps:
                    risk_level = getattr(app, 'risk_level', '低')
                    risk_distribution[risk_level] = risk_distribution.get(risk_level, 0) + 1
                summary['new_apps_risk_distribution'] = risk_distribution

            # 可疑模式类型分布
            if suspicious_patterns:
                pattern_types = {}
                severity_distribution = {'高': 0, '中': 0, '低': 0}
                for pattern in suspicious_patterns:
                    pattern_types[pattern.type] = pattern_types.get(pattern.type, 0) + 1
                    severity_distribution[pattern.severity] = severity_distribution.get(pattern.severity, 0) + 1

                summary['pattern_type_distribution'] = pattern_types
                summary['pattern_severity_distribution'] = severity_distribution

            return summary

        except Exception as e:
            print(f"⚠️ 模式检测摘要生成失败: {e}")
            return {}


# 为兼容性保持原类名
class SimplifiedPatternDetector(PatternDetector):
    """简化版模式检测器 - 兼容性包装器"""
    pass