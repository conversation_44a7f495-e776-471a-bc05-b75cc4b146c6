"""
APP涉案监控系统 - 工具函数
提供通用工具函数、验证函数等
"""
import re
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Union


class ValidationUtils:
    """数据验证工具类"""

    @staticmethod
    def is_valid_phone(phone: str) -> bool:
        """验证手机号格式"""
        if not phone or not isinstance(phone, str):
            return False

        # 中国手机号正则
        pattern = r'^1[3-9]\d{9}$'
        return bool(re.match(pattern, phone.strip()))

    @staticmethod
    def is_valid_amount(amount: Union[str, int, float]) -> bool:
        """验证金额有效性"""
        try:
            amt = float(amount)
            return amt > 0
        except (ValueError, TypeError):
            return False

    @staticmethod
    def is_valid_app_name(app_name: str) -> bool:
        """验证APP名称有效性"""
        if not app_name or not isinstance(app_name, str):
            return False

        app_name = app_name.strip()

        # 基本长度检查
        if len(app_name) < 2 or len(app_name) > 100:
            return False

        # 无效模式检查
        invalid_patterns = [
            r'^[?？]+$',  # 匹配只有问号的字符串
            r'^\*+$',  # 匹配只有星号的字符串
            r'^#+$',  # 匹配只有井号的字符串
            r'^[\.。]+$',  # 匹配只有点号的字符串
            r'^[-—]+$',  # 匹配只有横线的字符串
            r'^[/\\]+$',  # 匹配只有斜杠的字符串
            r'^\d+$'  # 匹配纯数字字符串
        ]

        for pattern in invalid_patterns:
            if re.match(pattern, app_name):
                return False

        return True


class DataUtils:
    """数据处理工具类"""

    @staticmethod
    def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
        """安全除法，避免除零错误"""
        try:
            return numerator / denominator if denominator != 0 else default
        except (TypeError, ValueError):
            return default

    @staticmethod
    def clean_string(text: str) -> str:
        """清理字符串"""
        if not text or not isinstance(text, str):
            return ""

        # 去除首尾空白
        text = text.strip()

        # 替换多个空格为单个空格
        text = re.sub(r'\s+', ' ', text)

        return text

    @staticmethod
    def standardize_area_name(area: str) -> str:
        """标准化地区名称"""
        if not area or not isinstance(area, str):
            return ""

        area = area.strip()

        # 提取省份信息
        if '省' in area:
            province = area.split('省')[0] + '省'
            return province
        elif '市' in area and '省' not in area:
            # 直辖市处理
            if any(city in area for city in ['北京', '上海', '天津', '重庆']):
                return area.split('市')[0] + '市'
            else:
                return area
        elif '自治区' in area:
            return area.split('自治区')[0] + '自治区'
        elif '特别行政区' in area:
            return area.split('特别行政区')[0] + '特别行政区'
        else:
            return area

    @staticmethod
    def parse_amount_string(amount_str: str) -> float:
        """解析金额字符串"""
        if not amount_str:
            return 0.0

        try:
            # 移除常见的非数字字符
            clean_str = re.sub(r'[^\d.]', '', str(amount_str))
            return float(clean_str) if clean_str else 0.0
        except (ValueError, TypeError):
            return 0.0

    @staticmethod
    def calculate_days_between(start_date: datetime, end_date: datetime) -> int:
        """计算两个日期之间的天数"""
        try:
            return (end_date - start_date).days
        except (TypeError, AttributeError):
            return 0


class TimeUtils:
    """时间处理工具类"""

    @staticmethod
    def get_date_range(days: int) -> tuple:
        """获取日期范围"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        return start_date, end_date

    @staticmethod
    def format_datetime(dt: datetime, format_str: str = '%Y-%m-%d %H:%M:%S') -> str:
        """格式化日期时间"""
        try:
            return dt.strftime(format_str)
        except (AttributeError, TypeError):
            return ""

    @staticmethod
    def is_recent(dt: datetime, days: int = 7) -> bool:
        """判断日期是否在最近几天内"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            return dt >= cutoff_date
        except (TypeError, AttributeError):
            return False


class StatUtils:
    """统计工具类"""

    @staticmethod
    def calculate_growth_rate(current: float, previous: float) -> float:
        """计算增长率"""
        if previous == 0:
            return float('inf') if current > 0 else 0.0

        return (current - previous) / previous * 100

    @staticmethod
    def calculate_percentile(data: List[float], percentile: float) -> float:
        """计算百分位数"""
        if not data:
            return 0.0

        try:
            return pd.Series(data).quantile(percentile / 100)
        except Exception:
            return 0.0

    @staticmethod
    def detect_outliers_iqr(data: List[float]) -> List[bool]:
        """使用IQR方法检测异常值"""
        if not data or len(data) < 4:
            return [False] * len(data)

        try:
            series = pd.Series(data)
            Q1 = series.quantile(0.25)
            Q3 = series.quantile(0.75)
            IQR = Q3 - Q1

            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR

            return [(x < lower_bound) or (x > upper_bound) for x in data]
        except Exception:
            return [False] * len(data)


class FormatUtils:
    """格式化工具类"""

    @staticmethod
    def format_number(num: Union[int, float], decimal_places: int = 2) -> str:
        """格式化数字"""
        try:
            if isinstance(num, int) or (isinstance(num, float) and num.is_integer()):
                return f"{int(num):,}"
            else:
                return f"{num:,.{decimal_places}f}"
        except (ValueError, TypeError):
            return "0"

    @staticmethod
    def format_currency(amount: float, currency: str = "元") -> str:
        """格式化货币"""
        try:
            return f"{amount:,.2f} {currency}"
        except (ValueError, TypeError):
            return f"0.00 {currency}"

    @staticmethod
    def format_percentage(value: float, decimal_places: int = 1) -> str:
        """格式化百分比"""
        try:
            return f"{value:.{decimal_places}f}%"
        except (ValueError, TypeError):
            return "0.0%"

    @staticmethod
    def truncate_string(text: str, max_length: int = 50, suffix: str = "...") -> str:
        """截断字符串"""
        if not text or not isinstance(text, str):
            return ""

        if len(text) <= max_length:
            return text

        return text[:max_length - len(suffix)] + suffix


# 常量定义
class Constants:
    """系统常量"""

    # 风险等级
    RISK_HIGH = "高"
    RISK_MEDIUM = "中"
    RISK_LOW = "低"
    RISK_NORMAL = "正常"

    # 预警类型
    ALERT_CASE_SPIKE = "案件数激增"
    ALERT_AMOUNT_ANOMALY = "异常高额"
    ALERT_APP_VARIANT = "APP变种"
    ALERT_GEO_CONCENTRATION = "地区集中"

    # 分析类型
    ANALYSIS_FINANCIAL = "financial"
    ANALYSIS_PATTERN = "pattern"
    ANALYSIS_NETWORK = "network"
    ANALYSIS_RISK = "risk"

    # 文件格式
    FORMAT_HTML = "html"
    FORMAT_JSON = "json"
    FORMAT_CSV = "csv"


# 便捷函数
def get_risk_emoji(risk_level: str) -> str:
    """获取风险等级对应的emoji"""
    emoji_map = {
        Constants.RISK_HIGH: "🔴",
        Constants.RISK_MEDIUM: "🟡",
        Constants.RISK_LOW: "🟢",
        Constants.RISK_NORMAL: "⚪"
    }
    return emoji_map.get(risk_level, "⚪")


def get_severity_emoji(severity: str) -> str:
    """获取严重程度对应的emoji"""
    emoji_map = {
        "高": "🔴",
        "중": "🟡",
        "低": "🟢"
    }
    return emoji_map.get(severity, "⚪")


def safe_get(dictionary: Dict, key: str, default: Any = None) -> Any:
    """安全获取字典值"""
    try:
        return dictionary.get(key, default)
    except (AttributeError, TypeError):
        return default


def ensure_list(value: Any) -> List:
    """确保返回列表类型"""
    if value is None:
        return []
    elif isinstance(value, list):
        return value
    else:
        return [value]


def merge_dicts(*dicts: Dict) -> Dict:
    """合并多个字典"""
    result = {}
    for d in dicts:
        if isinstance(d, dict):
            result.update(d)
    return result