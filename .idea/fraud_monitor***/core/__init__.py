"""
APP涉案监控系统 - Core模块初始化文件
"""

# 导入核心模块
from .database import DatabaseManager
from .data_processor import DataProcessor
from .models import *

# 尝试导入可选模块
try:
    from .time_query_manager import (
        TimeQueryType,
        SimpleTimeQueryManager,
        SimpleTimeQueryInterface,
        get_current_time_config,
        get_current_datetime_range,
        show_time_selection_menu,
        get_time_summary,
        get_days_count,
        get_time_description,
        is_natural_week,
        is_natural_month,
        set_default_time_query,
        reset_to_default,
        get_app_analysis_days,
        get_app_analysis_description
    )
except ImportError:
    # 如果时间管理器不可用，提供默认值
    def get_days_count():
        return 7


    def get_time_description():
        return "默认7天（自然周）"


    def get_app_analysis_days():
        return 7


    def get_app_analysis_description():
        return "默认7天（自然周）"


    def show_time_selection_menu():
        print("⚠️ 时间管理器不可用")
        return False


    def get_time_summary():
        return "默认7天（自然周）"

try:
    from .app_analysis_time_manager import *
except ImportError:
    # 如果APP分析时间管理器不可用，使用通用时间管理器的接口
    pass

__all__ = [
    'DatabaseManager',
    'DataProcessor',
    'get_days_count',
    'get_time_description',
    'get_app_analysis_days',
    'get_app_analysis_description',
    'show_time_selection_menu',
    'get_time_summary'
]