"""
APP涉案监控系统 - 最终修复版时间查询管理器
修复时间范围计算问题，确保开始/结束时间的精确性
"""
from datetime import datetime, timedelta, date
from typing import Dict, Any, Tuple
import calendar
from enum import Enum


class TimeQueryType(Enum):
    """时间查询类型枚举"""
    NATURAL_WEEK = "natural_week"
    LAST_WEEK = "last_week"
    RECENT_DAYS = "recent_days"
    NATURAL_MONTH = "natural_month"
    DATE_RANGE = "date_range"


class SimpleTimeQueryManager:
    """简化版时间查询管理器 - 修复时间范围计算"""

    def __init__(self):
        self.current_query_type = TimeQueryType.NATURAL_WEEK
        self.current_config = self._get_natural_week_config()

    def _get_natural_week_config(self) -> Dict[str, Any]:
        """获取本周配置（修复版本）- 排除当天数据，避免延迟更新问题"""
        today = date.today()
        days_since_monday = today.weekday()
        week_start = today - timedelta(days=days_since_monday)

        # 修复问题1：本周不包含当天，结束时间为前一天
        # 防止当天数据延迟更新的问题
        yesterday = today - timedelta(days=1)
        week_end = week_start + timedelta(days=6)

        # 如果本周还没结束，则结束时间为昨天
        if week_end >= today:
            week_end = yesterday

        # 确保开始时间不超过结束时间
        if week_start > week_end:
            week_start = week_end

        # 计算实际天数
        actual_days = (week_end - week_start).days + 1

        return {
            'type': TimeQueryType.NATURAL_WEEK,
            'start_date': week_start,
            'end_date': week_end,
            'days_count': actual_days,
            'description': f"本周 ({week_start.strftime('%m月%d日')} - {week_end.strftime('%m月%d日')})",
            'label': f"自然周 {week_start.strftime('%Y年第%U周')}"
        }

    def _get_last_week_config(self) -> Dict[str, Any]:
        """获取上周配置 - 修复版本，确保完整的周一到周日"""
        today = date.today()
        # 修复问题3：确保正确计算周一
        days_since_monday = today.weekday()  # 0=周一, 6=周日
        this_week_start = today - timedelta(days=days_since_monday)
        last_week_start = this_week_start - timedelta(days=7)
        last_week_end = last_week_start + timedelta(days=6)  # 完整的7天，周一到周日

        return {
            'type': TimeQueryType.LAST_WEEK,
            'start_date': last_week_start,
            'end_date': last_week_end,
            'days_count': 7,  # 上周总是完整的7天
            'description': f"上周 ({last_week_start.strftime('%m月%d日')} - {last_week_end.strftime('%m月%d日')})",
            'label': f"上周 {last_week_start.strftime('%Y年第%U周')}"
        }

    def get_recent_days_config(self, days: int) -> Dict[str, Any]:
        """获取近N天配置"""
        # 修复问题1：近N天也排除当天，结束时间为昨天
        end_date = date.today() - timedelta(days=1)  # 结束时间为昨天
        start_date = end_date - timedelta(days=days - 1)

        return {
            'type': TimeQueryType.RECENT_DAYS,
            'start_date': start_date,
            'end_date': end_date,
            'days_count': days,
            'description': f"近{days}天 ({start_date.strftime('%m月%d日')} - {end_date.strftime('%m月%d日')})",
            'label': f"近{days}天"
        }

    def get_natural_month_config(self) -> Dict[str, Any]:
        """获取自然月配置"""
        today = date.today()
        month_start = today.replace(day=1)
        _, last_day = calendar.monthrange(today.year, today.month)
        month_end = today.replace(day=last_day)

        # 修复问题1：本月也排除当天，结束日期为昨天
        yesterday = today - timedelta(days=1)
        if month_end > yesterday:
            month_end = yesterday

        # 确保开始时间不超过结束时间
        if month_start > month_end:
            month_start = month_end

        days_count = (month_end - month_start).days + 1

        return {
            'type': TimeQueryType.NATURAL_MONTH,
            'start_date': month_start,
            'end_date': month_end,
            'days_count': days_count,
            'description': f"本月 ({month_start.strftime('%m月%d日')} - {month_end.strftime('%m月%d日')})",
            'label': f"自然月 {month_start.strftime('%Y年%m月')}"
        }

    def get_date_range_config(self, start_date: date, end_date: date) -> Dict[str, Any]:
        """获取自定义日期区间配置"""
        # 确保结束日期不超过昨天
        yesterday = date.today() - timedelta(days=1)
        if end_date > yesterday:
            end_date = yesterday

        # 确保开始时间不超过结束时间
        if start_date > end_date:
            start_date = end_date

        days_count = (end_date - start_date).days + 1

        return {
            'type': TimeQueryType.DATE_RANGE,
            'start_date': start_date,
            'end_date': end_date,
            'days_count': days_count,
            'description': f"自定义 ({start_date.strftime('%m月%d日')} - {end_date.strftime('%m月%d日')})",
            'label': f"自定义区间"
        }

    def set_query_type(self, query_type: TimeQueryType, **kwargs):
        """设置查询类型和配置"""
        self.current_query_type = query_type

        if query_type == TimeQueryType.NATURAL_WEEK:
            self.current_config = self._get_natural_week_config()
        elif query_type == TimeQueryType.LAST_WEEK:
            self.current_config = self._get_last_week_config()
        elif query_type == TimeQueryType.RECENT_DAYS:
            days = kwargs.get('days', 7)
            self.current_config = self.get_recent_days_config(days)
        elif query_type == TimeQueryType.NATURAL_MONTH:
            self.current_config = self.get_natural_month_config()
        elif query_type == TimeQueryType.DATE_RANGE:
            start_date = kwargs.get('start_date')
            end_date = kwargs.get('end_date')
            if start_date and end_date:
                self.current_config = self.get_date_range_config(start_date, end_date)

    def get_current_config(self) -> Dict[str, Any]:
        """获取当前时间查询配置"""
        return self.current_config.copy()

    def get_datetime_range(self) -> Tuple[datetime, datetime]:
        """获取datetime格式的时间范围 - 修复版本"""
        config = self.get_current_config()

        # 重要修复：确保开始时间是当天的00:00:00，结束时间是当天的23:59:59
        start_datetime = datetime.combine(config['start_date'], datetime.min.time())
        # 结束时间设置为当天的23:59:59
        end_datetime = datetime.combine(config['end_date'], datetime.max.time().replace(microsecond=999999))

        return start_datetime, end_datetime

    def format_summary(self) -> str:
        """格式化当前时间查询摘要"""
        config = self.get_current_config()
        return f"📅 {config['label']}: {config['description']} (共{config['days_count']}天)"


class SimpleTimeQueryInterface:
    """修复版时间查询交互界面"""

    def __init__(self, manager: SimpleTimeQueryManager):
        self.manager = manager

    def show_time_selection_menu(self):
        """显示时间选择菜单"""
        print("\n📅 时间查询设置")
        print("━" * 50)

        menu = """
选择时间范围:
1. 📅 本周 (自然周，默认)    - 本周周一到昨天
2. 📅 上周 (自然周)         - 上周周一到周日 (7天)
3. 📅 近7天                 - 最近7天数据 (排除今天)
4. 📅 近14天                - 最近14天数据 (排除今天)
5. 📅 近30天                - 最近30天数据 (排除今天)
6. 📅 本月 (自然月)         - 本月1日到昨天
7. 📅 自定义日期区间        - 指定开始和结束日期
8. 🔙 返回上级菜单         - 保持当前设置
        """
        print(menu)

        return self._get_user_choice()

    def _get_user_choice(self) -> bool:
        """获取用户选择"""
        try:
            choice = input("请选择 (1-8): ").strip()

            if choice == '1':
                self.manager.set_query_type(TimeQueryType.NATURAL_WEEK)
                print("✅ 已设置为本周 (自然周，排除今天)")

            elif choice == '2':
                self.manager.set_query_type(TimeQueryType.LAST_WEEK)
                print("✅ 已设置为上周 (自然周，7天)")

            elif choice == '3':
                self.manager.set_query_type(TimeQueryType.RECENT_DAYS, days=7)
                print("✅ 已设置为近7天 (排除今天)")

            elif choice == '4':
                self.manager.set_query_type(TimeQueryType.RECENT_DAYS, days=14)
                print("✅ 已设置为近14天 (排除今天)")

            elif choice == '5':
                self.manager.set_query_type(TimeQueryType.RECENT_DAYS, days=30)
                print("✅ 已设置为近30天 (排除今天)")

            elif choice == '6':
                self.manager.set_query_type(TimeQueryType.NATURAL_MONTH)
                print("✅ 已设置为本月 (自然月，排除今天)")

            elif choice == '7':
                if self._handle_custom_date_range():
                    print("✅ 已设置为自定义日期区间")
                else:
                    print("❌ 自定义日期设置失败")

            elif choice == '8':
                print("🔙 保持当前设置")
                return False

            else:
                print("❌ 无效选择")
                return False

            return True

        except (EOFError, KeyboardInterrupt):
            return False

    def _handle_custom_date_range(self) -> bool:
        """处理自定义日期区间输入"""
        try:
            print("\n📅 自定义日期区间")
            print("日期格式: YYYY-MM-DD (例如: 2024-08-01)")

            start_input = input("请输入开始日期: ").strip()
            end_input = input("请输入结束日期: ").strip()

            start_date = datetime.strptime(start_input, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_input, '%Y-%m-%d').date()

            if start_date > end_date:
                print("❌ 开始日期不能晚于结束日期")
                return False

            # 确保结束日期不超过昨天
            yesterday = date.today() - timedelta(days=1)
            if end_date > yesterday:
                print(f"⚠️ 结束日期已调整为昨天 ({yesterday.strftime('%Y-%m-%d')}) 以避免数据延迟问题")
                end_date = yesterday

            self.manager.set_query_type(TimeQueryType.DATE_RANGE,
                                        start_date=start_date, end_date=end_date)
            return True

        except ValueError:
            print("❌ 日期格式错误")
            return False
        except (EOFError, KeyboardInterrupt):
            return False


# 全局实例
global_time_manager = SimpleTimeQueryManager()
global_time_interface = SimpleTimeQueryInterface(global_time_manager)


# 便捷函数
def get_current_time_config() -> Dict[str, Any]:
    """获取当前时间配置"""
    return global_time_manager.get_current_config()


def get_current_datetime_range() -> Tuple[datetime, datetime]:
    """获取当前datetime时间范围"""
    return global_time_manager.get_datetime_range()


def show_time_selection_menu():
    """显示时间选择菜单"""
    return global_time_interface.show_time_selection_menu()


def get_time_summary() -> str:
    """获取时间查询摘要"""
    return global_time_manager.format_summary()


def get_days_count() -> int:
    """获取当前时间范围的天数"""
    config = get_current_time_config()
    return config['days_count']


def get_time_description() -> str:
    """获取时间范围描述"""
    config = get_current_time_config()
    return config['description']


def is_natural_week() -> bool:
    """判断是否为自然周查询"""
    config = get_current_time_config()
    return config['type'] == TimeQueryType.NATURAL_WEEK


def is_last_week() -> bool:
    """判断是否为上周查询"""
    config = get_current_time_config()
    return config['type'] == TimeQueryType.LAST_WEEK


def is_natural_month() -> bool:
    """判断是否为自然月查询"""
    config = get_current_time_config()
    return config['type'] == TimeQueryType.NATURAL_MONTH


def set_default_time_query(query_type: TimeQueryType = TimeQueryType.NATURAL_WEEK):
    """设置默认时间查询类型 - 默认自然周（排除当天）"""
    global_time_manager.set_query_type(query_type)


def reset_to_default():
    """重置为默认设置（自然周，排除当天）"""
    global_time_manager.set_query_type(TimeQueryType.NATURAL_WEEK)


# 为新APP分析专门提供的便捷函数
def get_app_analysis_days() -> int:
    """获取APP分析天数 - 兼容新APP分析时间管理器"""
    return get_days_count()


def get_app_analysis_description() -> str:
    """获取APP分析时间描述 - 兼容新APP分析时间管理器"""
    return get_time_description()


# 使用示例和测试
if __name__ == "__main__":
    # 创建时间管理器
    manager = SimpleTimeQueryManager()
    interface = SimpleTimeQueryInterface(manager)

    # 显示当前设置
    print("当前设置:", manager.format_summary())

    # 显示可用选项
    print("\n默认配置:")
    print("1. 本周 (默认):", manager._get_natural_week_config()['description'])
    print("2. 上周:", manager._get_last_week_config()['description'])
    print("3. 近7天:", manager.get_recent_days_config(7)['description'])
    print("4. 近30天:", manager.get_recent_days_config(30)['description'])
    print("5. 自然月:", manager.get_natural_month_config()['description'])

    # 测试时间选择菜单
    print("\n测试时间选择菜单:")
    interface.show_time_selection_menu()

    # 显示更新后的设置
    print("\n更新后设置:", manager.format_summary())

    # 测试便捷函数
    print(f"\n便捷函数测试:")
    print(f"天数: {get_days_count()}")
    print(f"描述: {get_time_description()}")
    print(f"是否本周: {is_natural_week()}")
    print(f"是否上周: {is_last_week()}")
    print(f"是否自然月: {is_natural_month()}")

    # 测试兼容性函数
    print(f"\nAPP分析兼容性:")
    print(f"APP分析天数: {get_app_analysis_days()}")
    print(f"APP分析描述: {get_app_analysis_description()}")

    # 测试datetime范围
    start_dt, end_dt = get_current_datetime_range()
    print(f"\nDatetime范围测试:")
    print(f"开始时间: {start_dt.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"结束时间: {end_dt.strftime('%Y-%m-%d %H:%M:%S')}")