"""
APP涉案监控系统 - 数据模型
定义案件、APP、分析结果等数据结构
"""
from dataclasses import dataclass
from datetime import datetime
from typing import List, Dict, Optional, Any
from config import FIELD_MAPPING


@dataclass
class CaseRecord:
    """案件记录数据模型"""
    case_number: str
    final_app_name: str
    involved_amount: float
    insert_day: datetime
    occurrence_area: str
    case_main_type: str
    case_sub_type: str
    victim_age: Optional[int] = None
    brief_case_description: Optional[str] = None
    transfer_method: Optional[str] = None
    suspect_phone_number: Optional[str] = None
    suspect_account_number: Optional[str] = None
    occurrence_time: Optional[datetime] = None

    # 衍生字段
    age_group: Optional[str] = None
    amount_level: Optional[str] = None
    hour: Optional[int] = None
    day_of_week: Optional[int] = None
    date: Optional[datetime] = None

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CaseRecord':
        """从字典创建案件记录"""
        return cls(**{k: v for k, v in data.items() if k in cls.__annotations__})

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            field: getattr(self, field)
            for field in self.__annotations__
            if hasattr(self, field) and getattr(self, field) is not None
        }


@dataclass
class AppProfile:
    """APP画像数据模型"""
    app_name: str
    case_count: int
    total_amount: float
    avg_amount: float
    max_amount: float
    first_seen: datetime
    last_seen: datetime
    victim_ages: List[int]
    areas: List[str]
    case_types: List[str]

    # 风险评估
    risk_level: str = "低"
    risk_score: float = 0.0

    @property
    def avg_victim_age(self) -> float:
        """平均受害人年龄"""
        return sum(self.victim_ages) / len(self.victim_ages) if self.victim_ages else 0

    @property
    def active_days(self) -> int:
        """活跃天数"""
        return (self.last_seen - self.first_seen).days + 1

    @property
    def daily_case_avg(self) -> float:
        """日均案件数"""
        return self.case_count / max(self.active_days, 1)

    @property
    def daily_amount_avg(self) -> float:
        """日均涉案金额"""
        return self.total_amount / max(self.active_days, 1)


@dataclass
class FinancialAnalysis:
    """资金分析结果"""
    total_amount: float
    avg_amount: float
    median_amount: float
    max_amount: float
    case_count: int
    amount_distribution: Dict[str, int]  # 金额等级分布
    high_risk_cases: int

    def get_summary(self) -> Dict[str, Any]:
        """获取摘要信息"""
        return {
            'total_amount': self.total_amount,
            'avg_amount': self.avg_amount,
            'median_amount': self.median_amount,
            'max_amount': self.max_amount,
            'case_count': self.case_count,
            'high_risk_cases': self.high_risk_cases,
            'high_risk_percentage': (self.high_risk_cases / self.case_count * 100) if self.case_count > 0 else 0
        }


@dataclass
class NewAppAlert:
    """新APP预警"""
    app_name: str
    first_seen: datetime
    case_count: int
    total_amount: float
    avg_amount: float
    risk_level: str

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'app_name': self.app_name,
            'first_seen': self.first_seen.strftime('%Y-%m-%d') if self.first_seen else None,
            'case_count': self.case_count,
            'total_amount': self.total_amount,
            'avg_amount': self.avg_amount,
            'risk_level': self.risk_level
        }


@dataclass
class SuspiciousPattern:
    """可疑模式预警"""
    type: str  # 预警类型：案件数激增、异常高额、APP变种、地区集中等
    app_name: str
    severity: str  # 严重程度：高、中、低
    description: str
    recommendation: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'type': self.type,
            'app_name': self.app_name,
            'severity': self.severity,
            'description': self.description,
            'recommendation': self.recommendation
        }


@dataclass
class NetworkCluster:
    """网络集群"""
    cluster_id: str
    cluster_type: str  # 年龄相似、地域相似、时间相似等
    apps: List[str]
    similarity: float
    description: str

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'cluster_id': self.cluster_id,
            'type': self.cluster_type,
            'apps': self.apps,
            'similarity': self.similarity,
            'description': self.description
        }


@dataclass
class SuspiciousNetwork:
    """可疑网络"""
    network_type: str  # 时间爆发网络、金额异常关联、地理集中网络
    apps: List[str]
    risk_level: str
    description: str
    date: Optional[str] = None
    area: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'type': self.network_type,
            'apps': self.apps,
            'risk_level': self.risk_level,
            'description': self.description,
            'date': self.date,
            'area': self.area
        }


@dataclass
class VictimProfile:
    """受害人画像"""
    avg_age: float
    median_age: float
    age_range: tuple
    age_distribution: Dict[str, int]  # 年龄段分布
    total_victims: int

    def get_summary(self) -> Dict[str, Any]:
        """获取摘要信息"""
        return {
            'avg_age': self.avg_age,
            'median_age': self.median_age,
            'age_range': f"{self.age_range[0]}-{self.age_range[1]}岁",
            'total_victims': self.total_victims,
            'age_distribution': self.age_distribution
        }


@dataclass
class RiskAssessment:
    """风险评估结果"""
    risk_level: str  # 高风险、中风险、低风险、正常
    risk_score: float  # 0-100分
    generated_time: datetime
    data_period: str
    total_cases: int
    total_amount: float
    unique_apps: int

    # 各项风险评分
    new_apps_score: float = 0.0
    alerts_score: float = 0.0
    financial_score: float = 0.0
    case_volume_score: float = 0.0

    # 建议措施
    recommendations: List[str] = None

    def __post_init__(self):
        if self.recommendations is None:
            self.recommendations = []

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'risk_level': self.risk_level,
            'risk_score': self.risk_score,
            'generated_time': self.generated_time.isoformat(),
            'data_period': self.data_period,
            'total_cases': self.total_cases,
            'total_amount': self.total_amount,
            'unique_apps': self.unique_apps,
            'score_breakdown': {
                'new_apps': self.new_apps_score,
                'alerts': self.alerts_score,
                'financial': self.financial_score,
                'case_volume': self.case_volume_score
            },
            'recommendations': self.recommendations
        }


@dataclass
class AnalysisResults:
    """分析结果集合"""
    financial_analysis: Optional[FinancialAnalysis] = None
    new_apps: Optional[List[NewAppAlert]] = None
    alerts: Optional[List[SuspiciousPattern]] = None
    fraud_methods: Optional[Dict[str, Any]] = None
    victim_profile: Optional[VictimProfile] = None
    network_analysis: Optional[Dict[str, Any]] = None
    risk_assessment: Optional[RiskAssessment] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {}

        if self.financial_analysis:
            result['financial'] = self.financial_analysis.get_summary()

        if self.new_apps:
            result['new_apps'] = [app.to_dict() for app in self.new_apps]

        if self.alerts:
            result['alerts'] = [alert.to_dict() for alert in self.alerts]

        if self.fraud_methods:
            result['fraud_methods'] = self.fraud_methods

        if self.victim_profile:
            result['victim_profile'] = self.victim_profile.get_summary()

        if self.network_analysis:
            result['network_analysis'] = self.network_analysis

        if self.risk_assessment:
            result['risk_assessment'] = self.risk_assessment.to_dict()

        return result


class DataModelFactory:
    """数据模型工厂类"""

    @staticmethod
    def create_case_record(row_data: Dict[str, Any]) -> CaseRecord:
        """创建案件记录"""
        return CaseRecord.from_dict(row_data)

    @staticmethod
    def create_app_profile(app_name: str, case_data: List[CaseRecord]) -> AppProfile:
        """创建APP画像"""
        if not case_data:
            raise ValueError("案件数据不能为空")

        amounts = [case.involved_amount for case in case_data if case.involved_amount]
        victim_ages = [case.victim_age for case in case_data if case.victim_age]
        areas = [case.occurrence_area for case in case_data if case.occurrence_area]
        case_types = [case.case_main_type for case in case_data if case.case_main_type]

        return AppProfile(
            app_name=app_name,
            case_count=len(case_data),
            total_amount=sum(amounts),
            avg_amount=sum(amounts) / len(amounts) if amounts else 0,
            max_amount=max(amounts) if amounts else 0,
            first_seen=min(case.insert_day for case in case_data),
            last_seen=max(case.insert_day for case in case_data),
            victim_ages=victim_ages,
            areas=list(set(areas)),
            case_types=list(set(case_types))
        )

    @staticmethod
    def create_financial_analysis(df) -> FinancialAnalysis:
        """创建资金分析结果"""
        if df is None or df.empty or 'involved_amount' not in df.columns:
            return None

        amount_data = df['involved_amount'].dropna()
        amount_data = amount_data[amount_data > 0]

        if amount_data.empty:
            return None

        # 金额等级分布
        amount_distribution = {}
        if 'amount_level' in df.columns:
            amount_distribution = df['amount_level'].value_counts().to_dict()

        # 高风险案件（≥10万）
        high_risk_cases = len(amount_data[amount_data >= 100000])

        return FinancialAnalysis(
            total_amount=float(amount_data.sum()),
            avg_amount=float(amount_data.mean()),
            median_amount=float(amount_data.median()),
            max_amount=float(amount_data.max()),
            case_count=len(amount_data),
            amount_distribution=amount_distribution,
            high_risk_cases=high_risk_cases
        )

    @staticmethod
    def create_victim_profile(df) -> VictimProfile:
        """创建受害人画像"""
        if df is None or df.empty or 'victim_age' not in df.columns:
            return None

        age_data = df['victim_age'].dropna()
        if age_data.empty:
            return None

        # 年龄段分布
        age_distribution = {}
        if 'age_group' in df.columns:
            age_distribution = df['age_group'].value_counts().to_dict()

        return VictimProfile(
            avg_age=float(age_data.mean()),
            median_age=float(age_data.median()),
            age_range=(int(age_data.min()), int(age_data.max())),
            age_distribution=age_distribution,
            total_victims=len(age_data)
        )