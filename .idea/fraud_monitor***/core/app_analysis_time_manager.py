"""
新APP分析时间管理器 - 支持灵活的时间范围设置
"""
from datetime import date, datetime, timedelta
from typing import Dict, Any, Tuple, Optional
import calendar
from enum import Enum


class AppAnalysisTimeType(Enum):
    """新APP分析时间类型"""
    RECENT_DAYS = "recent_days"  # 近N天
    NATURAL_WEEK = "natural_week"  # 自然周
    NATURAL_MONTH = "natural_month"  # 自然月
    CUSTOM_DATE_RANGE = "custom_range"  # 自定义日期范围


class AppAnalysisTimeManager:
    """新APP分析时间管理器"""

    def __init__(self):
        # 默认设置为自然周
        self.current_type = AppAnalysisTimeType.NATURAL_WEEK
        self.current_config = self._get_current_natural_week()

    def _get_current_natural_week(self) -> Dict[str, Any]:
        """获取当前自然周（周一到周日）"""
        today = date.today()
        days_since_monday = today.weekday()
        week_start = today - timedelta(days=days_since_monday)
        week_end = week_start + timedelta(days=6)

        return {
            'type': AppAnalysisTimeType.NATURAL_WEEK,
            'start_date': week_start,
            'end_date': week_end,
            'days_count': 7,
            'description': f"本周 ({week_start.strftime('%m月%d日')} - {week_end.strftime('%m月%d日')})",
            'query_description': f"自然周 {week_start.strftime('%Y年第%U周')}"
        }

    def set_recent_days(self, days: int) -> bool:
        """设置近N天"""
        if days <= 0 or days > 365:
            return False

        end_date = date.today()
        start_date = end_date - timedelta(days=days - 1)

        self.current_type = AppAnalysisTimeType.RECENT_DAYS
        self.current_config = {
            'type': AppAnalysisTimeType.RECENT_DAYS,
            'start_date': start_date,
            'end_date': end_date,
            'days_count': days,
            'description': f"近{days}天 ({start_date.strftime('%m月%d日')} - {end_date.strftime('%m月%d日')})",
            'query_description': f"近{days}天"
        }
        return True

    def set_natural_week(self, week_range: str = None) -> bool:
        """设置自然周 - 支持输入格式如 20250728-20250803"""
        try:
            if week_range:
                # 解析格式：20250728-20250803
                if '-' in week_range and len(week_range) == 17:
                    start_str, end_str = week_range.split('-')
                    start_date = datetime.strptime(start_str, '%Y%m%d').date()
                    end_date = datetime.strptime(end_str, '%Y%m%d').date()

                    # 验证是否为完整的一周
                    if (end_date - start_date).days != 6:
                        return False

                    # 验证是否为周一到周日
                    if start_date.weekday() != 0 or end_date.weekday() != 6:
                        return False

                    self.current_config = {
                        'type': AppAnalysisTimeType.NATURAL_WEEK,
                        'start_date': start_date,
                        'end_date': end_date,
                        'days_count': 7,
                        'description': f"指定周 ({start_date.strftime('%m月%d日')} - {end_date.strftime('%m月%d日')})",
                        'query_description': f"自然周 {start_date.strftime('%Y年第%U周')}"
                    }
                else:
                    return False
            else:
                # 使用当前自然周
                self.current_config = self._get_current_natural_week()

            self.current_type = AppAnalysisTimeType.NATURAL_WEEK
            return True

        except (ValueError, AttributeError):
            return False

    def set_natural_month(self, month_str: str = None) -> bool:
        """设置自然月 - 支持输入格式如 202507"""
        try:
            if month_str:
                # 解析格式：202507
                if len(month_str) == 6:
                    year = int(month_str[:4])
                    month = int(month_str[4:6])

                    if month < 1 or month > 12:
                        return False

                    month_start = date(year, month, 1)
                    _, last_day = calendar.monthrange(year, month)
                    month_end = date(year, month, last_day)

                    self.current_config = {
                        'type': AppAnalysisTimeType.NATURAL_MONTH,
                        'start_date': month_start,
                        'end_date': month_end,
                        'days_count': (month_end - month_start).days + 1,
                        'description': f"指定月 ({month_start.strftime('%Y年%m月')})",
                        'query_description': f"{year}年{month}月"
                    }
                else:
                    return False
            else:
                # 使用当前月
                today = date.today()
                month_start = date(today.year, today.month, 1)
                _, last_day = calendar.monthrange(today.year, today.month)
                month_end = date(today.year, today.month, last_day)

                self.current_config = {
                    'type': AppAnalysisTimeType.NATURAL_MONTH,
                    'start_date': month_start,
                    'end_date': month_end,
                    'days_count': (month_end - month_start).days + 1,
                    'description': f"本月 ({month_start.strftime('%Y年%m月')})",
                    'query_description': f"{today.year}年{today.month}月"
                }

            self.current_type = AppAnalysisTimeType.NATURAL_MONTH
            return True

        except (ValueError, AttributeError):
            return False

    def set_custom_range(self, start_date: date, end_date: date) -> bool:
        """设置自定义日期范围"""
        try:
            if start_date > end_date:
                start_date, end_date = end_date, start_date

            days_count = (end_date - start_date).days + 1
            if days_count > 365:
                return False

            self.current_type = AppAnalysisTimeType.CUSTOM_DATE_RANGE
            self.current_config = {
                'type': AppAnalysisTimeType.CUSTOM_DATE_RANGE,
                'start_date': start_date,
                'end_date': end_date,
                'days_count': days_count,
                'description': f"自定义 ({start_date.strftime('%m月%d日')} - {end_date.strftime('%m月%d日')})",
                'query_description': f"自定义范围 {days_count}天"
            }
            return True

        except (ValueError, AttributeError):
            return False

    def get_current_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return self.current_config.copy()

    def get_days_count(self) -> int:
        """获取天数"""
        return self.current_config['days_count']

    def get_description(self) -> str:
        """获取描述"""
        return self.current_config['description']

    def get_query_description(self) -> str:
        """获取查询描述"""
        return self.current_config['query_description']

    def get_date_range(self) -> Tuple[date, date]:
        """获取日期范围"""
        return self.current_config['start_date'], self.current_config['end_date']


class AppAnalysisTimeInterface:
    """新APP分析时间设置界面"""

    def __init__(self, manager: AppAnalysisTimeManager):
        self.manager = manager

    def show_time_settings_menu(self) -> bool:
        """显示时间设置菜单"""
        print("\n📅 新APP分析时间设置")
        print("━" * 60)

        # 显示当前设置
        current_desc = self.manager.get_description()
        print(f"当前设置: {current_desc}")

        menu = """
选择时间范围类型:
1. 📅 近7天                    - 最近7天数据
2. 📅 自然周                   - 当前周或指定周 (如: 20250728-20250803)
3. 📅 自然月                   - 当前月或指定月 (如: 202507)
4. 📅 自定义天数               - 近N天 (1-365天)
5. 📅 自定义日期范围           - 指定开始和结束日期
6. 🔙 返回上级菜单             - 保持当前设置
        """
        print(menu)

        return self._handle_user_choice()

    def _handle_user_choice(self) -> bool:
        """处理用户选择"""
        try:
            choice = input("请选择 (1-6): ").strip()

            if choice == '1':
                if self.manager.set_recent_days(7):
                    print("✅ 已设置为近7天")
                    return True
                else:
                    print("❌ 设置失败")

            elif choice == '2':
                return self._handle_natural_week_input()

            elif choice == '3':
                return self._handle_natural_month_input()

            elif choice == '4':
                return self._handle_custom_days_input()

            elif choice == '5':
                return self._handle_custom_range_input()

            elif choice == '6':
                print("🔙 保持当前设置")
                return False

            else:
                print("❌ 无效选择")
                return False

        except (EOFError, KeyboardInterrupt):
            return False

    def _handle_natural_week_input(self) -> bool:
        """处理自然周输入"""
        try:
            print("\n📅 自然周设置")
            print("格式说明:")
            print("  - 直接回车: 使用当前周")
            print("  - 输入格式: 20250728-20250803 (周一到周日)")

            week_input = input("请输入周范围 (或直接回车使用当前周): ").strip()

            if not week_input:
                # 使用当前周
                if self.manager.set_natural_week():
                    print("✅ 已设置为当前自然周")
                    return True
            else:
                # 使用指定周
                if self.manager.set_natural_week(week_input):
                    print("✅ 已设置为指定自然周")
                    return True
                else:
                    print("❌ 格式错误或不是完整的周一到周日")

        except Exception as e:
            print(f"❌ 设置失败: {e}")

        return False

    def _handle_natural_month_input(self) -> bool:
        """处理自然月输入"""
        try:
            print("\n📅 自然月设置")
            print("格式说明:")
            print("  - 直接回车: 使用当前月")
            print("  - 输入格式: 202507 (年月)")

            month_input = input("请输入月份 (或直接回车使用当前月): ").strip()

            if not month_input:
                # 使用当前月
                if self.manager.set_natural_month():
                    print("✅ 已设置为当前自然月")
                    return True
            else:
                # 使用指定月
                if self.manager.set_natural_month(month_input):
                    print("✅ 已设置为指定自然月")
                    return True
                else:
                    print("❌ 格式错误，请使用 YYYYMM 格式，如 202507")

        except Exception as e:
            print(f"❌ 设置失败: {e}")

        return False

    def _handle_custom_days_input(self) -> bool:
        """处理自定义天数输入"""
        try:
            print("\n📅 自定义天数设置")
            days_input = input("请输入天数 (1-365): ").strip()

            days = int(days_input)
            if self.manager.set_recent_days(days):
                print(f"✅ 已设置为近{days}天")
                return True
            else:
                print("❌ 天数必须在1-365之间")

        except ValueError:
            print("❌ 请输入有效的数字")
        except Exception as e:
            print(f"❌ 设置失败: {e}")

        return False

    def _handle_custom_range_input(self) -> bool:
        """处理自定义日期范围输入"""
        try:
            print("\n📅 自定义日期范围设置")
            print("日期格式: YYYY-MM-DD")

            start_input = input("请输入开始日期: ").strip()
            end_input = input("请输入结束日期: ").strip()

            start_date = datetime.strptime(start_input, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_input, '%Y-%m-%d').date()

            if self.manager.set_custom_range(start_date, end_date):
                print("✅ 已设置为自定义日期范围")
                return True
            else:
                print("❌ 日期范围不能超过365天")

        except ValueError:
            print("❌ 日期格式错误，请使用 YYYY-MM-DD 格式")
        except Exception as e:
            print(f"❌ 设置失败: {e}")

        return False


# 全局实例
global_app_time_manager = AppAnalysisTimeManager()
global_app_time_interface = AppAnalysisTimeInterface(global_app_time_manager)


def get_app_analysis_time_config() -> Dict[str, Any]:
    """获取新APP分析时间配置"""
    return global_app_time_manager.get_current_config()


def get_app_analysis_days() -> int:
    """获取新APP分析天数"""
    return global_app_time_manager.get_days_count()


def get_app_analysis_description() -> str:
    """获取新APP分析时间描述"""
    return global_app_time_manager.get_description()


def show_app_time_settings() -> bool:
    """显示新APP分析时间设置"""
    return global_app_time_interface.show_time_settings_menu()


def set_app_analysis_default_week():
    """设置默认为自然周"""
    global_app_time_manager.set_natural_week()


# 测试函数
if __name__ == "__main__":
    manager = AppAnalysisTimeManager()
    interface = AppAnalysisTimeInterface(manager)

    print("当前设置:", manager.get_description())
    print("天数:", manager.get_days_count())

    # 测试设置
    interface.show_time_settings_menu()

    print("\n更新后设置:", manager.get_description())
    print("天数:", manager.get_days_count())