"""
APP涉案监控系统 - 优化版数据处理器
适配新的数据库管理器，集成智能缓存，优化数据处理流程
修复对比分析数据获取问题，确保周度和月度对比有数据
"""
import pandas as pd
import numpy as np
import json
import time
from datetime import datetime, timedelta, date
from pathlib import Path
from typing import Dict, Any, Optional, List
from config import (
    CACHE_CONFIG, AGE_GROUPS, AMOUNT_LEVELS,
    INVALID_APP_PATTERNS, APP_NAME_STANDARDIZATION,
    PERFORMANCE_CONFIG, QUERY_CONFIG
)


class OptimizedDataProcessor:
    """优化版数据处理器 - 集成智能缓存和高性能数据处理"""

    def __init__(self, db_manager):
        self.db_manager = db_manager
        self._memory_cache = {}
        self._cache_stats = {
            'hits': 0,
            'misses': 0,
            'total_requests': 0
        }
        self._processing_stats = {
            'total_processed': 0,
            'total_time': 0,
            'avg_processing_time': 0
        }

    def get_comprehensive_data(self, days: int = 30, use_cache: bool = True) -> Optional[pd.DataFrame]:
        """获取综合数据 - 修复版本，解决缓存导致的时间范围不一致问题"""

        # 修复缓存键生成：基于实际时间范围而不是天数
        try:
            from core.time_query_manager import get_current_time_config
            time_config = get_current_time_config()
            start_date = time_config['start_date']
            end_date = time_config['end_date']
            cache_key = f"comprehensive_data_{start_date}_{end_date}"
            print(f"🔍 缓存键基于实际时间范围: {start_date} 至 {end_date}")
        except ImportError:
            cache_key = f"comprehensive_data_{days}"

        # 更新统计
        self._cache_stats['total_requests'] += 1

        # 检查内存缓存
        if use_cache and CACHE_CONFIG['enable_intelligent_cache']:
            cached_data = self._get_from_memory_cache(cache_key)
            if cached_data is not None:
                self._cache_stats['hits'] += 1
                print(f"📦 使用内存缓存数据 (命中率: {self.get_cache_hit_rate():.1f}%)")
                return cached_data

        self._cache_stats['misses'] += 1

        try:
            start_time = time.time()

            # 使用兼容的数据库查询方法
            print(f"📡 获取数据 (最近{days}天)...")

            # 检测数据库管理器的方法签名并调用相应方法
            if hasattr(self.db_manager, 'query_comprehensive_data'):
                # 尝试检测方法签名
                import inspect
                sig = inspect.signature(self.db_manager.query_comprehensive_data)
                params = list(sig.parameters.keys())

                if 'use_optimization' in params:
                    # 新版本方法
                    df = self.db_manager.query_comprehensive_data(days=days, use_optimization=True)
                elif 'use_cache' in params:
                    # 兼容版本方法
                    df = self.db_manager.query_comprehensive_data(days=days, use_cache=use_cache)
                else:
                    # 只有days参数的简化版本
                    df = self.db_manager.query_comprehensive_data(days=days)
            else:
                print("❌ 数据库管理器缺少 query_comprehensive_data 方法")
                return None

            if df is None or df.empty:
                print("⚠️ 查询结果为空")
                return None

            processing_time = time.time() - start_time

            # 更新统计
            self._update_processing_stats(processing_time, len(df))

            print(f"✅ 获取 {len(df)} 条记录")

            # 存储到内存缓存 - 使用修复的缓存键
            if use_cache and CACHE_CONFIG['enable_intelligent_cache']:
                self._store_in_memory_cache(cache_key, df)

            return df

        except Exception as e:
            print(f"❌ 数据获取失败: {e}")
            # 如果是参数错误，尝试使用最简单的调用方式
            if "unexpected keyword argument" in str(e):
                try:
                    print("🔄 尝试使用简化调用方式...")
                    df = self.db_manager.query_comprehensive_data(days)
                    if df is not None and not df.empty:
                        print(f"✅ 简化调用成功，获取 {len(df)} 条记录")
                        return df
                except Exception as e2:
                    print(f"❌ 简化调用也失败: {e2}")
            return None

    def get_data_by_date_range(self, start_date: date, end_date: date, use_cache: bool = True) -> Optional[
        pd.DataFrame]:
        """根据日期范围获取数据 - 静默版本"""
        cache_key = f"data_range_{start_date}_{end_date}"

        # 更新统计
        self._cache_stats['total_requests'] += 1

        # 检查内存缓存
        if use_cache and CACHE_CONFIG['enable_intelligent_cache']:
            cached_data = self._get_from_memory_cache(cache_key)
            if cached_data is not None:
                self._cache_stats['hits'] += 1
                return cached_data

        self._cache_stats['misses'] += 1

        try:
            start_time = time.time()

            # 使用数据库管理器的日期范围查询方法
            if hasattr(self.db_manager, 'query_data_by_date_range'):
                df = self.db_manager.query_data_by_date_range(start_date, end_date)
            else:
                # 回退方法：计算天数差并使用常规查询
                days_diff = (end_date - start_date).days + 1
                df = self.db_manager.query_comprehensive_data(days=days_diff)

                # 手动过滤日期范围
                if df is not None and not df.empty and 'insert_day' in df.columns:
                    start_datetime = pd.to_datetime(start_date)
                    end_datetime = pd.to_datetime(end_date) + pd.Timedelta(hours=23, minutes=59, seconds=59)
                    df = df[(df['insert_day'] >= start_datetime) & (df['insert_day'] <= end_datetime)]

            if df is None or df.empty:
                return pd.DataFrame()

            processing_time = time.time() - start_time

            # 更新统计
            self._update_processing_stats(processing_time, len(df))

            # 存储到内存缓存
            if use_cache and CACHE_CONFIG['enable_intelligent_cache']:
                self._store_in_memory_cache(cache_key, df)

            return df

        except Exception as e:
            print(f"❌ 日期范围数据获取失败: {e}")
            return pd.DataFrame()

    def get_comparison_data(self, current_start: date, current_end: date,
                            previous_start: date, previous_end: date) -> Dict[str, pd.DataFrame]:
        """获取对比分析用的数据 - 静默版本"""
        try:
            # 计算总的时间范围
            earliest_date = min(current_start, previous_start)
            latest_date = max(current_end, previous_end)
            total_days = (latest_date - earliest_date).days + 1

            # 一次性获取大范围数据
            all_data = self.get_data_by_date_range(earliest_date, latest_date, use_cache=True)

            if all_data is None or all_data.empty:
                return {'current': pd.DataFrame(), 'previous': pd.DataFrame()}

            # 分离当前期和对比期数据
            current_data = self._filter_data_by_date_range(all_data, current_start, current_end)
            previous_data = self._filter_data_by_date_range(all_data, previous_start, previous_end)

            return {
                'current': current_data,
                'previous': previous_data,
                'all_data': all_data
            }

        except Exception as e:
            print(f"❌ 对比数据获取失败: {e}")
            return {'current': pd.DataFrame(), 'previous': pd.DataFrame()}

    def _filter_data_by_date_range(self, df: pd.DataFrame, start_date: date, end_date: date) -> pd.DataFrame:
        """根据日期范围过滤数据"""
        try:
            if df is None or df.empty or 'insert_day' not in df.columns:
                return pd.DataFrame()

            # 确保 insert_day 是 datetime 类型
            if not pd.api.types.is_datetime64_any_dtype(df['insert_day']):
                df['insert_day'] = pd.to_datetime(df['insert_day'])

            start_datetime = pd.to_datetime(start_date)
            end_datetime = pd.to_datetime(end_date) + pd.Timedelta(hours=23, minutes=59, seconds=59)

            filtered_df = df[(df['insert_day'] >= start_datetime) & (df['insert_day'] <= end_datetime)]
            return filtered_df

        except Exception as e:
            print(f"⚠️ 数据过滤失败: {e}")
            return pd.DataFrame()

    def add_derived_fields(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加衍生字段 - 优化版本"""
        if df is None or df.empty:
            return df

        try:
            print("🔄 添加衍生字段...")
            start_time = time.time()

            df_enhanced = df.copy()

            # 1. 年龄分组 - 向量化处理
            if 'victim_age' in df_enhanced.columns:
                df_enhanced = self._add_age_groups_vectorized(df_enhanced)

            # 2. 金额等级分组 - 向量化处理
            if 'involved_amount' in df_enhanced.columns:
                df_enhanced = self._add_amount_levels_vectorized(df_enhanced)

            # 3. 时间衍生字段 - 批量处理
            if 'insert_day' in df_enhanced.columns:
                df_enhanced = self._add_time_features_batch(df_enhanced)

            # 4. APP名称标准化 - 优化版本
            if 'final_app_name' in df_enhanced.columns:
                df_enhanced = self._standardize_app_names_optimized(df_enhanced)

            processing_time = time.time() - start_time
            print(f"✅ 衍生字段处理完成，耗时 {processing_time:.2f}秒")

            return df_enhanced

        except Exception as e:
            print(f"⚠️ 衍生字段处理失败: {e}")
            return df

    def _add_age_groups_vectorized(self, df: pd.DataFrame) -> pd.DataFrame:
        """向量化年龄分组处理"""
        try:
            ages = df['victim_age'].fillna(0)

            # 使用 numpy.select 进行向量化分组
            conditions = [
                (ages >= 0) & (ages <= 18),
                (ages > 18) & (ages <= 30),
                (ages > 30) & (ages <= 50),
                (ages > 50) & (ages <= 65),
                ages > 65
            ]
            choices = ['未成年', '青年', '中年', '中老年', '老年']

            df['age_group'] = np.select(conditions, choices, default='未知')
            return df

        except Exception as e:
            print(f"⚠️ 年龄分组失败: {e}")
            return df

    def _add_amount_levels_vectorized(self, df: pd.DataFrame) -> pd.DataFrame:
        """向量化金额等级处理"""
        try:
            amounts = df['involved_amount'].fillna(0)

            # 使用 numpy.select 进行向量化分组
            conditions = [
                (amounts >= 0) & (amounts < 10000),
                (amounts >= 10000) & (amounts < 100000),
                (amounts >= 100000) & (amounts < 500000),
                (amounts >= 500000) & (amounts < 1000000),
                amounts >= 1000000
            ]
            choices = ['小额', '中额', '大额', '巨额', '特大额']

            df['amount_level'] = np.select(conditions, choices, default='未知')
            return df

        except Exception as e:
            print(f"⚠️ 金额等级分组失败: {e}")
            return df

    def _add_time_features_batch(self, df: pd.DataFrame) -> pd.DataFrame:
        """批量添加时间特征"""
        try:
            # 确保 insert_day 是 datetime 类型
            if not pd.api.types.is_datetime64_any_dtype(df['insert_day']):
                df['insert_day'] = pd.to_datetime(df['insert_day'])

            # 批量提取时间特征
            df['hour'] = df['insert_day'].dt.hour
            df['day_of_week'] = df['insert_day'].dt.dayofweek
            df['date'] = df['insert_day'].dt.date
            df['month'] = df['insert_day'].dt.month
            df['weekday'] = df['insert_day'].dt.day_name()

            return df

        except Exception as e:
            print(f"⚠️ 时间特征处理失败: {e}")
            return df

    def _standardize_app_names_optimized(self, df: pd.DataFrame) -> pd.DataFrame:
        """优化的APP名称标准化"""
        try:
            if 'final_app_name' not in df.columns:
                return df

            # 创建标准化映射的向量化版本
            app_names = df['final_app_name'].fillna('')

            # 批量应用标准化映射
            for old_name, new_name in APP_NAME_STANDARDIZATION.items():
                mask = app_names.str.lower() == old_name.lower()
                df.loc[mask, 'final_app_name'] = new_name

            return df

        except Exception as e:
            print(f"⚠️ APP名称标准化失败: {e}")
            return df

    def get_filtered_data(self, df: pd.DataFrame, filters: Dict[str, Any]) -> pd.DataFrame:
        """获取过滤后的数据 - 优化版本"""
        if df is None or df.empty:
            return df

        try:
            filtered_df = df.copy()

            # 应用过滤条件
            for column, condition in filters.items():
                if column not in filtered_df.columns:
                    continue

                if isinstance(condition, dict):
                    # 范围过滤
                    if 'min' in condition:
                        filtered_df = filtered_df[filtered_df[column] >= condition['min']]
                    if 'max' in condition:
                        filtered_df = filtered_df[filtered_df[column] <= condition['max']]
                elif isinstance(condition, list):
                    # 列表过滤
                    filtered_df = filtered_df[filtered_df[column].isin(condition)]
                else:
                    # 单值过滤
                    filtered_df = filtered_df[filtered_df[column] == condition]

            return filtered_df

        except Exception as e:
            print(f"⚠️ 数据过滤失败: {e}")
            return df

    def get_aggregated_data(self, df: pd.DataFrame, group_by: List[str],
                            agg_config: Dict[str, Any]) -> pd.DataFrame:
        """获取聚合数据 - 优化版本"""
        if df is None or df.empty:
            return df

        try:
            # 验证分组字段
            valid_group_by = [col for col in group_by if col in df.columns]
            if not valid_group_by:
                print("⚠️ 无有效分组字段")
                return df

            # 执行聚合
            grouped_data = df.groupby(valid_group_by).agg(agg_config)

            # 重置索引
            grouped_data = grouped_data.reset_index()

            return grouped_data

        except Exception as e:
            print(f"⚠️ 数据聚合失败: {e}")
            return df

    def _get_from_memory_cache(self, cache_key: str) -> Optional[pd.DataFrame]:
        """从内存缓存获取数据"""
        try:
            if cache_key in self._memory_cache:
                cached_data, timestamp = self._memory_cache[cache_key]

                # 检查缓存是否过期
                cache_timeout = CACHE_CONFIG.get('cache_timeout_seconds', 1800)  # 30分钟
                if time.time() - timestamp < cache_timeout:
                    return cached_data.copy()
                else:
                    # 删除过期缓存
                    del self._memory_cache[cache_key]

            return None

        except Exception as e:
            print(f"⚠️ 内存缓存读取失败: {e}")
            return None

    def _store_in_memory_cache(self, cache_key: str, data: pd.DataFrame):
        """存储数据到内存缓存"""
        try:
            # 限制缓存大小
            max_cache_size = CACHE_CONFIG.get('max_memory_cache_size', 20)
            if len(self._memory_cache) >= max_cache_size:
                # 删除最旧的缓存项
                oldest_key = min(self._memory_cache.keys(),
                                 key=lambda k: self._memory_cache[k][1])
                del self._memory_cache[oldest_key]

            self._memory_cache[cache_key] = (data.copy(), time.time())

        except Exception as e:
            print(f"⚠️ 内存缓存存储失败: {e}")

    def _update_processing_stats(self, processing_time: float, record_count: int):
        """更新处理统计"""
        self._processing_stats['total_processed'] += record_count
        self._processing_stats['total_time'] += processing_time

        if self._processing_stats['total_processed'] > 0:
            self._processing_stats['avg_processing_time'] = (
                    self._processing_stats['total_time'] / self._processing_stats['total_processed'] * 1000
            )  # 每千条记录的处理时间

    def get_cache_hit_rate(self) -> float:
        """获取缓存命中率"""
        if self._cache_stats['total_requests'] > 0:
            return (self._cache_stats['hits'] / self._cache_stats['total_requests']) * 100
        return 0.0

    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return {
            'cache_stats': self._cache_stats.copy(),
            'processing_stats': self._processing_stats.copy(),
            'cache_hit_rate': self.get_cache_hit_rate(),
            'memory_cache_size': len(self._memory_cache)
        }

    def clear_cache(self):
        """清除所有缓存"""
        self._memory_cache.clear()
        self._cache_stats = {
            'hits': 0,
            'misses': 0,
            'total_requests': 0
        }
        print("✅ 缓存已清除")

    def validate_data_quality(self, df: pd.DataFrame) -> Dict[str, Any]:
        """验证数据质量"""
        if df is None or df.empty:
            return {
                'status': 'empty',
                'issues': ['数据为空'],
                'warnings': [],
                'record_count': 0
            }

        try:
            issues = []
            warnings = []

            # 检查必要字段
            required_fields = ['final_app_name', 'involved_amount', 'insert_day']
            for field in required_fields:
                if field not in df.columns:
                    issues.append(f'缺少必要字段: {field}')
                else:
                    null_count = df[field].isnull().sum()
                    if null_count > 0:
                        null_rate = (null_count / len(df)) * 100
                        if null_rate > 20:
                            issues.append(f'{field} 缺失率过高: {null_rate:.1f}%')
                        elif null_rate > 5:
                            warnings.append(f'{field} 缺失率较高: {null_rate:.1f}%')

            # 检查数据类型
            if 'involved_amount' in df.columns:
                invalid_amounts = df['involved_amount'] <= 0
                if invalid_amounts.sum() > 0:
                    warnings.append(f'发现 {invalid_amounts.sum()} 条无效金额记录')

            # 检查时间范围
            if 'insert_day' in df.columns:
                date_range = df['insert_day'].max() - df['insert_day'].min()
                if date_range.days > 365:
                    warnings.append(f'数据时间跨度较大: {date_range.days} 天')

            # 确定状态
            if issues:
                status = 'error'
            elif warnings:
                status = 'warning'
            else:
                status = 'good'

            return {
                'status': status,
                'issues': issues,
                'warnings': warnings,
                'record_count': len(df),
                'column_count': len(df.columns),
                'memory_usage_mb': df.memory_usage(deep=True).sum() / 1024 / 1024
            }

        except Exception as e:
            return {
                'status': 'error',
                'issues': [f'数据质量检查失败: {e}'],
                'warnings': [],
                'record_count': len(df) if df is not None else 0
            }

    def get_data_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """获取数据摘要"""
        if df is None or df.empty:
            return {}

        try:
            summary = {
                'basic_info': {
                    'record_count': len(df),
                    'column_count': len(df.columns),
                    'memory_usage_mb': df.memory_usage(deep=True).sum() / 1024 / 1024
                }
            }

            # 时间范围
            if 'insert_day' in df.columns:
                summary['time_range'] = {
                    'start_date': df['insert_day'].min(),
                    'end_date': df['insert_day'].max(),
                    'days_span': (df['insert_day'].max() - df['insert_day'].min()).days + 1
                }

            # APP统计
            if 'final_app_name' in df.columns:
                summary['app_stats'] = {
                    'unique_apps': df['final_app_name'].nunique(),
                    'top_5_apps': df['final_app_name'].value_counts().head(5).to_dict()
                }

            # 金额统计
            if 'involved_amount' in df.columns:
                amount_data = df['involved_amount'].dropna()
                amount_data = amount_data[amount_data > 0]
                if not amount_data.empty:
                    summary['amount_stats'] = {
                        'total_amount': float(amount_data.sum()),
                        'avg_amount': float(amount_data.mean()),
                        'median_amount': float(amount_data.median()),
                        'max_amount': float(amount_data.max()),
                        'valid_records': len(amount_data)
                    }

            return summary

        except Exception as e:
            print(f"⚠️ 数据摘要生成失败: {e}")
            return {}


# 为兼容性保持原类名
class DataProcessor(OptimizedDataProcessor):
    """数据处理器 - 兼容性包装器"""
    pass