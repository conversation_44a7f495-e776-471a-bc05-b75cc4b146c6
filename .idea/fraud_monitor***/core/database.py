"""
APP涉案监控系统 - 完全修复版数据库模块
修复时间查询一致性问题，确保时间管理器的日期范围与实际查询一致
包含完整的业务逻辑：索引管理、性能监控、缓存机制、分页查询等
"""
import pandas as pd
import time
import threading
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text
from urllib.parse import quote_plus
from concurrent.futures import ThreadPoolExecutor
import json
from pathlib import Path
from config import (
    DATABASE_CONFIG, CONNECTION_POOL_CONFIG,
    DATABASE_INDEXES, QUERY_CONFIG, PERFORMANCE_CONFIG,
    CACHE_CONFIG, INVALID_APP_PATTERNS, APP_NAME_STANDARDIZATION,
    TIME_QUERY_CONFIG
)


class OptimizedDatabaseManager:
    """优化版数据库管理器 - 修复时间查询一致性问题，包含完整业务逻辑"""

    def __init__(self):
        self.engine = None
        self.is_connected = False
        self.query_cache = {}
        self.performance_stats = {
            'total_queries': 0,
            'total_time': 0,
            'cache_hits': 0,
            'slow_queries': 0
        }
        self._lock = threading.Lock()
        self._init_database_engine()

    def _init_database_engine(self):
        """初始化SQLAlchemy数据库引擎"""
        try:
            # URL编码密码中的特殊字符
            password_encoded = quote_plus(DATABASE_CONFIG['password'])

            connection_string = (
                f"mysql+pymysql://{DATABASE_CONFIG['user']}:{password_encoded}@"
                f"{DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}"
                f"?charset={DATABASE_CONFIG['charset']}"
            )

            # 创建引擎with优化的连接池配置
            self.engine = create_engine(
                connection_string,
                **CONNECTION_POOL_CONFIG
            )

            # 测试连接
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))

            print("✅ 优化版数据库引擎初始化成功")
            self.is_connected = True

        except Exception as e:
            print(f"❌ 数据库引擎初始化失败: {e}")
            self.engine = None
            self.is_connected = False

    def connect_database(self):
        """连接数据库"""
        if self.engine is None:
            self._init_database_engine()
        return self.is_connected

    def query_comprehensive_data(self, days=TIME_QUERY_CONFIG['default_days'], use_optimization=True):
        """修复版综合数据查询 - 确保时间管理器的日期范围与实际查询完全一致"""
        if not self.connect_database():
            return None

        # 优先从时间管理器获取精确的日期范围
        try:
            from core.time_query_manager import get_current_datetime_range, get_time_description, \
                get_current_time_config

            # 获取时间配置
            time_config = get_current_time_config()
            start_date = time_config['start_date']
            end_date = time_config['end_date']

            # 转换为datetime，确保时间范围的完整性
            # 关键修复：开始时间为当天的00:00:00，结束时间为当天的23:59:59
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time().replace(microsecond=999999))

            time_desc = get_time_description()
            print(f"🔍 使用时间管理器设定的精确范围: {time_desc}")
            print(
                f"🔍 精确时间范围: {start_datetime.strftime('%Y-%m-%d %H:%M:%S')} 至 {end_datetime.strftime('%Y-%m-%d %H:%M:%S')}")

        except ImportError:
            # 回退到传统的天数计算
            end_datetime = datetime.now().replace(hour=23, minute=59, second=59, microsecond=999999)
            start_datetime = end_datetime - timedelta(days=days - 1)  # 修复：确保包含完整的天数
            start_datetime = start_datetime.replace(hour=0, minute=0, second=0, microsecond=0)
            print(f"🔍 使用传统天数计算: 最近{days}天")
            print(
                f"🔍 计算时间范围: {start_datetime.strftime('%Y-%m-%d %H:%M:%S')} 至 {end_datetime.strftime('%Y-%m-%d %H:%M:%S')}")

        # 生成缓存键
        cache_key = f"comprehensive_data_{start_datetime.date()}_{end_datetime.date()}_{use_optimization}"

        # 检查缓存
        if CACHE_CONFIG['enable_intelligent_cache']:
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                with self._lock:
                    self.performance_stats['cache_hits'] += 1
                print("📦 使用缓存数据")
                return cached_result

        try:
            start_time = time.time()

            with self._lock:
                self.performance_stats['total_queries'] += 1

            if use_optimization:
                df = self._query_optimized_comprehensive_data_with_datetime(start_datetime, end_datetime)
            else:
                df = self._query_legacy_comprehensive_data_with_datetime(start_datetime, end_datetime)

            query_time = time.time() - start_time

            # 更新性能统计
            self._update_performance_stats(query_time, cache_hit=False)

            # 数据后处理
            if df is not None and not df.empty:
                df = self._post_process_data(df)

                # 缓存结果
                if CACHE_CONFIG['enable_intelligent_cache']:
                    self._store_in_cache(cache_key, df)

                # 验证数据范围
                self._verify_data_range(df, start_datetime, end_datetime)

            print(f"✅ 优化查询完成，获取 {len(df) if df is not None else 0} 条记录")
            return df

        except Exception as e:
            print(f"❌ 数据查询失败: {e}")
            return None

    def _verify_data_range(self, df: pd.DataFrame, expected_start: datetime, expected_end: datetime):
        """验证查询结果的时间范围是否符合预期 - 简化版本"""
        if df is None or df.empty or 'insert_day' not in df.columns:
            return

        try:
            actual_start = df['insert_day'].min()
            actual_end = df['insert_day'].max()

            # 只在存在明显问题时才显示警告
            expected_start_date = expected_start.date()
            expected_end_date = expected_end.date()
            actual_start_date = actual_start.date()
            actual_end_date = actual_end.date()

            # 只显示严重的数据遗漏警告
            if actual_start_date > expected_start_date:
                days_missing = (actual_start_date - expected_start_date).days
                if days_missing > 1:  # 只有遗漏超过1天才警告
                    print(f"⚠️ 数据起始日期晚于预期 {days_missing} 天")

        except Exception as e:
            # 静默处理验证错误
            pass

    def _query_optimized_comprehensive_data_with_datetime(self, start_datetime, end_datetime):
        """使用datetime范围的优化查询 - 简单有效的修复：直接使用DATE查询"""

        # 简单修复：直接使用已验证有效的DATE BETWEEN查询
        start_date = start_datetime.date().strftime('%Y-%m-%d')
        end_date = end_datetime.date().strftime('%Y-%m-%d')

        optimized_query = text("""
            SELECT 
                case_number,
                app_name as final_app_name,
                involved_amount,
                insert_day,
                occurrence_area,
                case_main_type,
                case_sub_type,
                victim_age,
                brief_case_description,
                transfer_method,
                suspect_phone_number,
                suspect_account_number,
                occurrence_time
            FROM anti_fraud_case_new
            WHERE DATE(insert_day) BETWEEN :start_date AND :end_date
                AND app_name IS NOT NULL
                AND app_name != ''
                AND app_name != 'NULL'
                AND involved_amount > 0
            ORDER BY insert_day DESC, case_number
        """)

        with self.engine.connect() as conn:
            result = conn.execute(optimized_query, {
                'start_date': start_date,
                'end_date': end_date
            })

            # 批量获取数据
            rows = result.fetchall()
            columns = result.keys()
            df = pd.DataFrame(rows, columns=columns)

        return df

    def _query_legacy_comprehensive_data_with_datetime(self, start_datetime, end_datetime):
        """使用datetime范围的传统查询 - 保持已验证有效的方法"""

        start_date = start_datetime.date().strftime('%Y-%m-%d')
        end_date = end_datetime.date().strftime('%Y-%m-%d')

        legacy_query = text("""
            SELECT 
                case_number, 
                app_name as final_app_name,
                involved_amount, 
                insert_day, 
                occurrence_area,
                case_main_type, 
                case_sub_type, 
                victim_age,
                brief_case_description,
                transfer_method,
                suspect_phone_number,
                suspect_account_number,
                occurrence_time
            FROM anti_fraud_case_new
            WHERE DATE(insert_day) BETWEEN :start_date AND :end_date
                AND app_name IS NOT NULL
                AND app_name != ''
                AND involved_amount > 0
            ORDER BY insert_day DESC
        """)

        with self.engine.connect() as conn:
            result = conn.execute(legacy_query, {
                'start_date': start_date,
                'end_date': end_date
            })

            rows = result.fetchall()
            columns = result.keys()
            df = pd.DataFrame(rows, columns=columns)

        return df

    def query_data_by_date_range(self, start_date, end_date):
        """根据日期范围查询数据 - 新增方法，专门用于对比分析"""
        if not self.connect_database():
            return None

        try:
            # 转换为完整的datetime范围
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time().replace(microsecond=999999))

            # print(f"🔍 按日期范围查询: {start_date} 至 {end_date}")

            query = text("""
                SELECT 
                    case_number,
                    app_name as final_app_name,
                    involved_amount,
                    insert_day,
                    occurrence_area,
                    case_main_type,
                    case_sub_type,
                    victim_age
                FROM anti_fraud_case_new
                WHERE insert_day >= :start_datetime 
                    AND insert_day <= :end_datetime
                    AND app_name IS NOT NULL
                    AND app_name != ''
                    AND involved_amount > 0
                ORDER BY insert_day DESC
            """)

            with self.engine.connect() as conn:
                result = conn.execute(query, {
                    'start_datetime': start_datetime,
                    'end_datetime': end_datetime
                })

                rows = result.fetchall()
                columns = result.keys()
                df = pd.DataFrame(rows, columns=columns)

            if df is not None and not df.empty:
                df = self._post_process_data(df)
                # print(f"✅ 获取 {len(df)} 条记录")
            else:
                print("⚠️ 该时间范围内无数据")

            return df

        except Exception as e:
            print(f"❌ 按日期范围查询失败: {e}")
            return None

    def query_app_first_appearance(self, app_names=None):
        """查询APP首次发现时间 - 从anti_app_name表，修复字段名问题"""
        if not self.connect_database():
            return {}

        try:
            # 构建查询条件
            if app_names and len(app_names) > 0:
                # 为IN查询构建占位符
                placeholders = ','.join([f':app_{i}' for i in range(len(app_names))])
                query = text(f"""
                    SELECT app_name, MIN(create_time) as first_appearance
                    FROM anti_app_name 
                    WHERE app_name IN ({placeholders})
                    GROUP BY app_name
                """)

                # 构建参数字典
                params = {f'app_{i}': app_name for i, app_name in enumerate(app_names)}
            else:
                query = text("""
                    SELECT app_name, MIN(create_time) as first_appearance
                    FROM anti_app_name 
                    GROUP BY app_name
                """)
                params = {}

            with self.engine.connect() as conn:
                result = conn.execute(query, params)
                rows = result.fetchall()

                app_first_times = {}
                for row in rows:
                    app_first_times[row[0]] = row[1]

                print(f"✅ 查询到 {len(app_first_times)} 个APP的首次发现时间")
                return app_first_times

        except Exception as e:
            print(f"⚠️ 查询APP首次发现时间失败: {e}")
            return {}

    def query_comprehensive_data_with_pagination(self, start_datetime, end_datetime, page_size=10000):
        """分页查询综合数据 - 处理大数据集"""
        if not self.connect_database():
            return None

        try:
            all_data = []
            offset = 0
            total_fetched = 0

            print(f"📄 开始分页查询...")

            while True:
                query = text("""
                    SELECT 
                        case_number,
                        app_name as final_app_name,
                        involved_amount,
                        insert_day,
                        occurrence_area,
                        case_main_type,
                        case_sub_type,
                        victim_age,
                        brief_case_description,
                        transfer_method,
                        suspect_phone_number,
                        suspect_account_number,
                        occurrence_time
                    FROM anti_fraud_case_new
                    WHERE insert_day >= :start_datetime 
                        AND insert_day <= :end_datetime
                        AND app_name IS NOT NULL
                        AND app_name != ''
                        AND involved_amount > 0
                    ORDER BY insert_day DESC, case_number
                    LIMIT :page_size OFFSET :offset
                """)

                with self.engine.connect() as conn:
                    result = conn.execute(query, {
                        'start_datetime': start_datetime,
                        'end_datetime': end_datetime,
                        'page_size': page_size,
                        'offset': offset
                    })

                    rows = result.fetchall()
                    if not rows:
                        break

                    columns = result.keys()
                    df_page = pd.DataFrame(rows, columns=columns)
                    all_data.append(df_page)

                    total_fetched += len(rows)
                    offset += page_size
                    print(f"📄 已获取 {total_fetched} 条记录...")

                    if len(rows) < page_size:
                        break

            if all_data:
                final_df = pd.concat(all_data, ignore_index=True)
                final_df = self._post_process_data(final_df)
                print(f"✅ 智能分页查询完成，总计 {len(final_df)} 条记录")
                return final_df
            else:
                print("⚠️ 分页查询无结果")
                return pd.DataFrame()

        except Exception as e:
            print(f"❌ 分页查询失败: {e}")
            return None

    def _get_from_cache(self, cache_key):
        """从缓存获取数据"""
        try:
            if cache_key in self.query_cache:
                cache_entry = self.query_cache[cache_key]
                cache_time = cache_entry['timestamp']

                # 检查缓存是否过期
                if (datetime.now() - cache_time).total_seconds() < CACHE_CONFIG['cache_expire_hours'] * 3600:
                    with self._lock:
                        self.performance_stats['cache_hits'] += 1
                    return cache_entry['data']
                else:
                    # 清理过期缓存
                    del self.query_cache[cache_key]

            return None

        except Exception as e:
            print(f"⚠️ 缓存读取警告: {e}")
            return None

    def _store_in_cache(self, cache_key, data):
        """存储数据到缓存"""
        try:
            # 限制缓存大小
            if len(self.query_cache) >= CACHE_CONFIG['memory_cache_limit']:
                # 清理最旧的缓存项
                oldest_key = min(self.query_cache.keys(),
                                 key=lambda k: self.query_cache[k]['timestamp'])
                del self.query_cache[oldest_key]

            self.query_cache[cache_key] = {
                'data': data.copy() if data is not None else None,
                'timestamp': datetime.now()
            }

        except Exception as e:
            print(f"⚠️ 缓存存储警告: {e}")

    def clear_cache(self):
        """清理查询缓存"""
        with self._lock:
            self.query_cache.clear()
            print("🗑️ 查询缓存已清理")

    def _update_performance_stats(self, query_time, cache_hit=False):
        """更新性能统计"""
        with self._lock:
            self.performance_stats['total_time'] += query_time

            if cache_hit:
                self.performance_stats['cache_hits'] += 1

            if query_time > PERFORMANCE_CONFIG.get('slow_query_threshold', 2.0):
                self.performance_stats['slow_queries'] += 1

    def get_performance_summary(self):
        """获取性能摘要"""
        with self._lock:
            stats = self.performance_stats.copy()

        if stats['total_queries'] > 0:
            stats['average_query_time'] = stats['total_time'] / stats['total_queries']
            stats['cache_hit_rate'] = (stats['cache_hits'] / stats['total_queries']) * 100
        else:
            stats['average_query_time'] = 0
            stats['cache_hit_rate'] = 0

        return stats

    # ================================
    # 性能优化的索引管理功能
    # ================================
    def create_optimized_database_indexes(self):
        """创建优化版数据库索引"""
        if not self.connect_database():
            print("❌ 无法连接数据库，索引创建失败")
            return False

        try:
            success_count = 0
            total_count = len(DATABASE_INDEXES)

            print(f"🚀 开始创建 {total_count} 个优化索引...")

            with self.engine.connect() as conn:
                for i, (index_name, table_name, columns) in enumerate(DATABASE_INDEXES, 1):
                    try:
                        print(f"📊 处理索引 {i}/{total_count}: {index_name}...")

                        # 检查索引是否已存在
                        check_sql = text("""
                            SELECT COUNT(*) as count 
                            FROM information_schema.statistics 
                            WHERE table_schema = DATABASE() 
                            AND table_name = :table_name 
                            AND index_name = :index_name
                        """)

                        result = conn.execute(check_sql, {
                            'table_name': table_name,
                            'index_name': index_name
                        })

                        index_exists = result.fetchone()[0] > 0

                        if index_exists:
                            print(f"   ✅ 索引 {index_name} 已存在，跳过")
                            success_count += 1
                        else:
                            # 创建索引
                            create_sql = text(f"CREATE INDEX {index_name} ON {table_name}({columns})")
                            conn.execute(create_sql)
                            conn.commit()
                            print(f"   ✅ 索引 {index_name} 创建成功")
                            success_count += 1

                    except Exception as e:
                        print(f"   ❌ 索引 {index_name} 创建失败: {e}")
                        continue

                # 更新表统计信息
                print("📊 更新表统计信息...")
                conn.execute(text("ANALYZE TABLE anti_fraud_case_new"))
                # 如果anti_app_name表存在，也更新其统计信息
                try:
                    conn.execute(text("ANALYZE TABLE anti_app_name"))
                except:
                    pass
                conn.commit()

            print(f"✅ 索引优化完成: 成功 {success_count}/{total_count}")
            return success_count == total_count

        except Exception as e:
            print(f"❌ 索引创建失败: {e}")
            return False

    def check_index_effectiveness(self):
        """检查索引有效性"""
        if not self.connect_database():
            return None

        try:
            with self.engine.connect() as conn:
                # 检查索引使用情况
                index_usage_sql = text("""
                    SELECT 
                        s.table_name,
                        s.index_name,
                        s.column_name,
                        s.cardinality,
                        s.nullable,
                        CASE 
                            WHEN s.cardinality > 1000 THEN '高选择性'
                            WHEN s.cardinality > 100 THEN '中选择性'
                            ELSE '低选择性'
                        END as selectivity
                    FROM information_schema.statistics s
                    WHERE s.table_schema = DATABASE()
                        AND s.table_name IN ('anti_fraud_case_new', 'anti_app_name')
                        AND s.index_name != 'PRIMARY'
                    ORDER BY s.table_name, s.cardinality DESC
                """)

                result = conn.execute(index_usage_sql)
                index_info = result.fetchall()

                print("📊 索引有效性分析:")
                print("-" * 100)
                print(f"{'表名':<20} {'索引名称':<30} {'字段名称':<20} {'基数':<10} {'选择性':<10}")
                print("-" * 100)

                for row in index_info:
                    print(f"{row[0]:<20} {row[1]:<30} {row[2]:<20} {row[3]:<10} {row[5]:<10}")

                return index_info

        except Exception as e:
            print(f"❌ 索引分析失败: {e}")
            return None

    # ================================
    # 性能监控功能
    # ================================
    def check_optimized_query_performance(self):
        """检查优化后的查询性能"""
        if not self.connect_database():
            return None

        test_queries = [
            {
                'name': '优化时间范围查询',
                'sql': f"""
                    SELECT COUNT(*) 
                    FROM anti_fraud_case_new 
                    WHERE insert_day >= DATE_SUB(NOW(), INTERVAL {TIME_QUERY_CONFIG['default_days']} DAY)
                        AND insert_day <= NOW()
                        AND involved_amount > 0
                """
            },
            {
                'name': '复合索引APP统计',
                'sql': f"""
                    SELECT app_name, COUNT(*) as cnt, AVG(involved_amount) as avg_amount
                    FROM anti_fraud_case_new 
                    WHERE insert_day >= DATE_SUB(NOW(), INTERVAL {TIME_QUERY_CONFIG['default_days']} DAY)
                        AND app_name IS NOT NULL 
                        AND app_name != ''
                        AND involved_amount > 0
                    GROUP BY app_name 
                    ORDER BY cnt DESC 
                    LIMIT 20
                """
            },
            {
                'name': 'APP首次发现时间查询',
                'sql': """
                    SELECT app_name, MIN(create_time) as first_appearance
                    FROM anti_app_name
                    GROUP BY app_name
                    ORDER BY first_appearance DESC
                    LIMIT 50
                """
            },
            {
                'name': '覆盖索引查询',
                'sql': f"""
                    SELECT app_name, occurrence_area, COUNT(*), AVG(involved_amount)
                    FROM anti_fraud_case_new 
                    WHERE insert_day >= DATE_SUB(NOW(), INTERVAL {TIME_QUERY_CONFIG['default_days']} DAY)
                        AND involved_amount > 0
                    GROUP BY app_name, occurrence_area
                    HAVING COUNT(*) >= 5
                    ORDER BY COUNT(*) DESC
                    LIMIT 10
                """
            }
        ]

        results = []
        print("🔧 开始性能测试...")

        with self.engine.connect() as conn:
            for i, test in enumerate(test_queries, 1):
                try:
                    print(f"   测试 {i}/{len(test_queries)}: {test['name']}")

                    start_time = time.time()
                    result = conn.execute(text(test['sql']))
                    rows = result.fetchall()
                    execution_time = time.time() - start_time

                    results.append({
                        'name': test['name'],
                        'execution_time': execution_time,
                        'row_count': len(rows),
                        'status': '优秀' if execution_time < 1 else '良好' if execution_time < 3 else '需优化'
                    })

                    print(f"     耗时: {execution_time:.3f}秒, 结果: {len(rows)} 行")

                except Exception as e:
                    print(f"     ❌ 测试失败: {e}")
                    results.append({
                        'name': test['name'],
                        'execution_time': 999.999,
                        'row_count': 0,
                        'status': '失败'
                    })

        return results

    def get_optimized_table_info(self):
        """获取优化的表信息"""
        if not self.connect_database():
            return None

        try:
            with self.engine.connect() as conn:
                # 获取表大小和行数信息
                table_info_sql = text("""
                    SELECT 
                        table_name,
                        ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'size_mb',
                        ROUND((index_length / 1024 / 1024), 2) AS 'index_size_mb',
                        table_rows as 'estimated_rows'
                    FROM information_schema.tables 
                    WHERE table_schema = DATABASE()
                        AND table_name IN ('anti_fraud_case_new', 'anti_app_name')
                    ORDER BY (data_length + index_length) DESC
                """)

                result = conn.execute(table_info_sql)
                rows = result.fetchall()

                if rows:
                    print("\n📊 数据库表信息:")
                    print("-" * 80)
                    print(f"{'表名':<20} {'数据大小(MB)':<12} {'索引大小(MB)':<15} {'估计行数':<12}")
                    print("-" * 80)

                    total_size = 0
                    total_index_size = 0

                    for row in rows:
                        total_size += row[1]
                        total_index_size += row[2]
                        print(f"{row[0]:<20} {row[1]:<12.2f} {row[2]:<15.2f} {row[3]:<12,}")

                    print("-" * 80)
                    print(f"{'总计':<20} {total_size:<12.2f} {total_index_size:<15.2f}")

                    # 性能建议
                    if total_size > 2000:  # 2GB
                        print("   🔴 数据库较大，建议考虑分区策略")
                    elif total_size > 500:  # 500MB
                        print("   🟡 数据库中等大小，注意索引优化")
                    else:
                        print("   🟢 数据库大小适中")

                    if total_index_size / max(total_size, 1) > 0.5:  # 索引大小超过数据大小50%
                        print("   🟡 索引比例较高，可能存在冗余索引")

                    return {
                        'total_size_mb': total_size,
                        'total_index_size_mb': total_index_size,
                        'tables': [{'name': row[0], 'size_mb': row[1], 'rows': row[3]} for row in rows]
                    }

        except Exception as e:
            print(f"⚠️ 无法获取表信息: {e}")
            return None

    def _post_process_data(self, df):
        """数据后处理 - 类型转换、过滤、标准化"""
        try:
            if df is None or df.empty:
                return df

            # 快速数据类型转换
            df = self._convert_data_types(df)

            # APP名称过滤和标准化
            if 'final_app_name' in df.columns:
                df = self._filter_and_standardize_app_names(df)

            return df

        except Exception as e:
            print(f"⚠️ 数据后处理警告: {e}")
            return df

    def _convert_data_types(self, df):
        """快速数据类型转换"""
        try:
            # 日期转换
            date_columns = ['insert_day', 'occurrence_time']
            for col in date_columns:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col], errors='coerce')

            # 数值转换
            numeric_columns = ['involved_amount', 'victim_age']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            return df

        except Exception as e:
            print(f"⚠️ 数据类型转换失败: {e}")
            return df

    def _filter_and_standardize_app_names(self, df):
        """过滤无效APP名称并标准化"""
        try:
            original_count = len(df)

            # 过滤无效模式
            for pattern in INVALID_APP_PATTERNS:
                df = df[~df['final_app_name'].str.contains(pattern, case=False, na=False)]

            # 标准化APP名称
            for old_name, new_name in APP_NAME_STANDARDIZATION.items():
                df.loc[df['final_app_name'].str.lower() == old_name.lower(), 'final_app_name'] = new_name

            filtered_count = len(df)
            if original_count != filtered_count:
                print(f"🔄 过滤无效APP: {original_count} -> {filtered_count} 条记录")

            return df

        except Exception as e:
            print(f"⚠️ APP名称处理失败: {e}")
            return df

    # ================================
    # 数据库状态检查功能
    # ================================
    def check_database_status(self):
        """检查数据库状态"""
        if not self.connect_database():
            return {
                'status': 'disconnected',
                'connection': False,
                'tables': [],
                'performance': {}
            }

        try:
            with self.engine.connect() as conn:
                # 检查基本连接
                conn.execute(text("SELECT 1"))

                # 检查表是否存在
                tables_check = text("""
                    SELECT table_name, table_rows, 
                           ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                    FROM information_schema.tables 
                    WHERE table_schema = DATABASE()
                        AND table_name IN ('anti_fraud_case_new', 'anti_app_name')
                """)

                result = conn.execute(tables_check)
                tables_info = result.fetchall()

                # 检查基本性能指标
                performance_check = text("SHOW GLOBAL STATUS LIKE 'Queries'")
                perf_result = conn.execute(performance_check)
                perf_info = perf_result.fetchall()

                return {
                    'status': 'connected',
                    'connection': True,
                    'tables': [{'name': row[0], 'rows': row[1], 'size_mb': row[2]} for row in tables_info],
                    'performance': dict(perf_info) if perf_info else {},
                    'cache_stats': self.get_performance_summary()
                }

        except Exception as e:
            return {
                'status': 'error',
                'connection': False,
                'error': str(e),
                'tables': [],
                'performance': {}
            }

    # ================================
    # 数据导入导出功能
    # ================================
    def export_data_to_csv(self, df: pd.DataFrame, filename: str):
        """导出数据到CSV文件"""
        try:
            if df is None or df.empty:
                print("⚠️ 无数据可导出")
                return False

            filepath = Path(filename)
            filepath.parent.mkdir(parents=True, exist_ok=True)

            df.to_csv(filepath, index=False, encoding='utf-8-sig')
            print(f"✅ 数据已导出到: {filepath}")
            print(f"   导出记录数: {len(df)}")
            return True

        except Exception as e:
            print(f"❌ 数据导出失败: {e}")
            return False

    def import_data_from_csv(self, filepath: str, table_name: str = 'anti_fraud_case_new'):
        """从CSV文件导入数据"""
        try:
            if not Path(filepath).exists():
                print(f"❌ 文件不存在: {filepath}")
                return False

            df = pd.read_csv(filepath, encoding='utf-8-sig')
            print(f"📥 读取CSV文件: {len(df)} 条记录")

            # 数据预处理
            df = self._post_process_data(df)

            # 批量插入数据库
            with self.engine.connect() as conn:
                df.to_sql(table_name, conn, if_exists='append', index=False, method='multi')
                conn.commit()

            print(f"✅ 数据导入完成: {len(df)} 条记录")
            return True

        except Exception as e:
            print(f"❌ 数据导入失败: {e}")
            return False

    # ================================
    # 备份和恢复功能
    # ================================
    def backup_table(self, table_name: str, backup_path: str):
        """备份表数据"""
        try:
            if not self.connect_database():
                return False

            print(f"🗄️ 开始备份表: {table_name}")

            # 查询所有数据
            query = text(f"SELECT * FROM {table_name}")

            with self.engine.connect() as conn:
                df = pd.read_sql(query, conn)

            if df.empty:
                print("⚠️ 表为空，无数据备份")
                return False

            # 生成备份文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = f"{backup_path}/{table_name}_backup_{timestamp}.csv"

            # 导出备份
            success = self.export_data_to_csv(df, backup_file)

            if success:
                print(f"✅ 表备份完成: {backup_file}")
                return True
            else:
                print("❌ 表备份失败")
                return False

        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return False

    # ================================
    # 数据库维护功能
    # ================================
    def optimize_tables(self):
        """优化表结构"""
        if not self.connect_database():
            return False

        try:
            with self.engine.connect() as conn:
                # 优化主表
                print("🔧 优化 anti_fraud_case_new 表...")
                conn.execute(text("OPTIMIZE TABLE anti_fraud_case_new"))

                # 优化APP表（如果存在）
                try:
                    print("🔧 优化 anti_app_name 表...")
                    conn.execute(text("OPTIMIZE TABLE anti_app_name"))
                except:
                    print("⚠️ anti_app_name 表不存在或无法优化")

                conn.commit()
                print("✅ 表优化完成")
                return True

        except Exception as e:
            print(f"❌ 表优化失败: {e}")
            return False

    def rebuild_indexes(self):
        """重建索引"""
        if not self.connect_database():
            return False

        try:
            with self.engine.connect() as conn:
                # 获取所有非主键索引
                indexes_query = text("""
                    SELECT DISTINCT index_name, table_name
                    FROM information_schema.statistics 
                    WHERE table_schema = DATABASE()
                        AND table_name IN ('anti_fraud_case_new', 'anti_app_name')
                        AND index_name != 'PRIMARY'
                """)

                result = conn.execute(indexes_query)
                indexes = result.fetchall()

                print(f"🔧 开始重建 {len(indexes)} 个索引...")

                for index_name, table_name in indexes:
                    try:
                        # 删除索引
                        conn.execute(text(f"DROP INDEX {index_name} ON {table_name}"))
                        print(f"   删除索引: {index_name}")
                    except:
                        continue

                conn.commit()

                # 重新创建优化索引
                self.create_optimized_database_indexes()

                print("✅ 索引重建完成")
                return True

        except Exception as e:
            print(f"❌ 索引重建失败: {e}")
            return False

    def close_connection(self):
        """关闭数据库连接"""
        try:
            if self.engine:
                # 打印性能摘要
                if self.performance_stats['total_queries'] > 0:
                    summary = self.get_performance_summary()
                    print(f"📊 数据库性能摘要:")
                    print(f"   总查询数: {summary['total_queries']}")
                    print(f"   平均查询时间: {summary['average_query_time']:.3f}秒")
                    print(f"   缓存命中率: {summary['cache_hit_rate']:.1f}%")
                    print(f"   慢查询数: {summary['slow_queries']}")

                self.engine.dispose()
                self.is_connected = False
                print("🔌 优化版数据库连接池已关闭")
        except Exception as e:
            print(f"⚠️ 关闭数据库连接失败: {e}")


# 为了兼容性，保持原有类名
class DatabaseManager(OptimizedDatabaseManager):
    """数据库管理器 - 兼容性包装器"""

    def query_comprehensive_data(self, days=TIME_QUERY_CONFIG['default_days'], use_cache=True, **kwargs):
        """兼容原有接口 - 修复版本"""
        # 处理不同的参数调用方式
        if 'use_optimization' in kwargs:
            # 新版本调用
            return super().query_comprehensive_data(days, use_optimization=kwargs['use_optimization'])
        else:
            # 兼容版本调用，将 use_cache 映射为 use_optimization
            return super().query_comprehensive_data(days, use_optimization=use_cache)

    def create_database_indexes(self):
        """兼容原有接口"""
        return self.create_optimized_database_indexes()

    def check_query_performance(self):
        """兼容原有接口"""
        return self.check_optimized_query_performance()

    def get_table_info(self):
        """兼容原有接口"""
        return self.get_optimized_table_info()