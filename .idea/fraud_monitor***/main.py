"""
APP涉案监控系统 - 优化版程序入口
适配新菜单系统，移除快速分析，简化命令行参数
"""
import argparse
import sys
from datetime import datetime
from pathlib import Path
from monitor import FraudMonitor
from config import QUERY_CONFIG, DEBUG_CONFIG, SYSTEM_CONSTANTS

def create_argument_parser():
    """创建简化版命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description=f'{SYSTEM_CONSTANTS["system_name"]} v{SYSTEM_CONSTANTS["version"]} - 智能检测诈骗APP和可疑模式',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                          # 启动交互式菜单 (推荐)
  python main.py -i                       # 启动交互式菜单
  python main.py -d 7                     # 分析最近7天数据并生成报告
  python main.py -d 30 --no-cache         # 不使用缓存
  python main.py --status                 # 检查系统状态
  python main.py --performance            # 检查数据库性能
  python main.py --create-indexes         # 创建优化索引
        """
    )

    parser.add_argument(
        '--interactive', '-i',
        action='store_true',
        help='启动交互式菜单模式 (默认模式)'
    )

    parser.add_argument(
        '-d', '--days',
        type=int,
        default=QUERY_CONFIG['default_days'],
        help=f'分析天数 (默认: {QUERY_CONFIG["default_days"]}天)'
    )

    # 移除快速分析相关参数
    # parser.add_argument('--quick', ...) # 用户认为无意义，已移除

    parser.add_argument(
        '--no-cache',
        action='store_true',
        help='不使用缓存，强制从数据库查询'
    )

    parser.add_argument(
        '--status',
        action='store_true',
        help='检查系统状态'
    )

    parser.add_argument(
        '--performance',
        action='store_true',
        help='检查数据库性能'
    )

    parser.add_argument(
        '--create-indexes',
        action='store_true',
        help='创建数据库优化索引'
    )

    parser.add_argument(
        '--clear-cache',
        action='store_true',
        help='清理所有缓存'
    )

    parser.add_argument(
        '--debug',
        action='store_true',
        help='启用调试模式'
    )

    parser.add_argument(
        '--version', '-v',
        action='version',
        version=f'{SYSTEM_CONSTANTS["system_name"]} v{SYSTEM_CONSTANTS["version"]}'
    )

    return parser


def main():
    """主程序入口"""
    # 检查是否有命令行参数
    if len(sys.argv) == 1:
        # 没有任何参数时，直接启动交互式菜单
        print(f"🚀 启动 {SYSTEM_CONSTANTS['system_name']} v{SYSTEM_CONSTANTS['version']}")
        handle_interactive_mode()
        return

    # 解析命令行参数
    parser = create_argument_parser()
    args = parser.parse_args()

    # 设置调试模式
    if args.debug:
        DEBUG_CONFIG['enable_debug'] = True
        DEBUG_CONFIG['verbose_output'] = True
        print("🐛 调试模式已启用")

    # 初始化监控系统
    monitor = None
    try:
        monitor = FraudMonitor()

        # 根据参数执行不同操作
        if args.interactive:
            handle_interactive_mode()
        elif args.status:
            handle_status_check(monitor)
        elif args.performance:
            handle_performance_check(monitor)
        elif args.create_indexes:
            handle_create_indexes(monitor)
        elif args.clear_cache:
            handle_clear_cache(monitor)
        else:
            # 默认执行完整分析
            handle_comprehensive_analysis(monitor, args)

    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 系统运行错误: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        sys.exit(1)
    finally:
        # 清理资源
        if monitor:
            monitor.close()


def handle_interactive_mode():
    """处理交互式模式 - 使用新菜单系统"""
    try:
        # 导入新的交互式菜单
        from interactive_menu import FinalInteractiveMenu
        menu = FinalInteractiveMenu()
        menu.start()
    except ImportError:
        # 如果新菜单不可用，回退到旧版本
        print("⚠️ 新菜单系统不可用，使用兼容模式")
        try:
            from interactive_menu import InteractiveMenu
            menu = InteractiveMenu()
            menu.start()
        except ImportError:
            print("❌ 交互式菜单系统不可用")
            sys.exit(1)


def handle_status_check(monitor):
    """处理系统状态检查 - 增强版"""
    print("🔍 检查系统状态...")
    status = monitor.get_system_status()

    print(f"\n📋 {SYSTEM_CONSTANTS['system_name']} 状态报告")
    print("=" * 60)
    print(f"   系统版本: v{SYSTEM_CONSTANTS['version']}")
    print(f"   检查时间: {datetime.now().strftime(SYSTEM_CONSTANTS['datetime_format'])}")
    print(f"   数据库连接: {'✅ 正常' if status['database_connected'] else '❌ 异常'}")

    print(f"\n🔧 组件状态:")
    for component, state in status['components'].items():
        status_icon = "✅" if state == "ready" else "❌"
        component_name = component.replace('_', ' ').title()
        print(f"   {component_name}: {status_icon} {state}")

    # 数据库信息
    if 'database_info' in status:
        db_info = status['database_info']
        print(f"\n💾 数据库信息:")
        print(f"   表大小: {db_info.get('table_size_mb', 0):.1f} MB")
        if 'index_size_mb' in db_info:
            print(f"   索引大小: {db_info.get('index_size_mb', 0):.1f} MB")
        print(f"   估计行数: {db_info.get('estimated_rows', 0):,}")

    # 性能统计
    if 'performance' in status:
        perf = status['performance']
        print(f"\n📈 性能统计:")
        print(f"   总查询数: {perf.get('total_queries', 0)}")
        print(f"   平均查询时间: {perf.get('average_query_time', 0):.3f}秒")
        print(f"   缓存命中率: {perf.get('cache_hit_rate', 0):.1f}%")
        if perf.get('slow_queries', 0) > 0:
            print(f"   慢查询数: {perf.get('slow_queries', 0)} (>5秒)")

    # 健康评估
    health_score = _calculate_system_health(status)
    health_status = "优秀" if health_score >= 90 else "良好" if health_score >= 70 else "需优化"
    health_emoji = "🟢" if health_score >= 90 else "🟡" if health_score >= 70 else "🔴"

    print(f"\n🎯 系统健康度: {health_emoji} {health_status} ({health_score:.0f}分)")


def _calculate_system_health(status):
    """计算系统健康度评分"""
    score = 0

    # 数据库连接 (30分)
    if status.get('database_connected', False):
        score += 30

    # 组件状态 (40分)
    components = status.get('components', {})
    ready_components = sum(1 for state in components.values() if state == 'ready')
    total_components = len(components)
    if total_components > 0:
        score += (ready_components / total_components) * 40

    # 性能指标 (30分)
    if 'performance' in status:
        perf = status['performance']
        cache_hit_rate = perf.get('cache_hit_rate', 0)
        avg_query_time = perf.get('average_query_time', 0)

        # 缓存命中率评分 (15分)
        if cache_hit_rate >= 80:
            score += 15
        elif cache_hit_rate >= 60:
            score += 10
        elif cache_hit_rate >= 40:
            score += 5

        # 查询性能评分 (15分)
        if avg_query_time <= 1:
            score += 15
        elif avg_query_time <= 3:
            score += 10
        elif avg_query_time <= 5:
            score += 5

    return min(score, 100)


def handle_performance_check(monitor):
    """处理性能检查 - 增强版"""
    print("⚡ 检查数据库性能...")

    # 检查查询性能
    print("\n🔍 执行性能测试...")
    performance_results = monitor.db_manager.check_optimized_query_performance()

    if performance_results:
        print("\n📊 性能测试摘要:")
        total_time = sum(result['execution_time'] for result in performance_results)
        avg_time = total_time / len(performance_results)

        excellent_queries = len([r for r in performance_results if r['execution_time'] < 1])
        good_queries = len([r for r in performance_results if 1 <= r['execution_time'] < 3])
        slow_queries = len([r for r in performance_results if r['execution_time'] >= 5])

        print(f"   平均查询时间: {avg_time:.3f}秒")
        print(f"   优秀查询(<1秒): {excellent_queries}/{len(performance_results)}")
        print(f"   良好查询(1-3秒): {good_queries}/{len(performance_results)}")
        print(f"   慢查询(≥5秒): {slow_queries}/{len(performance_results)}")

        if slow_queries > 0:
            print("💡 建议: 发现慢查询，建议创建优化索引")

    # 检查索引有效性
    print("\n📊 检查索引有效性...")
    monitor.db_manager.check_index_effectiveness()

    # 检查表信息
    print("\n💾 检查表信息...")
    monitor.db_manager.get_optimized_table_info()


def handle_create_indexes(monitor):
    """处理索引创建 - 增强版"""
    print("📊 创建数据库优化索引...")
    print("⚠️ 注意: 索引创建可能需要几分钟时间，请耐心等待")

    start_time = datetime.now()
    success = monitor.db_manager.create_optimized_database_indexes()
    end_time = datetime.now()

    execution_time = (end_time - start_time).total_seconds()

    if success:
        print(f"✅ 索引创建完成，耗时 {execution_time:.1f}秒")
        print("💡 建议: 重新启动应用以获得最佳性能")
    else:
        print(f"❌ 索引创建失败，耗时 {execution_time:.1f}秒")
        print("💡 建议: 检查数据库权限或联系管理员")


def handle_clear_cache(monitor):
    """处理缓存清理 - 增强版"""
    print("🗑️ 清理系统缓存...")

    # 清理数据处理器缓存
    if hasattr(monitor.data_processor, 'clear_cache'):
        monitor.data_processor.clear_cache()
        print("   ✅ 数据处理器缓存已清理")

    # 清理数据库查询缓存
    if hasattr(monitor.db_manager, 'clear_cache'):
        monitor.db_manager.clear_cache()
        print("   ✅ 数据库查询缓存已清理")

    print("✅ 所有缓存清理完成")


def handle_comprehensive_analysis(monitor, args):
    """处理完整分析 - 简化版"""
    use_cache = not args.no_cache
    days = args.days

    print(f"🚀 启动完整分析")
    print(f"📅 分析周期: {days}天 | 💾 使用缓存: {'是' if use_cache else '否'}")
    print("=" * 60)

    start_time = datetime.now()
    results = monitor.run_comprehensive_analysis(
        days=days,
        use_cache=use_cache,
        generate_report=True  # 默认生成报告
    )
    end_time = datetime.now()

    if results:
        execution_time = (end_time - start_time).total_seconds()
        print(f"\n✅ 完整分析完成！耗时: {execution_time:.2f}秒")

        # 显示简要摘要
        print_analysis_summary(results)

        if 'report_file' in results and results['report_file']:
            print(f"📊 报告文件: {results['report_file']}")
    else:
        print("❌ 分析失败")


def print_analysis_summary(results):
    """打印分析结果摘要 - 简化版"""
    print(f"\n📋 分析摘要:")

    # 基础统计
    if 'network_analysis' in results:
        network = results['network_analysis']
        new_apps_count = len(network.get('new_apps_immediate', []))
        alerts_count = len(network.get('comprehensive_alerts', []))
        if new_apps_count > 0 or alerts_count > 0:
            print(f"   🆕 新APP: {new_apps_count}个")
            print(f"   🚨 综合预警: {alerts_count}个")

    # 模式检测结果
    if 'new_apps' in results and results['new_apps']:
        new_apps_by_risk = {'高': 0, '中': 0, '低': 0}
        for app in results['new_apps']:
            risk_level = getattr(app, 'risk_level', '低')
            new_apps_by_risk[risk_level] = new_apps_by_risk.get(risk_level, 0) + 1

        high_risk = new_apps_by_risk.get('高', 0)
        if high_risk > 0:
            print(f"   ⚠️ 高风险新APP: {high_risk}个")

    # 可疑模式
    if 'alerts' in results and results['alerts']:
        high_severity = len([alert for alert in results['alerts']
                             if getattr(alert, 'severity', '低') == '高'])
        if high_severity > 0:
            print(f"   🔴 高危预警: {high_severity}个")

    # 受害人统计
    if 'victim_profile' in results and results['victim_profile']:
        profile = results['victim_profile']
        print(f"   👥 受害人总数: {profile.total_victims}人")
        print(f"   📊 平均年龄: {profile.avg_age:.1f}岁")


if __name__ == "__main__":
    main()