"""
APP涉案监控系统 - 增强版报告生成器
现代化设计，丰富交互，完整分析内容，提升用户体验和价值
修复图表显示和加载问题，支持完全离线使用
"""
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path
from config import (
    EXPORT_DIR, COLORS, COLOR_PALETTE, REPORT_CONFIG,
    SYSTEM_CONSTANTS
)


class ReportGenerator:
    """增强版报告生成器 - 现代化交互式报告"""

    def __init__(self):
        self.export_dir = EXPORT_DIR
        self.colors = COLORS
        self.color_palette = COLOR_PALETTE

    def generate_comprehensive_report(self, df: pd.DataFrame,
                                      analysis_results: Dict[str, Any]) -> Path:
        """生成增强版综合风险分析报告"""
        if df is None or df.empty:
            print("❌ 无法创建风险分析报告：无数据")
            return None

        print("\n📊 生成增强版综合风险分析报告")
        print("=" * 60)

        # 生成报告内容
        generation_time = datetime.now().strftime(SYSTEM_CONSTANTS['datetime_format'])
        data_period = f"{df['insert_day'].min().strftime(SYSTEM_CONSTANTS['date_format'])} 至 {df['insert_day'].max().strftime(SYSTEM_CONSTANTS['date_format'])}"

        html_content = self._build_enhanced_html_report(
            df, analysis_results, generation_time, data_period
        )

        # 保存HTML文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = self.export_dir / f"智能风险分析报告_{timestamp}.html"

        # 确保目录存在
        self.export_dir.mkdir(parents=True, exist_ok=True)

        with open(output_file, 'w', encoding=SYSTEM_CONSTANTS['encoding']) as f:
            f.write(html_content)

        # 验证文件大小
        if output_file.exists() and output_file.stat().st_size > 10240:  # 至少10KB
            file_size_mb = output_file.stat().st_size / 1024 / 1024
            print(f"📊 增强版风险分析报告已保存: {output_file}")
            print(f"📁 文件大小: {file_size_mb:.1f} MB")
            return output_file
        else:
            print("❌ 报告文件可能生成不完整")
            return output_file

    def _get_plotly_offline_js(self) -> str:
        """获取Plotly.js的离线版本"""
        try:
            # 获取Plotly.js的离线版本
            return plotly.offline.get_plotlyjs()
        except Exception as e:
            print(f"⚠️ 无法获取Plotly离线JS: {e}")
            # 提供一个基础的备用JS
            return """
            // Plotly.js备用版本
            console.log('Plotly.js备用版本加载');
            window.Plotly = {
                newPlot: function() { console.log('图表渲染中...'); },
                Plots: { resize: function() {} }
            };
            """

    def _build_enhanced_html_report(self, df: pd.DataFrame, analysis_results: Dict[str, Any],
                                    generation_time: str, data_period: str) -> str:
        """构建增强版HTML报告 - 现代化设计"""

        # 生成离线Plotly JS
        plotly_js_content = self._get_plotly_offline_js()

        # 现代化HTML模板
        html_template = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>🛡️ APP涉案智能风险分析报告</title>
            <!-- 内嵌Plotly.js，避免CDN依赖 -->
            <script>{plotly_js_content}</script>
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                /* 现代化CSS样式 */
                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}

                body {{
                    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    color: #333;
                    line-height: 1.6;
                }}

                .container {{
                    max-width: 1400px;
                    margin: 0 auto;
                    padding: 20px;
                }}

                /* 头部样式 */
                .header {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 30px;
                    border-radius: 15px;
                    margin-bottom: 30px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                    position: relative;
                    overflow: hidden;
                }}

                .header::before {{
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
                    pointer-events: none;
                }}

                .header h1 {{
                    font-size: 2.5em;
                    margin-bottom: 10px;
                    position: relative;
                    z-index: 1;
                }}

                .header .meta {{
                    font-size: 1.1em;
                    opacity: 0.9;
                    position: relative;
                    z-index: 1;
                }}

                /* 导航标签 */
                .nav-tabs {{
                    display: flex;
                    background: white;
                    border-radius: 10px;
                    padding: 5px;
                    margin-bottom: 20px;
                    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                    overflow-x: auto;
                }}

                .nav-tab {{
                    flex: 1;
                    text-align: center;
                    padding: 12px 20px;
                    cursor: pointer;
                    border-radius: 8px;
                    transition: all 0.3s ease;
                    font-weight: 500;
                    white-space: nowrap;
                    min-width: 120px;
                }}

                .nav-tab:hover {{
                    background: #f8f9ff;
                    transform: translateY(-2px);
                }}

                .nav-tab.active {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
                }}

                /* 内容区域 */
                .tab-content {{
                    display: none;
                    animation: fadeIn 0.3s ease-in;
                }}

                .tab-content.active {{
                    display: block;
                }}

                @keyframes fadeIn {{
                    from {{ opacity: 0; transform: translateY(10px); }}
                    to {{ opacity: 1; transform: translateY(0); }}
                }}

                /* 卡片样式 */
                .card {{
                    background: white;
                    border-radius: 15px;
                    padding: 25px;
                    margin-bottom: 25px;
                    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
                    transition: transform 0.3s ease, box-shadow 0.3s ease;
                    border: 1px solid rgba(255,255,255,0.2);
                }}

                .card:hover {{
                    transform: translateY(-5px);
                    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
                }}

                .card h2 {{
                    margin-bottom: 20px;
                    color: #2c3e50;
                    font-size: 1.8em;
                    border-bottom: 3px solid #667eea;
                    padding-bottom: 10px;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }}

                /* 指标网格 */
                .metrics-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 20px;
                    margin: 20px 0;
                }}

                .metric-card {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 20px;
                    border-radius: 12px;
                    text-align: center;
                    transition: transform 0.3s ease;
                    position: relative;
                    overflow: hidden;
                }}

                .metric-card::before {{
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background-size: 20px 20px;
                }}

                .metric-card:hover {{
                    transform: scale(1.05);
                }}

                .metric-value {{
                    font-size: 1.5em;
                    font-weight: bold;
                    margin-bottom: 5px;
                    position: relative;
                    z-index: 1;
                }}

                .metric-label {{
                    font-size: 0.9em;
                    opacity: 0.9;
                    position: relative;
                    z-index: 1;
                }}

                /* 风险评估样式 */
                .risk-assessment {{
                    display: flex;
                    align-items: center;
                    gap: 30px;
                    padding: 30px;
                    border-radius: 15px;
                    background: white;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                }}

                .risk-info {{
                    flex: 1;
                }}

                .risk-score {{
                    font-size: 3em;
                    font-weight: bold;
                    margin-bottom: 10px;
                }}

                .risk-level {{
                    font-size: 1.5em;
                    margin-bottom: 15px;
                    font-weight: 600;
                }}

                .risk-details {{
                    color: #666;
                    line-height: 1.8;
                }}

                .risk-chart {{
                    width: 200px;
                    height: 200px;
                    position: relative;
                }}

                /* 表格样式 */
                .modern-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                    background: white;
                    border-radius: 10px;
                    overflow: hidden;
                    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                }}

                .modern-table th {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 15px;
                    text-align: left;
                    font-weight: 600;
                }}

                .modern-table td {{
                    padding: 12px 15px;
                    border-bottom: 1px solid #eee;
                    transition: background-color 0.3s ease;
                }}

                .modern-table tr:hover td {{
                    background-color: #f8f9ff;
                }}

                .modern-table tr:nth-child(even) {{
                    background-color: #fafbff;
                }}

                /* 预警样式 */
                .alert-item {{
                    padding: 15px 20px;
                    margin: 10px 0;
                    border-radius: 10px;
                    border-left: 5px solid;
                    transition: all 0.3s ease;
                    cursor: pointer;
                }}

                .alert-item:hover {{
                    transform: translateX(5px);
                    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                }}

                .alert-high {{
                    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
                    border-left-color: #f44336;
                }}

                .alert-medium {{
                    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
                    border-left-color: #ff9800;
                }}

                .alert-low {{
                    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
                    border-left-color: #4caf50;
                }}

                /* 图表容器 */
                .chart-container {{
                    margin: 20px 0;
                    padding: 20px;
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                    min-height: 400px;
                }}

                .chart-title {{
                    font-size: 1.4em;
                    margin-bottom: 15px;
                    color: #2c3e50;
                    font-weight: 600;
                }}

                /* 统计概览 */
                .stats-overview {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 20px;
                    margin: 20px 0;
                }}

                .stat-item {{
                    background: white;
                    padding: 20px;
                    border-radius: 12px;
                    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                    text-align: center;
                    transition: transform 0.3s ease;
                }}

                .stat-item:hover {{
                    transform: translateY(-3px);
                }}

                .stat-icon {{
                    font-size: 2.5em;
                    margin-bottom: 10px;
                    color: #667eea;
                }}

                .stat-value {{
                    font-size: 2em;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 5px;
                }}

                .stat-label {{
                    color: #666;
                    font-size: 0.9em;
                }}

                /* 趋势分析 */
                .trend-analysis {{
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 20px;
                    margin: 20px 0;
                }}

                .trend-item {{
                    background: white;
                    padding: 20px;
                    border-radius: 12px;
                    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                }}

                .trend-value {{
                    font-size: 1.8em;
                    font-weight: bold;
                    margin-bottom: 5px;
                }}

                .trend-change {{
                    font-size: 0.9em;
                    padding: 3px 8px;
                    border-radius: 15px;
                    font-weight: 500;
                }}

                .trend-up {{
                    background: #ffebee;
                    color: #f44336;
                }}

                .trend-down {{
                    background: #e8f5e8;
                    color: #4caf50;
                }}

                .trend-stable {{
                    background: #f3f4f6;
                    color: #666;
                }}

                /* 对比分析样式 */
                .comparison-section {{
                    background: white;
                    border-radius: 15px;
                    padding: 25px;
                    margin: 20px 0;
                    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
                }}

                .comparison-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 20px;
                    margin: 20px 0;
                }}

                .comparison-item {{
                    background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
                    padding: 20px;
                    border-radius: 12px;
                    border-left: 4px solid #667eea;
                }}

                .comparison-title {{
                    font-size: 1.2em;
                    font-weight: 600;
                    margin-bottom: 15px;
                    color: #2c3e50;
                }}

                .comparison-values {{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin: 10px 0;
                }}

                .comparison-change {{
                    padding: 5px 10px;
                    border-radius: 20px;
                    font-weight: 600;
                    font-size: 0.9em;
                }}

                .change-positive {{
                    background: #ffebee;
                    color: #f44336;
                }}

                .change-negative {{
                    background: #e8f5e8;
                    color: #4caf50;
                }}

                .change-neutral {{
                    background: #f5f5f5;
                    color: #666;
                }}

                /* 响应式设计 */
                @media (max-width: 768px) {{
                    .risk-assessment {{
                        flex-direction: column;
                        text-align: center;
                    }}

                    .trend-analysis {{
                        grid-template-columns: 1fr;
                    }}

                    .nav-tabs {{
                        flex-direction: column;
                    }}

                    .nav-tab {{
                        flex: none;
                    }}

                    .comparison-grid {{
                        grid-template-columns: 1fr;
                    }}
                }}

                /* 加载动画 */
                .loading {{
                    display: inline-block;
                    width: 20px;
                    height: 20px;
                    border: 3px solid #f3f3f3;
                    border-top: 3px solid #667eea;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }}

                @keyframes spin {{
                    0% {{ transform: rotate(0deg); }}
                    100% {{ transform: rotate(360deg); }}
                }}

                /* 徽章样式 */
                .badge {{
                    display: inline-block;
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-size: 0.8em;
                    font-weight: 500;
                    text-transform: uppercase;
                }}

                .badge-high {{
                    background: #ffebee;
                    color: #f44336;
                }}

                .badge-medium {{
                    background: #fff3e0;
                    color: #ff9800;
                }}

                .badge-low {{
                    background: #e8f5e8;
                    color: #4caf50;
                }}

                /* 工具提示 */
                .tooltip {{
                    position: relative;
                    cursor: help;
                }}

                .tooltip:hover::after {{
                    content: attr(data-tooltip);
                    position: absolute;
                    bottom: 100%;
                    left: 50%;
                    transform: translateX(-50%);
                    background: #333;
                    color: white;
                    padding: 8px 12px;
                    border-radius: 6px;
                    font-size: 0.8em;
                    white-space: nowrap;
                    z-index: 1000;
                    opacity: 0;
                    animation: fadeIn 0.3s ease-in forwards;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <!-- 头部 -->
                <div class="header">
                    <h1><i class="fas fa-shield-alt"></i> APP涉案智能风险分析报告</h1>
                    <div class="meta">
                        <i class="fas fa-calendar-alt"></i> 生成时间: {generation_time} | 
                        <i class="fas fa-clock"></i> 数据周期: {data_period}
                    </div>
                </div>

                <!-- 导航标签 -->
                <div class="nav-tabs">
                    <div class="nav-tab active" onclick="showTab('overview')">
                        <i class="fas fa-chart-line"></i> 概览
                    </div>
                    <div class="nav-tab" onclick="showTab('financial')">
                        <i class="fas fa-money-bill-wave"></i> 资金分析
                    </div>
                    <div class="nav-tab" onclick="showTab('risk')">
                        <i class="fas fa-exclamation-triangle"></i> 风险评估
                    </div>
                    <div class="nav-tab" onclick="showTab('apps')">
                        <i class="fas fa-mobile-alt"></i> APP分析
                    </div>
                    <div class="nav-tab" onclick="showTab('alerts')">
                        <i class="fas fa-bell"></i> 预警信息
                    </div>
                    <div class="nav-tab" onclick="showTab('trends')">
                        <i class="fas fa-trending-up"></i> 趋势分析
                    </div>
                    <div class="nav-tab" onclick="showTab('geography')">
                        <i class="fas fa-map-marked-alt"></i> 地域分析
                    </div>
                </div>

                <!-- 内容区域 -->
                {overview_section}
                {financial_section}
                {risk_section}
                {apps_section}
                {alerts_section}
                {trends_section}
                {geography_section}
            </div>

            <script>
                // JavaScript 交互功能
                function showTab(tabName) {{
                    // 隐藏所有内容
                    document.querySelectorAll('.tab-content').forEach(content => {{
                        content.classList.remove('active');
                    }});

                    // 移除所有活动标签
                    document.querySelectorAll('.nav-tab').forEach(tab => {{
                        tab.classList.remove('active');
                    }});

                    // 显示选中的内容
                    document.getElementById(tabName).classList.add('active');
                    event.target.classList.add('active');

                    // 重新渲染图表
                    setTimeout(() => {{
                        if (window.Plotly) {{
                            document.querySelectorAll('.plotly-graph-div').forEach(div => {{
                                window.Plotly.Plots.resize(div);
                            }});
                        }}
                    }}, 100);
                }}

                // 图表交互
                function toggleChart(chartId) {{
                    const chart = document.getElementById(chartId);
                    chart.style.display = chart.style.display === 'none' ? 'block' : 'none';
                }}

                // 数据排序
                function sortTable(tableId, column) {{
                    // 表格排序逻辑
                    console.log('Sorting table', tableId, 'by column', column);
                }}

                // 导出功能
                function exportData(format) {{
                    alert('导出功能: ' + format);
                }}

                // 页面加载完成后的初始化
                document.addEventListener('DOMContentLoaded', function() {{
                    console.log('报告页面加载完成');

                    // 确保所有图表正确显示
                    setTimeout(() => {{
                        if (window.Plotly) {{
                            // 重新渲染所有Plotly图表
                            document.querySelectorAll('.plotly-graph-div').forEach(div => {{
                                window.Plotly.Plots.resize(div);
                            }});
                            console.log('图表初始化完成');
                        }} else {{
                            console.warn('Plotly未成功加载');
                        }}
                    }}, 1000);

                    // 添加平滑滚动
                    document.querySelectorAll('a[href^="#"]').forEach(anchor => {{
                        anchor.addEventListener('click', function (e) {{
                            e.preventDefault();
                            document.querySelector(this.getAttribute('href')).scrollIntoView({{
                                behavior: 'smooth'
                            }});
                        }});
                    }});
                }});
            </script>
        </body>
        </html>
        """

        # 生成各个部分内容
        overview_section = self._generate_overview_section(df, analysis_results)
        financial_section = self._generate_financial_section(df, analysis_results)
        risk_section = self._generate_risk_section(analysis_results)
        apps_section = self._generate_apps_section(analysis_results)
        alerts_section = self._generate_alerts_section(analysis_results)
        trends_section = self._generate_trends_section(df, analysis_results)
        geography_section = self._generate_geography_section(df, analysis_results)

        return html_template.format(
            plotly_js_content=plotly_js_content,
            generation_time=generation_time,
            data_period=data_period,
            overview_section=overview_section,
            financial_section=financial_section,
            risk_section=risk_section,
            apps_section=apps_section,
            alerts_section=alerts_section,
            trends_section=trends_section,
            geography_section=geography_section
        )

    def _generate_overview_section(self, df: pd.DataFrame, analysis_results: Dict[str, Any]) -> str:
        """生成概览部分 - 核心指标和统计"""
        total_cases = len(df)
        unique_apps = df['final_app_name'].nunique()

        # 金额统计
        amount_data = df['involved_amount'].dropna() if 'involved_amount' in df.columns else pd.Series()
        amount_data = amount_data[amount_data > 0] if not amount_data.empty else pd.Series()

        total_amount = amount_data.sum() if not amount_data.empty else 0
        avg_amount = amount_data.mean() if not amount_data.empty else 0
        high_amount_cases = len(amount_data[amount_data >= 500000]) if not amount_data.empty else 0

        # 新APP统计
        new_apps_count = len(analysis_results.get('new_apps_detection', []))
        high_risk_apps = len([app for app in analysis_results.get('new_apps_detection', [])
                              if app.get('risk_level') == '高'])

        # 预警统计
        alerts_count = len(analysis_results.get('suspicious_patterns', []))
        high_alerts = len([alert for alert in analysis_results.get('suspicious_patterns', [])
                           if alert.get('severity') == '高'])

        return f"""
        <div id="overview" class="tab-content active">
            <div class="card">
                <h2><i class="fas fa-chart-pie"></i> 数据概览</h2>

                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">{total_cases:,}</div>
                        <div class="metric-label">预警信息</div>
                    </div>
                </div>

                <div class="stats-overview">
                    <div class="stat-item">
                        <div class="stat-icon"><i class="fas fa-exclamation-circle"></i></div>
                        <div class="stat-value">{high_amount_cases}</div>
                        <div class="stat-label">高额案件(≥50万)</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon"><i class="fas fa-mobile-alt"></i></div>
                        <div class="stat-value">{high_risk_apps}</div>
                        <div class="stat-label">高风险新APP</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon"><i class="fas fa-bell"></i></div>
                        <div class="stat-value">{high_alerts}</div>
                        <div class="stat-label">高危预警</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon"><i class="fas fa-percent"></i></div>
                        <div class="stat-value">{(high_amount_cases / total_cases * 100) if total_cases > 0 else 0:.1f}%</div>
                        <div class="stat-label">高额案件占比</div>
                    </div>
                </div>
            </div>

            <div class="card">
                <h2><i class="fas fa-chart-line"></i> 核心趋势图表</h2>
                <div class="chart-container">
                    {self._create_interactive_time_trend_chart(df)}
                </div>
            </div>
        </div>
        """

    def _generate_financial_section(self, df: pd.DataFrame, analysis_results: Dict[str, Any]) -> str:
        """生成资金分析部分 - 包含对比分析"""
        if 'involved_amount' not in df.columns:
            return f"""
            <div id="financial" class="tab-content">
                <div class="card">
                    <h2><i class="fas fa-money-bill-wave"></i> 资金分析</h2>
                    <p style="text-align: center; color: #666; font-size: 1.2em; padding: 40px;">
                        <i class="fas fa-info-circle"></i> 数据中缺少金额信息
                    </p>
                </div>
            </div>
            """

        amount_data = df['involved_amount'].dropna()
        amount_data = amount_data[amount_data > 0]

        if amount_data.empty:
            return f"""
            <div id="financial" class="tab-content">
                <div class="card">
                    <h2><i class="fas fa-money-bill-wave"></i> 资金分析</h2>
                    <p style="text-align: center; color: #666; font-size: 1.2em; padding: 40px;">
                        <i class="fas fa-info-circle"></i> 无有效金额数据
                    </p>
                </div>
            </div>
            """

        # 基础统计
        total_amount = amount_data.sum()
        avg_amount = amount_data.mean()
        median_amount = amount_data.median()
        max_amount = amount_data.max()
        min_amount = amount_data.min()
        case_count = len(amount_data)

        # 金额等级分布
        amount_levels = self._calculate_amount_levels(amount_data)

        # TOP10 高损失APP
        top_apps_financial = self._get_top_financial_apps(df)

        # 生成对比分析（如果有财务分析结果）
        comparison_section = ""
        financial_analysis = analysis_results.get('financial_analysis')
        if financial_analysis and hasattr(financial_analysis, 'comparison_data'):
            comparison_section = self._generate_financial_comparison(financial_analysis.comparison_data)

        return f"""
        <div id="financial" class="tab-content">
            <div class="card">
                <h2><i class="fas fa-money-bill-wave"></i> 资金影响分析</h2>

                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">{total_amount / 10000:,.1f}万</div>
                        <div class="metric-label">涉案总金额</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{avg_amount:,.0f}</div>
                        <div class="metric-label">平均损失(元)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{median_amount:,.0f}</div>
                        <div class="metric-label">中位数损失(元)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{max_amount:,.0f}</div>
                        <div class="metric-label">最高单笔(元)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{case_count:,}</div>
                        <div class="metric-label">有效案件数</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{len([x for x in amount_data if x >= 500000]):,}</div>
                        <div class="metric-label">高额案件(≥50万)</div>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-title">💰 金额等级分布</div>
                    {self._create_amount_distribution_chart(amount_levels)}
                </div>

                <h3><i class="fas fa-trophy"></i> TOP10 高损失APP</h3>
                {self._generate_top_apps_financial_table(top_apps_financial)}
            </div>

            {comparison_section}
        </div>
        """

    def _generate_financial_comparison(self, comparison_data: Dict[str, Any]) -> str:
        """生成资金对比分析部分"""
        if not comparison_data:
            return ""

        return f"""
        <div class="comparison-section">
            <h2><i class="fas fa-chart-bar"></i> 资金对比分析</h2>

            <div class="comparison-grid">
                {self._generate_weekly_comparison(comparison_data.get('weekly', {}))}
                {self._generate_monthly_comparison(comparison_data.get('monthly', {}))}
            </div>
        </div>
        """

    def _generate_weekly_comparison(self, weekly_data: Dict[str, Any]) -> str:
        """生成周度对比"""
        if not weekly_data:
            return ""

        current = weekly_data.get('current', {})
        previous = weekly_data.get('previous', {})

        if not current or not previous:
            return ""

        total_change = self._calculate_change_percentage(
            current.get('total_amount', 0),
            previous.get('total_amount', 0)
        )

        avg_change = self._calculate_change_percentage(
            current.get('avg_amount', 0),
            previous.get('avg_amount', 0)
        )

        count_change = self._calculate_change_percentage(
            current.get('case_count', 0),
            previous.get('case_count', 0)
        )

        return f"""
        <div class="comparison-item">
            <div class="comparison-title">📅 周度对比分析</div>

            <div class="comparison-values">
                <span>总金额</span>
                <span class="comparison-change {self._get_change_class(total_change)}">
                    {self._format_change(total_change)}
                </span>
            </div>

            <div class="comparison-values">
                <span>平均金额</span>
                <span class="comparison-change {self._get_change_class(avg_change)}">
                    {self._format_change(avg_change)}
                </span>
            </div>

            <div class="comparison-values">
                <span>案件数</span>
                <span class="comparison-change {self._get_change_class(count_change)}">
                    {self._format_change(count_change)}
                </span>
            </div>

            <small style="color: #666; margin-top: 10px; display: block;">
                本周: {current.get('total_amount', 0) / 10000:.1f}万元 vs 
                上周: {previous.get('total_amount', 0) / 10000:.1f}万元
            </small>
        </div>
        """

    def _generate_monthly_comparison(self, monthly_data: Dict[str, Any]) -> str:
        """生成月度对比"""
        if not monthly_data:
            return ""

        current = monthly_data.get('current', {})
        previous = monthly_data.get('previous', {})

        if not current or not previous:
            return ""

        total_change = self._calculate_change_percentage(
            current.get('total_amount', 0),
            previous.get('total_amount', 0)
        )

        avg_change = self._calculate_change_percentage(
            current.get('avg_amount', 0),
            previous.get('avg_amount', 0)
        )

        count_change = self._calculate_change_percentage(
            current.get('case_count', 0),
            previous.get('case_count', 0)
        )

        return f"""
        <div class="comparison-item">
            <div class="comparison-title">📅 月度对比分析</div>

            <div class="comparison-values">
                <span>总金额</span>
                <span class="comparison-change {self._get_change_class(total_change)}">
                    {self._format_change(total_change)}
                </span>
            </div>

            <div class="comparison-values">
                <span>平均金额</span>
                <span class="comparison-change {self._get_change_class(avg_change)}">
                    {self._format_change(avg_change)}
                </span>
            </div>

            <div class="comparison-values">
                <span>案件数</span>
                <span class="comparison-change {self._get_change_class(count_change)}">
                    {self._format_change(count_change)}
                </span>
            </div>

            <small style="color: #666; margin-top: 10px; display: block;">
                本月: {current.get('total_amount', 0) / 10000:.1f}万元 vs 
                上月同期: {previous.get('total_amount', 0) / 10000:.1f}万元
            </small>
        </div>
        """

    def _calculate_change_percentage(self, current: float, previous: float) -> float:
        """计算变化百分比"""
        if previous == 0:
            return float('inf') if current > 0 else 0
        return ((current - previous) / previous) * 100

    def _get_change_class(self, change: float) -> str:
        """获取变化样式类"""
        if change == float('inf') or change > 0:
            return 'change-positive'
        elif change < 0:
            return 'change-negative'
        else:
            return 'change-neutral'

    def _format_change(self, change: float) -> str:
        """格式化变化显示"""
        if change == float('inf'):
            return "🆕 新增"
        elif change > 0:
            return f"📈 +{change:.1f}%"
        elif change < 0:
            return f"📉 {change:.1f}%"
        else:
            return "➡️ 持平"

    def _calculate_amount_levels(self, amount_data: pd.Series) -> Dict[str, int]:
        """计算金额等级分布"""
        levels = {
            '小额(<1万)': len(amount_data[amount_data < 10000]),
            '中额(1-10万)': len(amount_data[(amount_data >= 10000) & (amount_data < 100000)]),
            '大额(10-50万)': len(amount_data[(amount_data >= 100000) & (amount_data < 500000)]),
            '巨额(50-100万)': len(amount_data[(amount_data >= 500000) & (amount_data < 1000000)]),
            '巨额(≥100万)': len(amount_data[amount_data >= 1000000])
        }
        return levels

    def _get_top_financial_apps(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """获取TOP财务影响APP"""
        if 'final_app_name' not in df.columns or 'involved_amount' not in df.columns:
            return []

        app_financial = df.groupby('final_app_name').agg({
            'involved_amount': ['count', 'sum', 'mean', 'max']
        }).round(2)

        app_financial.columns = ['案件数', '总金额', '平均金额', '最高金额']
        app_financial = app_financial.sort_values('总金额', ascending=False)

        result = []
        for app, row in app_financial.head(10).iterrows():
            result.append({
                'app_name': app,
                'case_count': int(row['案件数']),
                'total_amount': row['总金额'],
                'avg_amount': row['平均金额'],
                'max_amount': row['最高金额']
            })

        return result

    def _generate_risk_section(self, analysis_results: Dict[str, Any]) -> str:
        """生成风险评估部分"""
        # 计算综合风险评分
        new_apps_count = len(analysis_results.get('new_apps_detection', []))
        high_risk_apps = len([app for app in analysis_results.get('new_apps_detection', [])
                              if app.get('risk_level') == '高'])
        alerts_count = len(analysis_results.get('suspicious_patterns', []))
        high_alerts = len([alert for alert in analysis_results.get('suspicious_patterns', [])
                           if alert.get('severity') == '高'])

        # 风险评分计算
        risk_score = min(100, high_risk_apps * 15 + high_alerts * 10 + new_apps_count * 5)

        if risk_score >= 70:
            risk_level = "高风险"
            risk_color = "#e74c3c"
            risk_icon = "fas fa-exclamation-triangle"
        elif risk_score >= 40:
            risk_level = "中风险"
            risk_color = "#f39c12"
            risk_icon = "fas fa-exclamation-circle"
        elif risk_score >= 20:
            risk_level = "低风险"
            risk_color = "#f1c40f"
            risk_icon = "fas fa-info-circle"
        else:
            risk_level = "正常"
            risk_color = "#27ae60"
            risk_icon = "fas fa-check-circle"

        return f"""
        <div id="risk" class="tab-content">
            <div class="card">
                <h2><i class="fas fa-shield-alt"></i> 综合风险评估</h2>

                <div class="risk-assessment">
                    <div class="risk-info">
                        <div class="risk-level" style="color: {risk_color};">
                            <i class="{risk_icon}"></i> {risk_level}
                        </div>
                        <div class="risk-score" style="color: {risk_color};">{risk_score}/100</div>
                        <div class="risk-details">
                            <p><strong>评估依据:</strong></p>
                            <ul>
                                <li>新发现APP数量: {new_apps_count} 个</li>
                                <li>高风险APP: {high_risk_apps} 个</li>
                                <li>预警信息: {alerts_count} 个</li>
                                <li>高危预警: {high_alerts} 个</li>
                            </ul>
                        </div>
                    </div>
                    <div class="risk-chart">
                        <canvas id="riskChart" width="200" height="200"></canvas>
                    </div>
                </div>

                <div class="trend-analysis">
                    <div class="trend-item">
                        <h3><i class="fas fa-mobile-alt"></i> APP风险分析</h3>
                        <div class="trend-value">{high_risk_apps}/{new_apps_count}</div>
                        <div class="trend-change {'trend-up' if high_risk_apps > 0 else 'trend-stable'}">
                            {"⚠️ 存在高风险APP" if high_risk_apps > 0 else "✅ 暂无高风险APP"}
                        </div>
                    </div>
                    <div class="trend-item">
                        <h3><i class="fas fa-bell"></i> 预警分析</h3>
                        <div class="trend-value">{high_alerts}/{alerts_count}</div>
                        <div class="trend-change {'trend-up' if high_alerts > 0 else 'trend-stable'}">
                            {"🚨 存在高危预警" if high_alerts > 0 else "✅ 暂无高危预警"}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        """

    def _generate_apps_section(self, analysis_results: Dict[str, Any]) -> str:
        """生成APP分析部分"""
        new_apps = analysis_results.get('new_apps_detection', [])

        if not new_apps:
            return f"""
            <div id="apps" class="tab-content">
                <div class="card">
                    <h2><i class="fas fa-mobile-alt"></i> APP分析</h2>
                    <p style="text-align: center; color: #666; font-size: 1.2em; padding: 40px;">
                        <i class="fas fa-info-circle"></i> 当前周期内未发现新APP
                    </p>
                </div>
            </div>
            """

        # 按风险等级分组
        high_risk_apps = [app for app in new_apps if app.get('risk_level') == '高']
        medium_risk_apps = [app for app in new_apps if app.get('risk_level') == '中']
        low_risk_apps = [app for app in new_apps if app.get('risk_level') == '低']

        # 生成APP表格
        apps_table = self._generate_apps_table(new_apps[:20])  # 显示TOP20

        return f"""
        <div id="apps" class="tab-content">
            <div class="card">
                <h2><i class="fas fa-mobile-alt"></i> 新APP分析</h2>

                <div class="stats-overview">
                    <div class="stat-item">
                        <div class="stat-icon" style="color: #e74c3c;"><i class="fas fa-exclamation-triangle"></i></div>
                        <div class="stat-value">{len(high_risk_apps)}</div>
                        <div class="stat-label">高风险APP</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon" style="color: #f39c12;"><i class="fas fa-exclamation-circle"></i></div>
                        <div class="stat-value">{len(medium_risk_apps)}</div>
                        <div class="stat-label">中风险APP</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon" style="color: #27ae60;"><i class="fas fa-check-circle"></i></div>
                        <div class="stat-value">{len(low_risk_apps)}</div>
                        <div class="stat-label">低风险APP</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon" style="color: #3498db;"><i class="fas fa-chart-line"></i></div>
                        <div class="stat-value">{sum(app.get('case_count', 0) for app in new_apps)}</div>
                        <div class="stat-label">总案件数</div>
                    </div>
                </div>

                <h3><i class="fas fa-list"></i> TOP20 新发现APP详情</h3>
                {apps_table}

                {self._generate_app_risk_chart(high_risk_apps, medium_risk_apps, low_risk_apps)}
            </div>
        </div>
        """

    def _generate_alerts_section(self, analysis_results: Dict[str, Any]) -> str:
        """生成预警信息部分"""
        alerts = self._get_all_alerts(analysis_results)

        if not alerts:
            return f"""
            <div id="alerts" class="tab-content">
                <div class="card">
                    <h2><i class="fas fa-bell"></i> 预警信息</h2>
                    <p style="text-align: center; color: #666; font-size: 1.2em; padding: 40px;">
                        <i class="fas fa-check-circle" style="color: #27ae60;"></i> 未发现明显的预警信息
                    </p>
                </div>
            </div>
            """

        # 按严重程度分组
        high_alerts = [alert for alert in alerts if alert.get('severity') == '高']
        medium_alerts = [alert for alert in alerts if alert.get('severity') == '中']
        low_alerts = [alert for alert in alerts if alert.get('severity') == '低']

        # 按类型分组统计
        type_stats = {}
        for alert in alerts:
            alert_type = alert.get('type', '未知')
            type_stats[alert_type] = type_stats.get(alert_type, 0) + 1

        return f"""
        <div id="alerts" class="tab-content">
            <div class="card">
                <h2><i class="fas fa-bell"></i> 预警信息 ({len(alerts)}个)</h2>

                <div class="stats-overview">
                    <div class="stat-item">
                        <div class="stat-icon" style="color: #e74c3c;"><i class="fas fa-exclamation-triangle"></i></div>
                        <div class="stat-value">{len(high_alerts)}</div>
                        <div class="stat-label">高危预警</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon" style="color: #f39c12;"><i class="fas fa-exclamation-circle"></i></div>
                        <div class="stat-value">{len(medium_alerts)}</div>
                        <div class="stat-label">中等预警</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon" style="color: #27ae60;"><i class="fas fa-info-circle"></i></div>
                        <div class="stat-value">{len(low_alerts)}</div>
                        <div class="stat-label">低级预警</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon" style="color: #3498db;"><i class="fas fa-tags"></i></div>
                        <div class="stat-value">{len(type_stats)}</div>
                        <div class="stat-label">预警类型</div>
                    </div>
                </div>

                <h3><i class="fas fa-list-ul"></i> 预警类型分布</h3>
                <div class="chart-container">
                    {self._generate_alert_type_chart(type_stats)}
                </div>

                <h3><i class="fas fa-exclamation-triangle"></i> 详细预警信息</h3>
                <div style="max-height: 600px; overflow-y: auto;">
                    {self._generate_alerts_list(alerts[:50])}
                </div>
            </div>
        </div>
        """

    def _generate_trends_section(self, df: pd.DataFrame, analysis_results: Dict[str, Any]) -> str:
        """生成趋势分析部分"""
        return f"""
        <div id="trends" class="tab-content">
            <div class="card">
                <h2><i class="fas fa-trending-up"></i> 趋势分析</h2>

                <div class="chart-container">
                    <div class="chart-title">📈 案件数量时间趋势</div>
                    {self._create_time_trend_chart(df)}
                </div>

                <div class="chart-container">
                    <div class="chart-title">📱 TOP15 APP分布</div>
                    {self._create_app_distribution_chart(df)}
                </div>

                <div class="chart-container">
                    <div class="chart-title">💰 金额分布分析</div>
                    {self._create_amount_distribution_chart_detailed(df)}
                </div>
            </div>
        </div>
        """

    def _generate_geography_section(self, df: pd.DataFrame, analysis_results: Dict[str, Any]) -> str:
        """生成地域分析部分"""
        if 'occurrence_area' not in df.columns:
            return f"""
            <div id="geography" class="tab-content">
                <div class="card">
                    <h2><i class="fas fa-map-marked-alt"></i> 地域分析</h2>
                    <p style="text-align: center; color: #666; font-size: 1.2em; padding: 40px;">
                        <i class="fas fa-info-circle"></i> 数据中缺少地域信息
                    </p>
                </div>
            </div>
            """

        # 地域统计
        area_counts = df['occurrence_area'].value_counts().head(20)
        total_areas = df['occurrence_area'].nunique()

        return f"""
        <div id="geography" class="tab-content">
            <div class="card">
                <h2><i class="fas fa-map-marked-alt"></i> 地域分布分析</h2>

                <div class="stats-overview">
                    <div class="stat-item">
                        <div class="stat-icon"><i class="fas fa-map"></i></div>
                        <div class="stat-value">{total_areas}</div>
                        <div class="stat-label">涉及地区数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon"><i class="fas fa-chart-bar"></i></div>
                        <div class="stat-value">{area_counts.iloc[0] if len(area_counts) > 0 else 0}</div>
                        <div class="stat-label">最高发案数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon"><i class="fas fa-balance-scale"></i></div>
                        <div class="stat-value">{len(df) / total_areas if total_areas > 0 else 0:.1f}</div>
                        <div class="stat-label">平均案件/地区</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon"><i class="fas fa-crown"></i></div>
                        <div class="stat-value">{area_counts.index[0] if len(area_counts) > 0 else '无'}</div>
                        <div class="stat-label">高发地区</div>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-title">🗺️ TOP20 地区分布</div>
                    {self._create_area_distribution_chart(df)}
                </div>

                <h3><i class="fas fa-list"></i> 地区详细统计</h3>
                {self._generate_area_table(area_counts)}
            </div>
        </div>
        """

    # 图表生成方法
    def _create_interactive_time_trend_chart(self, df: pd.DataFrame) -> str:
        """创建交互式时间趋势图 - 修复版"""
        if 'insert_day' not in df.columns:
            return "<p>暂无时间数据</p>"

        try:
            # 按日期统计案件数和金额
            df['date'] = df['insert_day'].dt.date
            daily_stats = df.groupby('date').agg({
                'case_number': 'count',
                'involved_amount': 'sum' if 'involved_amount' in df.columns else 'count'
            }).reset_index()
            daily_stats.columns = ['date', 'case_count', 'total_amount']

            # 确保日期是连续的
            date_range = pd.date_range(start=daily_stats['date'].min(),
                                       end=daily_stats['date'].max(),
                                       freq='D')
            full_dates = pd.DataFrame({'date': date_range.date})
            daily_stats = full_dates.merge(daily_stats, on='date', how='left').fillna(0)

            fig = make_subplots(specs=[[{"secondary_y": True}]])

            fig.add_trace(
                go.Scatter(
                    x=daily_stats['date'],
                    y=daily_stats['case_count'],
                    name='案件数',
                    line=dict(color='#667eea', width=3),
                    mode='lines+markers',
                    marker=dict(size=6),
                    hovertemplate='<b>%{x}</b><br>案件数: %{y}<extra></extra>'
                ),
                secondary_y=False
            )

            if 'involved_amount' in df.columns and daily_stats['total_amount'].sum() > 0:
                fig.add_trace(
                    go.Scatter(
                        x=daily_stats['date'],
                        y=daily_stats['total_amount'],
                        name='涉案金额',
                        line=dict(color='#e74c3c', width=3),
                        mode='lines+markers',
                        marker=dict(size=6),
                        hovertemplate='<b>%{x}</b><br>金额: ¥%{y:,.0f}<extra></extra>'
                    ),
                    secondary_y=True
                )

            fig.update_layout(
                height=400,
                hovermode='x unified',
                legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
                plot_bgcolor='rgba(0,0,0,0)',
                paper_bgcolor='rgba(0,0,0,0)',
                margin=dict(l=0, r=0, t=0, b=0)
            )

            fig.update_yaxes(title_text="案件数", secondary_y=False)
            if 'involved_amount' in df.columns and daily_stats['total_amount'].sum() > 0:
                fig.update_yaxes(title_text="涉案金额(元)", secondary_y=True)

            return fig.to_html(include_plotlyjs=False, full_html=False,
                               config={'displayModeBar': True, 'responsive': True})

        except Exception as e:
            return f"<p>图表生成失败: {e}</p>"

    def _create_time_trend_chart(self, df: pd.DataFrame) -> Optional[str]:
        """创建时间趋势图"""
        if 'insert_day' not in df.columns:
            return "<p>暂无时间数据</p>"

        try:
            daily_stats = df.groupby(df['insert_day'].dt.date).agg({
                'case_number': 'count',
                'involved_amount': 'sum' if 'involved_amount' in df.columns else 'count'
            }).reset_index()
            daily_stats.columns = ['date', 'case_count', 'total_amount']

            fig = make_subplots(specs=[[{"secondary_y": True}]])

            fig.add_trace(
                go.Scatter(
                    x=daily_stats['date'],
                    y=daily_stats['case_count'],
                    name='案件数',
                    line=dict(color='#667eea', width=3),
                    mode='lines+markers'
                ),
                secondary_y=False
            )

            if 'involved_amount' in df.columns:
                fig.add_trace(
                    go.Scatter(
                        x=daily_stats['date'],
                        y=daily_stats['total_amount'],
                        name='涉案金额',
                        line=dict(color='#e74c3c', width=3),
                        mode='lines+markers'
                    ),
                    secondary_y=True
                )

            fig.update_layout(
                height=400,
                hovermode='x unified',
                legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
                plot_bgcolor='rgba(0,0,0,0)',
                paper_bgcolor='rgba(0,0,0,0)'
            )

            fig.update_yaxes(title_text="案件数", secondary_y=False)
            if 'involved_amount' in df.columns:
                fig.update_yaxes(title_text="涉案金额(元)", secondary_y=True)

            return fig.to_html(include_plotlyjs=False, full_html=False)

        except Exception as e:
            return f"<p>图表生成失败: {e}</p>"

    def _create_app_distribution_chart(self, df: pd.DataFrame) -> Optional[str]:
        """创建APP分布图"""
        if 'final_app_name' not in df.columns:
            return "<p>暂无APP数据</p>"

        try:
            app_counts = df['final_app_name'].value_counts().head(15)

            fig = go.Figure(go.Bar(
                y=app_counts.index,
                x=app_counts.values,
                orientation='h',
                marker_color='#764ba2',
                hovertemplate='<b>%{y}</b><br>案件数: %{x}<extra></extra>'
            ))

            fig.update_layout(
                height=500,
                xaxis_title='案件数',
                yaxis_title='APP名称',
                yaxis={'categoryorder': 'total ascending'},
                plot_bgcolor='rgba(0,0,0,0)',
                paper_bgcolor='rgba(0,0,0,0)'
            )

            return fig.to_html(include_plotlyjs=False, full_html=False)

        except Exception as e:
            return f"<p>图表生成失败: {e}</p>"

    def _create_area_distribution_chart(self, df: pd.DataFrame) -> Optional[str]:
        """创建地区分布图"""
        if 'occurrence_area' not in df.columns:
            return "<p>暂无地区数据</p>"

        try:
            area_counts = df['occurrence_area'].value_counts().head(20)

            fig = go.Figure(go.Bar(
                x=area_counts.index,
                y=area_counts.values,
                marker_color='#27ae60',
                hovertemplate='<b>%{x}</b><br>案件数: %{y}<extra></extra>'
            ))

            fig.update_layout(
                height=400,
                xaxis_title='地区',
                yaxis_title='案件数',
                xaxis_tickangle=-45,
                plot_bgcolor='rgba(0,0,0,0)',
                paper_bgcolor='rgba(0,0,0,0)'
            )

            return fig.to_html(include_plotlyjs=False, full_html=False)

        except Exception as e:
            return f"<p>图表生成失败: {e}</p>"

    def _create_amount_distribution_chart(self, amount_levels: Dict[str, int]) -> str:
        """创建金额分布图"""
        try:
            labels = list(amount_levels.keys())
            values = list(amount_levels.values())

            fig = go.Figure(data=[go.Pie(
                labels=labels,
                values=values,
                marker_colors=['#3498db', '#2ecc71', '#f39c12', '#e74c3c', '#9b59b6'],
                hovertemplate='<b>%{label}</b><br>数量: %{value}<br>占比: %{percent}<extra></extra>'
            )])

            fig.update_layout(
                height=400,
                showlegend=True,
                plot_bgcolor='rgba(0,0,0,0)',
                paper_bgcolor='rgba(0,0,0,0)'
            )

            return fig.to_html(include_plotlyjs=False, full_html=False)

        except Exception as e:
            return f"<p>图表生成失败: {e}</p>"

    def _create_amount_distribution_chart_detailed(self, df: pd.DataFrame) -> Optional[str]:
        """创建详细金额分布图"""
        if 'involved_amount' not in df.columns:
            return "<p>暂无金额数据</p>"

        try:
            amount_data = df['involved_amount'].dropna()
            amount_data = amount_data[amount_data > 0]

            if amount_data.empty:
                return "<p>暂无有效金额数据</p>"

            # 创建金额区间
            bins = [0, 1000, 5000, 10000, 50000, 100000, 500000, float('inf')]
            labels = ['<1千', '1千-5千', '5千-1万', '1万-5万', '5万-10万', '10万-50万', '50万+']

            amount_ranges = pd.cut(amount_data, bins=bins, labels=labels, include_lowest=True)
            range_counts = amount_ranges.value_counts()

            fig = go.Figure(data=[go.Bar(
                x=range_counts.index,
                y=range_counts.values,
                marker_color='#f39c12',
                hovertemplate='<b>%{x}</b><br>案件数: %{y}<extra></extra>'
            )])

            fig.update_layout(
                height=400,
                xaxis_title='金额区间',
                yaxis_title='案件数',
                plot_bgcolor='rgba(0,0,0,0)',
                paper_bgcolor='rgba(0,0,0,0)'
            )

            return fig.to_html(include_plotlyjs=False, full_html=False)

        except Exception as e:
            return f"<p>图表生成失败: {e}</p>"

    def _generate_app_risk_chart(self, high_risk_apps, medium_risk_apps, low_risk_apps) -> str:
        """生成APP风险分布图"""
        data = [len(high_risk_apps), len(medium_risk_apps), len(low_risk_apps)]
        labels = ['高风险', '中风险', '低风险']
        colors = ['#e74c3c', '#f39c12', '#27ae60']

        fig = go.Figure(data=[go.Pie(
            labels=labels,
            values=data,
            marker_colors=colors,
            hovertemplate='<b>%{label}</b><br>数量: %{value}<br>占比: %{percent}<extra></extra>'
        )])

        fig.update_layout(
            height=400,
            showlegend=True,
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)'
        )

        return f"""
        <div class="chart-container">
            <div class="chart-title">🎯 APP风险等级分布</div>
            {fig.to_html(include_plotlyjs=False, full_html=False)}
        </div>
        """

    def _generate_alert_type_chart(self, type_stats: Dict[str, int]) -> str:
        """生成预警类型分布图"""
        if not type_stats:
            return "<p>暂无预警类型数据</p>"

        fig = go.Figure(data=[go.Bar(
            x=list(type_stats.keys()),
            y=list(type_stats.values()),
            marker_color='#667eea',
            hovertemplate='<b>%{x}</b><br>数量: %{y}<extra></extra>'
        )])

        fig.update_layout(
            height=300,
            xaxis_title='预警类型',
            yaxis_title='数量',
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)'
        )

        return fig.to_html(include_plotlyjs=False, full_html=False)

    # 表格生成方法
    def _generate_apps_table(self, apps: List[Dict[str, Any]]) -> str:
        """生成APP表格"""
        if not apps:
            return "<p>暂无新APP数据</p>"

        table_html = """
        <table class="modern-table">
            <thead>
                <tr>
                    <th>排名</th>
                    <th>APP名称</th>
                    <th>风险等级</th>
                    <th>案件数</th>
                    <th>涉案金额(万)</th>
                    <th>平均金额(万)</th>
                    <th>首次发现</th>
                </tr>
            </thead>
            <tbody>
        """

        for i, app in enumerate(apps, 1):
            risk_level = app.get('risk_level', '低')
            risk_badge_class = {
                '高': 'badge-high',
                '中': 'badge-medium',
                '低': 'badge-low'
            }.get(risk_level, 'badge-low')

            first_seen = app.get('first_seen')
            if hasattr(first_seen, 'strftime'):
                first_seen_str = first_seen.strftime('%m-%d')
            else:
                first_seen_str = str(first_seen) if first_seen else '未知'

            table_html += f"""
            <tr>
                <td>{i}</td>
                <td><strong>{app.get('app_name', '未知')}</strong></td>
                <td><span class="badge {risk_badge_class}">{risk_level}</span></td>
                <td>{app.get('case_count', 0)}</td>
                <td>{app.get('total_amount', 0) / 10000:.2f}</td>
                <td>{app.get('avg_amount', 0) / 10000:.2f}</td>
                <td>{first_seen_str}</td>
            </tr>
            """

        table_html += """
            </tbody>
        </table>
        """

        return table_html

    def _generate_top_apps_financial_table(self, top_apps: List[Dict[str, Any]]) -> str:
        """生成TOP APP财务表格"""
        if not top_apps:
            return "<p>暂无财务数据</p>"

        table_html = """
        <table class="modern-table">
            <thead>
                <tr>
                    <th>排名</th>
                    <th>APP名称</th>
                    <th>案件数</th>
                    <th>总金额(万)</th>
                    <th>平均金额(万)</th>
                    <th>最高金额(万)</th>
                </tr>
            </thead>
            <tbody>
        """

        for i, app in enumerate(top_apps, 1):
            table_html += f"""
            <tr>
                <td>{i}</td>
                <td><strong>{app['app_name']}</strong></td>
                <td>{app['case_count']}</td>
                <td>{app['total_amount'] / 10000:.2f}</td>
                <td>{app['avg_amount'] / 10000:.2f}</td>
                <td>{app['max_amount'] / 10000:.2f}</td>
            </tr>
            """

        table_html += """
            </tbody>
        </table>
        """

        return table_html

    def _generate_alerts_list(self, alerts: List[Dict[str, Any]]) -> str:
        """生成预警列表"""
        if not alerts:
            return "<p>暂无预警信息</p>"

        alerts_html = ""
        for alert in alerts:
            severity = alert.get('severity', '低')
            alert_class = f"alert-{severity.lower()}"
            severity_emoji = {'高': '🔴', '中': '🟡', '低': '🟢'}.get(severity, '⚪')

            alerts_html += f"""
            <div class="alert-item {alert_class}">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <strong>{severity_emoji} [{alert.get('type', '未知类型')}] {alert.get('app_name', '未知APP')}</strong>
                        <p style="margin: 5px 0 0 0; color: #666;">{alert.get('description', '无描述')}</p>
                    </div>
                    <span class="badge badge-{severity.lower()}">{severity}</span>
                </div>
            </div>
            """

        return alerts_html

    def _generate_area_table(self, area_counts) -> str:
        """生成地区统计表格"""
        table_html = """
        <table class="modern-table">
            <thead>
                <tr>
                    <th>排名</th>
                    <th>地区</th>
                    <th>案件数</th>
                    <th>占比</th>
                </tr>
            </thead>
            <tbody>
        """

        total_cases = area_counts.sum()
        for i, (area, count) in enumerate(area_counts.head(20).items(), 1):
            percentage = (count / total_cases * 100) if total_cases > 0 else 0
            table_html += f"""
            <tr>
                <td>{i}</td>
                <td><strong>{area}</strong></td>
                <td>{count}</td>
                <td>{percentage:.1f}%</td>
            </tr>
            """

        table_html += """
            </tbody>
        </table>
        """

        return table_html

    # 辅助方法
    def _get_all_alerts(self, analysis_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取所有预警信息"""
        alerts = []

        # 从多个来源获取预警
        if 'suspicious_patterns' in analysis_results:
            patterns = analysis_results['suspicious_patterns']
            if isinstance(patterns, list):
                alerts.extend(patterns)

        if 'growth_monitoring' in analysis_results:
            growth_results = analysis_results['growth_monitoring']
            if isinstance(growth_results, list):
                for growth_alert in growth_results:
                    alerts.append({
                        'type': 'APP增长异常',
                        'app_name': growth_alert.get('app_name', '未知'),
                        'severity': growth_alert.get('alert_level', '中'),
                        'description': f"增长速度异常，加速度{growth_alert.get('acceleration', 0):.2f}"
                    })

        if 'explosion_prediction' in analysis_results:
            explosion_results = analysis_results['explosion_prediction']
            if isinstance(explosion_results, list):
                for explosion_alert in explosion_results:
                    alerts.append({
                        'type': 'APP爆发风险',
                        'app_name': explosion_alert.get('app_name', '未知'),
                        'severity': explosion_alert.get('risk_level', '中'),
                        'description': f"爆发概率{explosion_alert.get('explosion_probability', 0):.1%}"
                    })

        return self._deduplicate_alerts_for_report(alerts)

    def _deduplicate_alerts_for_report(self, alerts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """为报告去重预警信息"""
        alert_map = {}
        for alert in alerts:
            if not isinstance(alert, dict):
                continue

            key = (alert.get('app_name', ''), alert.get('type', ''))
            if key not in alert_map:
                alert_map[key] = alert
            else:
                # 保留严重程度更高的
                current_severity = {'高': 3, '中': 2, '低': 1}.get(alert.get('severity', '低'), 0)
                existing_severity = {'高': 3, '中': 2, '低': 1}.get(alert_map[key].get('severity', '低'), 0)
                if current_severity > existing_severity:
                    alert_map[key] = alert

        return list(alert_map.values())

    def _generate_overview_section(self, df: pd.DataFrame, analysis_results: Dict[str, Any]) -> str:
        """生成概览部分 - 核心指标和统计"""
        total_cases = len(df)
        unique_apps = df['final_app_name'].nunique()

        # 金额统计
        amount_data = df['involved_amount'].dropna() if 'involved_amount' in df.columns else pd.Series()
        amount_data = amount_data[amount_data > 0] if not amount_data.empty else pd.Series()

        total_amount = amount_data.sum() if not amount_data.empty else 0
        avg_amount = amount_data.mean() if not amount_data.empty else 0
        high_amount_cases = len(amount_data[amount_data >= 500000]) if not amount_data.empty else 0

        # 新APP统计
        new_apps_count = len(analysis_results.get('new_apps_detection', []))
        high_risk_apps = len([app for app in analysis_results.get('new_apps_detection', [])
                              if app.get('risk_level') == '高'])

        # 预警统计
        alerts_count = len(analysis_results.get('suspicious_patterns', []))
        high_alerts = len([alert for alert in analysis_results.get('suspicious_patterns', [])
                           if alert.get('severity') == '高'])

        return f"""
        <div id="overview" class="tab-content active">
            <div class="card">
                <h2><i class="fas fa-chart-pie"></i> 数据概览</h2>

                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">{total_cases:,}</div>
                        <div class="metric-label">案件总数</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{unique_apps:,}</div>
                        <div class="metric-label">涉案APP数</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{total_amount / 10000:,.1f}万</div>
                        <div class="metric-label">涉案总金额</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{avg_amount:,.0f}</div>
                        <div class="metric-label">平均损失(元)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{new_apps_count}</div>
                        <div class="metric-label">新发现APP</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{alerts_count}</div>
                        <div class="metric-label">预警信息</div>
                    </div>
                </div>

                <div class="stats-overview">
                    <div class="stat-item">
                        <div class="stat-icon"><i class="fas fa-exclamation-circle"></i></div>
                        <div class="stat-value">{high_amount_cases}</div>
                        <div class="stat-label">高额案件(≥50万)</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon"><i class="fas fa-mobile-alt"></i></div>
                        <div class="stat-value">{high_risk_apps}</div>
                        <div class="stat-label">高风险新APP</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon"><i class="fas fa-bell"></i></div>
                        <div class="stat-value">{high_alerts}</div>
                        <div class="stat-label">高危预警</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon"><i class="fas fa-percent"></i></div>
                        <div class="stat-value">{(high_amount_cases / total_cases * 100) if total_cases > 0 else 0:.1f}%</div>
                        <div class="stat-label">高额案件占比</div>
                    </div>
                </div>
            </div>

            <div class="card">
                <h2><i class="fas fa-chart-line"></i> 核心趋势图表</h2>
                <div class="chart-container">
                    {self._create_interactive_time_trend_chart(df)}
                </div>
            </div>
        </div>
        """