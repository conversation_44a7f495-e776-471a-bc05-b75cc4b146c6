"""
时间工具函数 - 支持自然周、自然月等时间计算
修复版本：确保周一到周日的完整计算，排除当天数据避免延迟问题
"""
from datetime import date, timedelta
from typing import Tuple
import calendar


def get_last_natural_week() -> Tuple[date, date]:
    """获取上一个自然周（周一到周日）- 修复版本"""
    today = date.today()
    days_since_monday = today.weekday()  # 0=周一, 6=周日
    this_week_monday = today - timedelta(days=days_since_monday)
    last_week_monday = this_week_monday - timedelta(days=7)
    last_week_sunday = last_week_monday + timedelta(days=6)
    return last_week_monday, last_week_sunday


def get_current_natural_week() -> Tuple[date, date]:
    """获取当前自然周（周一到周日）- 修复版本，排除当天数据"""
    today = date.today()
    days_since_monday = today.weekday()  # 0=周一, 6=周日
    week_monday = today - timedelta(days=days_since_monday)
    week_sunday = week_monday + timedelta(days=6)

    # 修复问题1：如果本周还没结束，结束时间为昨天，避免当天数据延迟问题
    yesterday = today - timedelta(days=1)
    if week_sunday >= today:
        week_sunday = yesterday

    # 确保开始时间不超过结束时间
    if week_monday > week_sunday:
        week_monday = week_sunday

    return week_monday, week_sunday


def get_current_month_to_date() -> Tuple[date, date]:
    """获取本月1日到当前日期 - 修复版本，排除当天数据"""
    today = date.today()
    month_start = date(today.year, today.month, 1)

    # 修复问题1：结束时间为昨天，避免当天数据延迟问题
    yesterday = today - timedelta(days=1)
    month_end = yesterday

    # 确保开始时间不超过结束时间
    if month_start > month_end:
        month_start = month_end

    return month_start, month_end


def get_previous_month_same_period() -> Tuple[date, date]:
    """获取上个月同期时间范围 - 修复版本"""
    today = date.today()
    current_month = today.month
    current_year = today.year

    # 计算上个月
    if current_month == 1:
        prev_month = 12
        prev_year = current_year - 1
    else:
        prev_month = current_month - 1
        prev_year = current_year

    # 上个月1日
    prev_month_start = date(prev_year, prev_month, 1)

    # 上个月的对应日期（避免日期不存在的问题）
    try:
        # 使用昨天的日期而不是今天的日期
        yesterday = today - timedelta(days=1)
        current_day = yesterday.day
        last_day_prev_month = calendar.monthrange(prev_year, prev_month)[1]
        prev_month_end_day = min(current_day, last_day_prev_month)
        prev_month_end = date(prev_year, prev_month, prev_month_end_day)
    except ValueError:
        # 如果日期不存在，使用上个月最后一天
        last_day = calendar.monthrange(prev_year, prev_month)[1]
        prev_month_end = date(prev_year, prev_month, last_day)

    return prev_month_start, prev_month_end


def get_week_range_excluding_today(weeks_ago: int = 0) -> Tuple[date, date]:
    """获取指定周数前的周范围，排除当天数据

    Args:
        weeks_ago: 几周前，0表示本周，1表示上周

    Returns:
        tuple: (周一, 周日) 或 (周一, 昨天)
    """
    today = date.today()
    days_since_monday = today.weekday()  # 0=周一, 6=周日

    # 计算目标周的周一
    this_week_monday = today - timedelta(days=days_since_monday)
    target_week_monday = this_week_monday - timedelta(days=weeks_ago * 7)
    target_week_sunday = target_week_monday + timedelta(days=6)

    if weeks_ago == 0:
        # 本周：排除当天数据，结束时间为昨天
        yesterday = today - timedelta(days=1)
        if target_week_sunday >= today:
            target_week_sunday = yesterday

        # 确保开始时间不超过结束时间
        if target_week_monday > target_week_sunday:
            target_week_monday = target_week_sunday

    return target_week_monday, target_week_sunday


def get_recent_days_excluding_today(days: int) -> Tuple[date, date]:
    """获取最近N天，排除当天数据

    Args:
        days: 天数

    Returns:
        tuple: (开始日期, 结束日期)
    """
    today = date.today()
    yesterday = today - timedelta(days=1)
    start_date = yesterday - timedelta(days=days - 1)

    return start_date, yesterday


def format_date_range(start_date: date, end_date: date) -> str:
    """格式化日期范围显示"""
    if start_date.year == end_date.year:
        if start_date.month == end_date.month:
            return f"{start_date.month}月{start_date.day}日-{end_date.day}日"
        else:
            return f"{start_date.month}月{start_date.day}日-{end_date.month}月{end_date.day}日"
    else:
        return f"{start_date.year}年{start_date.month}月{start_date.day}日-{end_date.year}年{end_date.month}月{end_date.day}日"


def get_week_description(start_date: date, end_date: date) -> str:
    """获取周描述"""
    return f"{start_date.year}年第{start_date.isocalendar()[1]}周"


def get_month_description(target_date: date) -> str:
    """获取月份描述"""
    return f"{target_date.year}年{target_date.month}月"


def is_same_week(date1: date, date2: date) -> bool:
    """判断两个日期是否在同一周（周一到周日）"""
    # 获取两个日期所在周的周一
    days_since_monday1 = date1.weekday()
    days_since_monday2 = date2.weekday()

    week_start1 = date1 - timedelta(days=days_since_monday1)
    week_start2 = date2 - timedelta(days=days_since_monday2)

    return week_start1 == week_start2


def is_same_month(date1: date, date2: date) -> bool:
    """判断两个日期是否在同一月"""
    return date1.year == date2.year and date1.month == date2.month


def get_weekday_name(target_date: date) -> str:
    """获取中文星期名称"""
    weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    return weekdays[target_date.weekday()]


def calculate_business_days(start_date: date, end_date: date) -> int:
    """计算工作日天数（周一到周五）"""
    current_date = start_date
    business_days = 0

    while current_date <= end_date:
        if current_date.weekday() < 5:  # 0-4为周一到周五
            business_days += 1
        current_date += timedelta(days=1)

    return business_days


def get_quarter_range(year: int, quarter: int) -> Tuple[date, date]:
    """获取季度范围

    Args:
        year: 年份
        quarter: 季度 (1-4)

    Returns:
        tuple: (季度开始日期, 季度结束日期)
    """
    if quarter == 1:
        start_month, end_month = 1, 3
    elif quarter == 2:
        start_month, end_month = 4, 6
    elif quarter == 3:
        start_month, end_month = 7, 9
    elif quarter == 4:
        start_month, end_month = 10, 12
    else:
        raise ValueError("季度必须是1-4之间的整数")

    start_date = date(year, start_month, 1)

    # 计算季度结束日期
    if end_month == 12:
        end_date = date(year, 12, 31)
    else:
        end_date = date(year, end_month + 1, 1) - timedelta(days=1)

    return start_date, end_date


def get_current_quarter() -> Tuple[int, int]:
    """获取当前季度

    Returns:
        tuple: (年份, 季度)
    """
    today = date.today()
    quarter = (today.month - 1) // 3 + 1
    return today.year, quarter


# 测试函数
if __name__ == "__main__":
    print("时间工具函数测试")
    print("=" * 50)

    # 测试自然周（修复版）
    this_week_start, this_week_end = get_current_natural_week()
    print(f"本周: {format_date_range(this_week_start, this_week_end)}")
    print(f"周描述: {get_week_description(this_week_start, this_week_end)}")

    last_week_start, last_week_end = get_last_natural_week()
    print(f"上一自然周: {format_date_range(last_week_start, last_week_end)}")

    # 测试月度（修复版）
    month_start, month_end = get_current_month_to_date()
    print(f"本月至今: {format_date_range(month_start, month_end)}")

    prev_month_start, prev_month_end = get_previous_month_same_period()
    print(f"上月同期: {format_date_range(prev_month_start, prev_month_end)}")

    # 测试新增功能
    print(f"\n新增功能测试:")

    # 测试周范围（排除当天）
    for i in range(3):
        week_start, week_end = get_week_range_excluding_today(i)
        week_name = "本周" if i == 0 else f"{i}周前"
        print(f"{week_name}: {format_date_range(week_start, week_end)}")

    # 测试最近N天（排除当天）
    for days in [7, 14, 30]:
        start, end = get_recent_days_excluding_today(days)
        print(f"最近{days}天: {format_date_range(start, end)}")

    # 测试季度功能
    current_year, current_quarter = get_current_quarter()
    print(f"当前季度: {current_year}年第{current_quarter}季度")

    quarter_start, quarter_end = get_quarter_range(current_year, current_quarter)
    print(f"季度范围: {format_date_range(quarter_start, quarter_end)}")

    # 测试星期名称
    today = date.today()
    print(f"今天是: {get_weekday_name(today)}")

    # 测试工作日计算
    week_start, week_end = get_current_natural_week()
    business_days = calculate_business_days(week_start, week_end)
    print(f"本周工作日: {business_days} 天")