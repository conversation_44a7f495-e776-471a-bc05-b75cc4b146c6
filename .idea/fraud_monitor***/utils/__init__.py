"""
APP涉案监控系统 - Utils模块初始化文件
"""

# 导入时间辅助工具
try:
    from .time_helpers import (
        get_last_natural_week,
        get_current_natural_week,
        get_current_month_to_date,
        get_previous_month_same_period,
        format_date_range,
        get_week_description,
        get_month_description
    )
except ImportError:
    # 如果时间辅助工具不可用，提供简单实现
    from datetime import date, timedelta


    def get_current_natural_week():
        today = date.today()
        days_since_monday = today.weekday()
        week_monday = today - timedelta(days=days_since_monday)
        week_sunday = week_monday + timedelta(days=6)
        return week_monday, week_sunday


    def format_date_range(start_date, end_date):
        return f"{start_date.strftime('%m月%d日')}-{end_date.strftime('%m月%d日')}"

__all__ = [
    'get_current_natural_week',
    'format_date_range'
]