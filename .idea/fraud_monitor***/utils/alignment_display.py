"""
APP涉案监控系统 - 修复版统一显示对齐工具
完全修复控制台列表显示对齐问题，确保完美对齐效果
"""
import unicodedata
from typing import List, Dict, Any, Optional
from config import DISPLAY_CONFIG


class AlignmentDisplay:
    """统一显示对齐工具类 - 修复版"""

    def __init__(self):
        self.table_width = DISPLAY_CONFIG['table_width']
        self.column_widths = DISPLAY_CONFIG['column_widths']
        self.encoding = DISPLAY_CONFIG['encoding']

    def get_display_width(self, text: str) -> int:
        """
        精确计算字符串的显示宽度（考虑中英文字符宽度差异）
        中文字符显示宽度为2，英文字符为1
        """
        if not text:
            return 0

        width = 0
        for char in str(text):
            # 检查字符是否为全角字符（包括中文、日文、韩文等）
            if unicodedata.east_asian_width(char) in ('F', 'W'):
                width += 2  # 全角字符占2个位置
            else:
                width += 1  # 半角字符占1个位置
        return width

    def pad_string(self, text: str, target_width: int, align: str = 'left') -> str:
        """
        将字符串填充到指定显示宽度 - 修复版

        Args:
            text: 要填充的文本
            target_width: 目标显示宽度
            align: 对齐方式 ('left', 'right', 'center')

        Returns:
            填充后的字符串
        """
        if not text:
            text = ""

        text = str(text)
        current_width = self.get_display_width(text)

        if current_width >= target_width:
            # 如果当前宽度超过目标宽度，需要截断
            return self.truncate_string(text, target_width)

        # 计算需要填充的空格数
        padding = target_width - current_width

        if align == 'left':
            return text + ' ' * padding
        elif align == 'right':
            return ' ' * padding + text
        elif align == 'center':
            left_padding = padding // 2
            right_padding = padding - left_padding
            return ' ' * left_padding + text + ' ' * right_padding
        else:
            return text + ' ' * padding

    def truncate_string(self, text: str, max_width: int, suffix: str = '..') -> str:
        """
        截断字符串到指定显示宽度 - 修复版

        Args:
            text: 要截断的文本
            max_width: 最大显示宽度
            suffix: 截断后的后缀

        Returns:
            截断后的字符串
        """
        if not text:
            return ""

        text = str(text)
        suffix_width = self.get_display_width(suffix)

        if max_width <= suffix_width:
            return suffix[:max_width]

        target_width = max_width - suffix_width
        current_width = 0
        result = ""

        for char in text:
            char_width = 2 if unicodedata.east_asian_width(char) in ('F', 'W') else 1
            if current_width + char_width > target_width:
                break
            result += char
            current_width += char_width

        if current_width < self.get_display_width(text):
            result += suffix

        return result

    def create_separator_line(self, width: int, char: str = '=') -> str:
        """创建分隔线"""
        return char * width

    def create_table_header(self, headers: List[str], widths: List[int]) -> str:
        """创建表格头部 - 修复版"""
        if len(headers) != len(widths):
            raise ValueError("Headers and widths must have the same length")

        header_parts = []
        for header, width in zip(headers, widths):
            padded_header = self.pad_string(header, width, 'center')
            header_parts.append(padded_header)

        return ' '.join(header_parts)

    def create_table_row(self, values: List[str], widths: List[int], alignments: Optional[List[str]] = None) -> str:
        """创建表格行 - 修复版"""
        if len(values) != len(widths):
            raise ValueError("Values and widths must have the same length")

        if alignments is None:
            alignments = ['left'] * len(values)
        elif len(alignments) != len(values):
            raise ValueError("Alignments and values must have the same length")

        row_parts = []
        for value, width, align in zip(values, widths, alignments):
            padded_value = self.pad_string(str(value), width, align)
            row_parts.append(padded_value)

        return ' '.join(row_parts)

    def print_aligned_table(self, data: List[Dict[str, Any]], headers: List[str],
                            widths: List[int], alignments: Optional[List[str]] = None,
                            show_index: bool = True, max_rows: Optional[int] = None):
        """
        打印完美对齐的表格 - 修复版

        Args:
            data: 数据列表，每个元素是包含行数据的字典
            headers: 表头列表
            widths: 各列宽度列表
            alignments: 各列对齐方式列表
            show_index: 是否显示序号
            max_rows: 最大显示行数
        """
        if not data:
            print("📋 无数据显示")
            return

        # 如果显示序号，需要调整headers和widths
        if show_index:
            headers = ['序号'] + headers
            widths = [self.column_widths['index']] + widths
            if alignments:
                alignments = ['right'] + alignments
            else:
                alignments = ['right'] + ['left'] * (len(headers) - 1)

        # 限制显示行数
        display_data = data[:max_rows] if max_rows else data

        # 计算总宽度
        total_width = sum(widths) + len(widths) - 1  # 加上空格分隔符的宽度

        # 打印表头
        print(self.create_table_header(headers, widths))
        print(self.create_separator_line(total_width, '='))

        # 打印数据行
        for i, row_data in enumerate(display_data, 1):
            row_values = []

            # 添加序号
            if show_index:
                row_values.append(str(i))

            # 添加数据列
            for header in headers[1:] if show_index else headers:
                value = row_data.get(header, row_data.get(header.lower(), ''))
                if value is None:
                    value = ''
                row_values.append(str(value))

            print(self.create_table_row(row_values, widths, alignments))

        # 如果有更多数据未显示，显示提示
        if max_rows and len(data) > max_rows:
            remaining = len(data) - max_rows
            print(f"\n... 还有 {remaining} 行数据未显示")


class AppListDisplay(AlignmentDisplay):
    """APP列表专用显示工具 - 修复版"""

    def print_new_apps_table(self, new_apps: List[Dict[str, Any]], max_display: int = 50):
        """打印新APP检测结果表格 - 修复对齐"""
        if not new_apps:
            print("✅ 未发现新APP")
            return

        print(f"📋 显示TOP{min(len(new_apps), max_display)}个新APP (按风险等级和案件数排序):")

        headers = ['APP名称', '风险等级', '案件数', '日均案件', '涉案金额(万)', '平均金额(万)', '地区数', '首次发现']
        widths = [28, 10, 8, 10, 14, 14, 8, 12]
        alignments = ['left', 'center', 'right', 'right', 'right', 'right', 'right', 'center']

        # 准备数据
        table_data = []
        for app in new_apps[:max_display]:
            risk_emoji = {'高': '🔴', '中': '🟡', '低': '🟢'}.get(app.get('risk_level', '低'), '⚪')
            daily_avg = app.get('case_count', 0) / max(app.get('days_active', 1), 1)

            # 首次发现时间格式化
            first_seen = app.get('first_seen')
            if hasattr(first_seen, 'strftime'):
                first_seen_str = first_seen.strftime('%m-%d')
            else:
                first_seen_str = str(first_seen) if first_seen else '未知'

            table_data.append({
                'APP名称': app.get('app_name', ''),
                '风险等级': f"{risk_emoji}{app.get('risk_level', '低')}",
                '案件数': app.get('case_count', 0),
                '日均案件': f"{daily_avg:.1f}",
                '涉案金额(万)': f"{app.get('total_amount', 0) / 10000:.2f}",
                '平均金额(万)': f"{app.get('avg_amount', 0) / 10000:.2f}",
                '地区数': app.get('area_count', 0),
                '首次发现': first_seen_str
            })

        self.print_aligned_table(table_data, headers, widths, alignments, max_rows=max_display)

        # 风险统计
        risk_stats = {'高': 0, '中': 0, '低': 0}
        for app in new_apps:
            risk_level = app.get('risk_level', '低')
            risk_stats[risk_level] = risk_stats.get(risk_level, 0) + 1

        print(
            f"\n📊 风险等级分布: 高风险 {risk_stats['高']}个 | 中风险 {risk_stats['中']}个 | 低风险 {risk_stats['低']}个")

    def print_growth_monitoring_table(self, growth_results: List[Dict[str, Any]], max_display: int = 20):
        """打印APP增长速度监控表格 - 修复对齐"""
        if not growth_results:
            print("✅ 未发现异常增长APP")
            return

        print(f"📈 发现 {len(growth_results)} 个异常增长APP:")

        headers = ['APP名称', '增长模式', '日增长率(%)', '加速度', '总案件数', '预警等级', '监控依据']
        widths = [28, 12, 12, 8, 10, 10, 35]
        alignments = ['left', 'center', 'right', 'right', 'right', 'center', 'left']

        # 准备数据
        table_data = []
        for app in growth_results[:max_display]:
            pattern = app.get('growth_pattern', 'stable')
            pattern_emoji = {'explosive': '💥', 'rapid': '🚀', 'steady': '📈', 'stable': '➡️'}.get(pattern, '❓')

            alert_level = app.get('alert_level', 'low')
            alert_emoji = {'critical': '🔴', 'high': '🟡'}.get(alert_level, '⚪')

            monitoring_basis = app.get('monitoring_basis', '')[:30]  # 截断过长的文本

            table_data.append({
                'APP名称': app.get('app_name', ''),
                '增长模式': f"{pattern_emoji}{pattern}",
                '日增长率(%)': f"{app.get('daily_growth_rate', 0):.1f}",
                '加速度': f"{app.get('acceleration', 0):.2f}",
                '总案件数': app.get('total_cases', 0),
                '预警等级': f"{alert_emoji}{alert_level}",
                '监控依据': monitoring_basis
            })

        self.print_aligned_table(table_data, headers, widths, alignments, max_rows=max_display)

    def print_explosion_prediction_table(self, predictions: List[Dict[str, Any]], max_display: int = 20):
        """打印APP爆发风险预测表格 - 修复对齐"""
        if not predictions:
            print("✅ 暂无高风险预测")
            return

        print(f"🎯 发现 {len(predictions)} 个爆发风险APP:")

        headers = ['APP名称', '爆发概率', '相似模式', '预测依据', '关键特征', '预测时间']
        widths = [28, 10, 12, 20, 18, 12]
        alignments = ['left', 'center', 'center', 'left', 'left', 'center']

        # 准备数据
        table_data = []
        for pred in predictions[:max_display]:
            prob_str = f"{pred.get('explosion_probability', 0):.1%}"

            table_data.append({
                'APP名称': pred.get('app_name', ''),
                '爆发概率': prob_str,
                '相似模式': pred.get('similar_pattern', '')[:10],
                '预测依据': pred.get('prediction_basis', '')[:17],
                '关键特征': pred.get('key_features', '')[:15],
                '预测时间': pred.get('predicted_timeframe', '')
            })

        self.print_aligned_table(table_data, headers, widths, alignments, max_rows=max_display)

    def print_suspicious_patterns_table(self, alerts: List[Dict[str, Any]], max_display: int = 50):
        """打印可疑模式检测表格 - 修复对齐"""
        if not alerts:
            print("✅ 未发现明显的可疑模式")
            return

        total_alerts = len(alerts)
        print(f"🚨 发现 {total_alerts} 个可疑模式，显示TOP{min(total_alerts, max_display)}个:")

        headers = ['类型', 'APP名称', '严重程度', '描述']
        widths = [12, 28, 10, 65]
        alignments = ['center', 'left', 'center', 'left']

        # 准备数据
        table_data = []
        for alert in alerts[:max_display]:
            severity = alert.get('severity', '低')
            severity_emoji = {'高': '🔴', '中': '🟡', '低': '🟢'}.get(severity, '⚪')

            description = str(alert.get('description', ''))
            if len(description) > 60:
                description = description[:57] + '..'

            table_data.append({
                '类型': alert.get('type', ''),
                'APP名称': alert.get('app_name', ''),
                '严重程度': f"{severity_emoji}{severity}",
                '描述': description
            })

        self.print_aligned_table(table_data, headers, widths, alignments, max_rows=max_display)

        # 统计信息
        high_alerts = len([alert for alert in alerts if alert.get('severity') == '高'])
        medium_alerts = len([alert for alert in alerts if alert.get('severity') == '中'])
        low_alerts = len([alert for alert in alerts if alert.get('severity') == '低'])

        print(f"\n📊 风险等级分布: 高风险 {high_alerts}个 | 中风险 {medium_alerts}个 | 低风险 {low_alerts}个")

        # 类型统计
        type_stats = {}
        for alert in alerts:
            alert_type = alert.get('type', '未知')
            type_stats[alert_type] = type_stats.get(alert_type, 0) + 1

        print(f"\n📈 预警类型分布:")
        for alert_type, count in sorted(type_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"   {alert_type}: {count}个")


class FinancialDisplay(AlignmentDisplay):
    """财务分析专用显示工具 - 修复版"""

    def print_top_apps_financial_table(self, app_stats: Dict[str, Any], max_display: int = 10):
        """打印TOP高损失APP表格 - 修复对齐"""
        if not app_stats:
            return

        print(f"\n🏆 TOP{max_display} 高损失APP:")

        headers = ['APP名称', '案件数', '总金额(万)', '平均金额(万)', '最高金额(万)']
        widths = [28, 10, 14, 14, 14]
        alignments = ['left', 'right', 'right', 'right', 'right']

        # 准备数据 - 从字典格式转换
        table_data = []
        app_items = list(app_stats.items())[:max_display]

        for app_name, stats in app_items:
            # 处理不同的数据结构
            if isinstance(stats, dict):
                case_count = stats.get('案件数', stats.get('count', 0))
                total_amount = stats.get('总金额', stats.get('total_amount', 0))
                avg_amount = stats.get('平均金额', stats.get('avg_amount', 0))
                max_amount = stats.get('最高金额', stats.get('max_amount', 0))
            else:
                # 如果只是数量统计
                case_count = stats
                total_amount = avg_amount = max_amount = 0

            table_data.append({
                'APP名称': app_name,
                '案件数': case_count,
                '总金额(万)': f"{total_amount / 10000:.2f}",
                '平均金额(万)': f"{avg_amount / 10000:.2f}",
                '最高金额(万)': f"{max_amount / 10000:.2f}"
            })

        self.print_aligned_table(table_data, headers, widths, alignments)


class RiskDisplay(AlignmentDisplay):
    """风险分析专用显示工具 - 修复版"""

    def print_app_risk_ranking_table(self, app_risks: List[Dict[str, Any]], max_display: int = 15):
        """打印APP风险排名表格 - 修复对齐"""
        if not app_risks:
            return

        print(f"🏆 TOP{max_display} 高风险APP:")

        headers = ['APP名称', '风险', '评分', '案件数', '日均', '地区']
        widths = [28, 8, 8, 10, 10, 8]
        alignments = ['left', 'center', 'right', 'right', 'right', 'right']

        # 准备数据
        table_data = []
        for app_risk in app_risks[:max_display]:
            risk_level = app_risk.get('risk_level', '低')
            risk_emoji = {'高': '🔴', '中': '🟡', '低': '🟢'}.get(risk_level, '⚪')

            table_data.append({
                'APP名称': app_risk.get('app_name', ''),
                '风险': risk_emoji,
                '评分': f"{app_risk.get('risk_score', 0):.1f}",
                '案件数': app_risk.get('case_count', 0),
                '日均': f"{app_risk.get('daily_avg', 0):.1f}",
                '地区': app_risk.get('area_count', 0)
            })

        self.print_aligned_table(table_data, headers, widths, alignments)


# 全局显示工具实例
app_display = AppListDisplay()
financial_display = FinancialDisplay()
risk_display = RiskDisplay()


def print_aligned_new_apps(new_apps: List[Dict[str, Any]], max_display: int = 50):
    """打印对齐的新APP列表"""
    app_display.print_new_apps_table(new_apps, max_display)


def print_aligned_growth_monitoring(growth_results: List[Dict[str, Any]], max_display: int = 20):
    """打印对齐的增长监控列表"""
    app_display.print_growth_monitoring_table(growth_results, max_display)


def print_aligned_explosion_prediction(predictions: List[Dict[str, Any]], max_display: int = 20):
    """打印对齐的爆发预测列表"""
    app_display.print_explosion_prediction_table(predictions, max_display)


def print_aligned_suspicious_patterns(alerts: List[Dict[str, Any]], max_display: int = 50):
    """打印对齐的可疑模式列表"""
    app_display.print_suspicious_patterns_table(alerts, max_display)


def print_aligned_financial_top_apps(app_stats: Dict[str, Any], max_display: int = 10):
    """打印对齐的财务TOP APP列表"""
    financial_display.print_top_apps_financial_table(app_stats, max_display)


def print_aligned_risk_ranking(app_risks: List[Dict[str, Any]], max_display: int = 15):
    """打印对齐的风险排名列表"""
    risk_display.print_app_risk_ranking_table(app_risks, max_display)