"""
APP涉案监控系统 - 优化版配置文件
集中管理所有系统配置参数，统一默认查询时间为7天
"""
from pathlib import Path
import warnings

# 禁用警告
warnings.filterwarnings('ignore')

# ================================
# 数据库配置
# ================================
DATABASE_CONFIG = {
    'host': '************',
    'port': 57861,
    'user': 'FzUser',
    'password': 'jZ4%fQ3}oI8(jH7)',
    'database': 'antiFraudPlatform',
    'charset': 'utf8mb4'
}

# 数据库连接池配置 - 优化版
CONNECTION_POOL_CONFIG = {
    'pool_size': 15,  # 增加连接池大小
    'max_overflow': 30,  # 增加最大溢出连接数
    'pool_timeout': 20,  # 减少获取连接超时时间
    'pool_recycle': 3600,  # 连接回收时间(1小时)
    'pool_pre_ping': True,  # 连接前ping测试
    'echo': False  # 不显示SQL日志
}

# ================================
# 文件路径配置
# ================================
# 缓存配置
CACHE_DIR = Path("../cache")
CACHE_FILE = CACHE_DIR / "advanced_fraud_cache.json"

# 导出配置
EXPORT_DIR = Path("/Users/<USER>/Documents/测试")

# 确保目录存在
CACHE_DIR.mkdir(exist_ok=True)
EXPORT_DIR.mkdir(exist_ok=True)

# ================================
# 时间查询统一配置 - 统一默认为7天
# ================================
TIME_QUERY_CONFIG = {
    'default_days': 7,  # 统一默认查询天数为7天
    'default_type': 'natural_week',  # 默认查询自然周
    'max_days_range': 365,  # 最大日期范围
    'allow_future_dates': False,  # 不允许未来日期
    'supported_formats': [
        '%Y-%m-%d',  # 2024-01-01
        '%Y/%m/%d',  # 2024/01/01
        '%m-%d',  # 01-01 (当年)
        '%m/%d'  # 01/01 (当年)
    ]
}

# ================================
# 查询配置 - 统一默认7天
# ================================
QUERY_CONFIG = {
    'default_days': 7,  # 统一默认查询天数为7天
    'page_size': 15000,  # 增大分页查询大小
    'max_similar_apps': 100,  # 增加相似APP检测最大数量
    'display_limit_default': 50,  # 默认显示限制
    'enable_query_optimization': True,  # 启用查询优化
    'use_covering_index': True,  # 使用覆盖索引
    'parallel_query_threads': 3,  # 并行查询线程数
}

# ================================
# 可视化配置
# ================================
# 中文字体设置
FONT_CONFIG = {
    'font.sans-serif': ['Microsoft YaHei', 'SimHei'],
    'axes.unicode_minus': False
}

# 配色方案
COLORS = {
    'primary': '#2E86AB',
    'secondary': '#A23B72',
    'accent': '#F18F01',
    'danger': '#C73E1D',
    'warning': '#F0BE38',
    'success': '#5C946E',
    'neutral': '#8B9DC3',
    'background': '#F8F9FA',
    'grid': '#E9ECEF',
    'text': '#2C3E50'
}

# 扩展颜色调色板 - 用于图表
COLOR_PALETTE = [
    '#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#5C946E',
    '#8B9DC3', '#F0BE38', '#7B68EE', '#20B2AA', '#FF6347',
    '#32CD32', '#FFD700', '#FF69B4', '#1E90FF', '#FF4500',
    '#9370DB', '#00CED1', '#FFA500', '#DC143C', '#00FA9A',
    '#4169E1', '#FF1493', '#00BFFF', '#FF6B6B', '#4ECDC4',
    '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'
]

# ================================
# 预警阈值配置 - 优化调整
# ================================
ALERT_THRESHOLDS = {
    'new_app_cases': 3,  # 降低新APP预警案件数阈值，提高敏感性
    'amount_spike': 2.5,  # 稍微提高金额异常倍数阈值
    'case_spike': 3.0,  # 案件数激增倍数阈值
    'high_risk_amount': 50000,  # 降低高风险金额阈值(5万)，更早发现风险
    'similarity_threshold': 0.75,  # 降低APP名称相似度阈值，发现更多变种
    'geographic_concentration': 0.25,  # 降低地区集中度阈值，提高敏感性
}

# ================================
# 数据库字段映射
# ================================
FIELD_MAPPING = {
    'case_number': '案件编号',
    'case_category': '案件类别',
    'case_main_type': '案件大类',
    'case_sub_type': '案件子类',
    'event_sequence': '事件顺序',
    'occurrence_time': '发案时间',
    'occurrence_area': '发案地区',
    'brief_case_description': '简要案情',
    'involved_amount': '涉案金额',
    'entry_start_time': '录入开始时间',
    'filing_time': '立案时间',
    'transfer_method': '转账方式',
    'victim_account_type': '受害人账号类型',
    'suspect_account_type': '嫌疑人账号类型',
    'suspect_account_name': '嫌疑人账户名',
    'suspect_account_number': '嫌疑人账号',
    'suspect_phone_number': '嫌疑人手机号',
    'suspect_qq_number': '嫌疑人QQ号',
    'suspect_wechat_number': '嫌疑人微信号',
    'suspect_other_contact': '嫌疑人其他联系方式',
    'suspect_service_account_type_name': '嫌疑人服务号类型名称',
    'suspect_other_contact_account': '嫌疑人其他联系账号',
    'service_account_url': '服务号网址链接',
    'service_account_company': '服务号所属公司',
    'service_account_app_name': '服务号APP名称',
    'service_account_app_download_url': '服务号APP下载链接',
    'website_url': '网址链接',
    'website_company': '网址所属公司',
    'app_name': 'APP名称',
    'app_download_url': 'APP下载链接',
    'app_company': 'APP所属公司',
    'contact_phone': '联系电话',
    'victim_age': '年龄',
    'url': '网址',
    'connection_relationship': '通联关系',
    'scammer_app_account': '诈骗分子APP账号',
    'victim_app_account': '受害人APP账号',
    'victim_bank_card_number': '受害人银行卡号',
    'sms_port_number': '短信端口号',
    'suspect_phone_info': '涉诈手机号运营商信息',
    'insert_day': '入库时间'
}

# ================================
# 数据处理配置
# ================================
# 年龄段分组配置
AGE_GROUPS = {
    'bins': [0, 18, 30, 40, 50, 60, 100],
    'labels': ['未成年(<18)', '青年(18-30)', '中年(30-40)', '中年(40-50)', '中老年(50-60)', '老年(60+)']
}

# 金额等级分组配置
AMOUNT_LEVELS = {
    'conditions_values': [10000, 100000, 500000, 1000000],
    'labels': ['小额(<1万)', '中额(1-10万)', '大额(10-50万)', '巨大额(50-100万)', '巨巨大额(≥100万)']
}

# 无效APP名称过滤模式 - 扩展版
INVALID_APP_PATTERNS = {
    'meaningless_words': [
        r'^无$', r'^wu$', r'^未知$', r'^unknown$', r'^null$',
        r'^其他$', r'^其它$', r'^不详$', r'^不明$', r'^暂无$', r'^待定$',
        r'^test$', r'^demo$', r'^temp$', r'^临时$', r'^测试$',
        r'^韩文$', r'^日文$', r'^符号$', r'^特殊符号$', r'^已删除$',
        r'^受害人无法提供$', r'^teams?$', r'^app$', r'^APP$',
        r'^韩文APP$', r'^约炮软件$', r'^色情.*$', r'^赌博.*$'
    ],
    'special_patterns': [
        r'^[?？]+$', r'^\*+$', r'^#+$', r'^[\.。]+$',
        r'^[-—]+$', r'^[/\\]+$', r'^\d+$', r'^[^\w\u4e00-\u9fff]{1,8}$'
    ]
}

# APP名称标准化映射 - 新增
APP_NAME_STANDARDIZATION = {
    # 大小写标准化
    'teams': 'Teams',
    'TEAMS': 'Teams',
    'qq': 'QQ',
    'QQ': 'QQ',
    'wechat': 'WeChat',
    'WECHAT': 'WeChat',
    'alipay': 'Alipay',
    'ALIPAY': 'Alipay',
    'taobao': 'Taobao',
    'TAOBAO': 'Taobao',
    # 相似名称合并
    '微信支付': '微信',
    'WeChat Pay': 'WeChat',
    '支付宝钱包': '支付宝',
    'Alipay Wallet': 'Alipay',
    # 英文中文对应
    'WeChat': '微信',
    'Alipay': '支付宝',
    'Taobao': '淘宝',
    'JD': '京东',
    'Meituan': '美团',
    'Didi': '滴滴',
    'Douyin': '抖音',
    'TikTok': '抖音'
}

# ================================
# 缓存配置 - 优化版
# ================================
CACHE_CONFIG = {
    'memory_cache_limit': 10,  # 增加内存缓存最大数量
    'cache_expire_hours': 2,  # 增加缓存过期时间
    'file_cache_days': 2,  # 增加文件缓存有效天数
    'enable_intelligent_cache': True,  # 启用智能缓存
    'cache_compression': True,  # 启用缓存压缩
}

# ================================
# 风险评估配置 - 调整权重
# ================================
RISK_ASSESSMENT = {
    'score_weights': {
        'new_apps': 35,  # 提高新APP风险权重
        'alerts': 35,  # 提高预警风险权重
        'financial': 20,  # 保持资金风险权重
        'case_volume': 10  # 保持案件量风险权重
    },
    'risk_levels': {
        'high': 65,  # 降低高风险阈值，提高敏感性
        'medium': 35,  # 降低中风险阈值
        'low': 15  # 降低低风险阈值
    },
    'app_risk_thresholds': {
        'daily_cases_high': 4,  # 降低日均案件数高风险阈值
        'daily_cases_medium': 2,  # 保持日均案件数中风险阈值
        'daily_amount_high': 30000,  # 降低日均金额高风险阈值
        'daily_amount_medium': 8000  # 降低日均金额中风险阈值
    }
}

# ================================
# 网络分析配置 - 优化参数
# ================================
NETWORK_ANALYSIS = {
    'similarity_thresholds': {
        'age_similarity': 0.75,  # 降低年龄相似度阈值
        'geo_similarity': 0.65,  # 降低地理相似度阈值
        'time_similarity': 0.75  # 降低时间模式相似度阈值
    },
    'detection_windows': {
        'time_burst_window': 1,  # 时间爆发检测窗口(天)
        'amount_similarity': 0.25,  # 增加金额相似度容差
        'geo_concentration_days': 5  # 缩短地理集中检测天数
    },
    'cluster_thresholds': {
        'min_cases_for_analysis': 3,  # 降低分析所需最小案件数
        'min_apps_for_cluster': 2,  # 保持集群最小APP数
        'min_networks_for_alert': 2  # 降低预警最小网络数
    }
}

# ================================
# 报告生成配置 - 优化版
# ================================
REPORT_CONFIG = {
    'html_template_config': {
        'include_plotlyjs': 'cdn',
        'full_html': False
    },
    'chart_config': {
        'default_height': 500,
        'default_width': 900,  # 增加默认宽度
        'top_items_count': 20  # 增加TOP榜单显示数量
    },
    'export_formats': ['html', 'json', 'pdf'],  # 增加PDF导出
    'max_display_items': {
        'alerts': 30,  # 增加预警显示数量
        'new_apps': 20,  # 增加新APP显示数量
        'clusters': 8,  # 增加集群显示数量
        'networks': 10,  # 增加网络显示数量
        'recommendations': 15  # 增加建议显示数量
    },
    'auto_save_report': True,  # 自动保存报告
    'report_compression': True  # 启用报告压缩
}

# ================================
# 数据库索引配置 - 性能优化版
# ================================
DATABASE_INDEXES = [
    # 原有基础索引
    ('idx_insert_day', 'anti_fraud_case_new', 'insert_day'),
    ('idx_app_name', 'anti_fraud_case_new', 'app_name'),
    ('idx_service_account_app_name', 'anti_fraud_case_new', 'service_account_app_name'),
    ('idx_involved_amount', 'anti_fraud_case_new', 'involved_amount'),
    ('idx_case_main_type', 'anti_fraud_case_new', 'case_main_type'),
    ('idx_occurrence_area', 'anti_fraud_case_new', 'occurrence_area'),

    # 优化的复合索引
    ('idx_comprehensive_analysis', 'anti_fraud_case_new',
     'insert_day, app_name, involved_amount, occurrence_area'),
    ('idx_financial_analysis', 'anti_fraud_case_new',
     'insert_day, involved_amount, app_name'),
    ('idx_pattern_detection', 'anti_fraud_case_new',
     'insert_day, app_name, case_main_type'),
    ('idx_geographic_analysis', 'anti_fraud_case_new',
     'insert_day, occurrence_area, app_name'),
    ('idx_victim_analysis', 'anti_fraud_case_new',
     'insert_day, victim_age, occurrence_area'),
    ('idx_network_analysis', 'anti_fraud_case_new',
     'insert_day, app_name, suspect_phone_number'),

    # 覆盖索引 - 减少回表查询
    ('idx_covering_main', 'anti_fraud_case_new',
     'insert_day, app_name, involved_amount, occurrence_area, victim_age, case_main_type'),

    # 专项优化索引
    ('idx_amount_filter', 'anti_fraud_case_new',
     'involved_amount, insert_day'),  # 金额过滤优化
    ('idx_app_time_amount', 'anti_fraud_case_new',
     'app_name, insert_day, involved_amount'),  # APP时间金额组合

    # 新增：APP名称表索引
    ('idx_app_name_create_time', 'anti_app_name', 'app_name, create_time'),
    ('idx_create_time', 'anti_app_name', 'create_time'),
]

# ================================
# 性能监控配置 - 新增
# ================================
PERFORMANCE_CONFIG = {
    'enable_monitoring': True,
    'slow_query_threshold': 5.0,  # 慢查询阈值(秒)
    'memory_alert_threshold': 500,  # 内存告警阈值(MB)
    'cache_hit_rate_target': 0.8,  # 缓存命中率目标
    'max_concurrent_queries': 5,  # 最大并发查询数
    'query_timeout': 30,  # 查询超时时间(秒)
}

# ================================
# 调试和日志配置
# ================================
DEBUG_CONFIG = {
    'enable_debug': False,
    'log_sql_queries': False,
    'log_performance': True,
    'verbose_output': False,  # 默认关闭详细输出
    'log_cache_operations': False,  # 缓存操作日志
    'enable_profiling': False,  # 性能分析
}

# ================================
# 系统常量
# ================================
SYSTEM_CONSTANTS = {
    'date_format': '%Y-%m-%d',
    'datetime_format': '%Y-%m-%d %H:%M:%S',
    'timezone': 'Asia/Shanghai',
    'encoding': 'utf-8',
    'version': '3.0',
    'system_name': 'APP涉案监控系统'
}

# ================================
# 图表配置 - 集成图表功能
# ================================
CHART_CONFIG = {
    'figure_size': {
        'trend_chart': (16, 10),
        'stacked_chart': (20, 12),
        'ranking_chart': (14, 16)
    },
    'dpi': 300,
    'save_format': 'png',
    'style_theme': 'modern',
    'show_values': True,
    'grid_alpha': 0.3,
    'legend_position': 'upper right'
}

# ================================
# 显示对齐配置 - 新增
# ================================
DISPLAY_CONFIG = {
    'table_width': 180,  # 表格总宽度
    'column_widths': {
        'index': 4,  # 序号列宽度
        'app_name': 25,  # APP名称列宽度
        'risk_level': 8,  # 风险等级列宽度
        'case_count': 8,  # 案件数列宽度
        'daily_avg': 10,  # 日均案件列宽度
        'amount': 12,  # 金额列宽度
        'description': 50  # 描述列宽度
    },
    'use_fixed_width_display': True,  # 启用固定宽度显示
    'encoding': 'utf-8'  # 显示编码
}

# 可疑检测阈值配置
SUSPICIOUS_DETECTION_CONFIG = {
    # 案件激增检测
    'case_spike': {
        'min_recent_cases': 5,  # 最近3天最少案件数
        'spike_multiplier': 2.0,  # 增长倍数阈值
        'high_risk_multiplier': 5.0,  # 高风险增长倍数
    },

    # 金额异常检测
    'amount_anomaly': {
        'high_amount_threshold': 500000,  # 高额案件阈值(50万)
        'min_high_amount_cases': 3,  # 最少高额案件数
        'critical_amount_threshold': 1000000,  # 极高额阈值(100万)
    },

    # 异常活跃检测
    'hyperactive': {
        'week_case_threshold': 50,  # 7天内案件数阈值
        'daily_high_risk_threshold': 20,  # 日均高风险阈值
    },

    # 时间集中爆发检测
    'time_burst': {
        'burst_period_hours': 24,  # 爆发检测时间窗口(小时)
        'burst_case_threshold': 10,  # 爆发案件数阈值
        'high_risk_burst_threshold': 20,  # 高风险爆发阈值
    },

    # 显示设置
    'display': {
        'max_high_risk_details': 5,  # 最多显示高风险详情数量
        'show_severity_stats': True,  # 显示严重程度统计
        'show_type_distribution': True,  # 显示类型分布
    }
}