import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import numpy as np
from datetime import datetime, timedelta
import warnings
import pymysql
import json
import os
from pathlib import Path
from tqdm import tqdm
import time
import sys
import requests
import re

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False


class TelecomMonitor:
    """运营商监控系统 - 电信专项版 (优化版)"""

    def __init__(self):
        self.connection = None
        self.is_connected = False

        # 风险阈值配置
        self.risk_config = {
            '电信': {'threshold': 2.5, 'high_risk': 5, 'color': '#0769B6'},
        }

        # 配色方案
        self.colors = {
            'primary': '#0769B6',
            'danger': '#E34234',
            'warning': '#F0BE38',
            'success': '#043474',
            'neutral': '#D7D1DC',
            'background': '#F8F9FA',
            'grid': '#E9ECEF',
            'text': '#2C3E50'
        }

        # 数据库配置
        self.db_config = {
            'host': '**************',
            'port': 3306,
            'user': 'FZUser',
            'password': 'fz@20250324',
            'database': 'antiFraudPlatform',
            'charset': 'utf8mb4'
        }

        # 缓存配置
        self.cache_dir = Path("cache")
        self.cache_dir.mkdir(exist_ok=True)
        self.cache_file = self.cache_dir / "fraud_cases_cache.json"

        # 图表导出配置
        self.export_dir = Path("/Users/<USER>/Documents/测试")
        self.export_dir.mkdir(exist_ok=True)

        # 分析选项配置
        self.analysis_options = {
            '1': {'name': '趋势分析', 'key': 'trend_analysis'},
            '2': {'name': '风险评估', 'key': 'risk_assessment'},
            '3': {'name': '异常检测', 'key': 'anomaly_detection'},
            '4': {'name': '案情特征分析', 'key': 'case_pattern_analysis'},
            '5': {'name': '综合分析', 'key': 'comprehensive_analysis'}
        }

        # 优化后的专业提示词模板配置
        self.prompt_templates = {
            'trend_analysis': """作为反诈专家，分析电信诈骗案件趋势：

        数据：{days}天内 {current_cases}起，日均{avg_cases:.1f}起，环比{growth_rate:+.1%}
        序列：{data_sequence}

        分析要点：
        1. 趋势方向：上升/下降/平稳，强度如何
        2. 异常识别：峰值、谷值、突变点
        3. 增长态势：加速/减速/稳定，持续性
        4. 预测预警：未来3-5天趋势，风险点

        要求：250-400字，数据支撑，突出关键发现""",

            'risk_assessment': """评估电信诈骗风险等级：

        当前：{current_cases}起（警告线{threshold}起，高风险线{high_risk_threshold}起）
        环比：{growth_rate:+.1%}，日均{daily_avg:.1f}起，标准差{std:.1f}

        评估要点：
        1. 风险定级：正常/警告/高风险，依据
        2. 关键指标：数量、增长率、波动性超标情况
        3. 趋势判断：短期风险、升级可能性
        4. 应对建议：监控调整、资源配置、预案启动

        要求：300-500字，明确结论，可操作建议""",

            'anomaly_detection': """检测数据异常：

        数据：{data_sequence}
        统计：均值{mean:.1f}，标准差{std:.1f}，异常阈值{anomaly_threshold:.1f}
        当前：{current_cases}起

        检测要点：
        1. 异常点：具体数值、时间、偏离程度
        2. 异常分级：轻微/中等/严重（1-2σ/2-3σ/3σ+）
        3. 异常模式：单点/连续、上升/下降
        4. 原因推断：数据质量/业务波动/系统风险
        5. 处理建议：验证、核实、调整、应急

        要求：400-600字，准确识别，深度分析""",

            'case_pattern_analysis': """分析案情特征模式：

        数据：{total_cases}起案件，{days}天，日均{daily_avg:.1f}起
        样本：{case_samples}

        注意：专注案情本身，忽略派出所、民警等数据源信息

        分析要点：
        1. 手法模式：诈骗流程、话术套路、目标选择
        2. 时序规律：作案时间、流程用时、报案延迟
        3. 受害画像：年龄、职业、地域、经济状况
        4. 资金特征：金额分布、转账方式、资金路径
        5. 技术手段：通讯工具、支付平台、身份伪装
        6. 防控要点：预警指标、防范措施、宣传重点

        要求：600-800字，挖掘规律，实战指导""",

            'comprehensive_analysis': """综合分析电信诈骗态势：

        数据：{days}天，{total_cases}起，当前{current_cases}起，环比{growth_rate:+.1%}
        序列：{data_sequence}
        阈值：警告{threshold}起，高风险{high_risk_threshold}起
        案例：{case_samples}

        分析框架：
        1. 总体态势：当前形势、同期对比、占比变化
        2. 数量趋势：变化方向、增长速度、波动特征
        3. 风险评估：等级判定、升级概率、关键因子
        4. 案情洞察：手法变化、新型特征、作案规律
        5. 关键发现：重要规律、异常现象、风险点
        6. 预警研判：短期风险、中期趋势、重点关注
        7. 行动建议：监控调整、防控优化、资源配置

        要求：500-700字，交叉分析，突出重点，决策支撑"""
        }

    def display_analysis_menu(self):
        """显示分析选项菜单"""
        print("\n" + "=" * 50)
        print("🤖 AI分析选项")
        print("=" * 50)
        print("请选择分析类型：\n")

        for key, option in self.analysis_options.items():
            print(f"【{key}】{option['name']}")

        print("\n💡 使用说明：")
        print("  - 单选：输入数字，如 '1'")
        print("  - 多选：用加号连接，如 '1+2+4'")
        print("  - 全选：输入 'all'")
        print("  - 退出：输入 'q'")
        print("=" * 50)

    def get_user_selection(self):
        """获取用户选择"""
        while True:
            try:
                user_input = input("\n🔍 请输入您的选择: ").strip()

                if user_input.lower() in ['exit', 'q', '退出']:
                    return None

                if user_input.lower() in ['all', '全部']:
                    return list(self.analysis_options.keys())

                # 处理多选输入
                selections = [s.strip() for s in user_input.split('+')] if '+' in user_input else [user_input]

                # 验证输入
                valid_selections = []
                for sel in selections:
                    if sel in self.analysis_options:
                        valid_selections.append(sel)
                    else:
                        print(f"❌ 无效选择: {sel}")
                        raise ValueError("输入无效")

                return valid_selections if valid_selections else None

            except (ValueError, KeyboardInterrupt):
                print("请输入有效的选择（如：1 或 1+3+4）")
                continue

    def format_prompt(self, template_name, **kwargs):
        """格式化提示词模板"""
        if template_name not in self.prompt_templates:
            return "模板不存在"

        try:
            return self.prompt_templates[template_name].format(**kwargs)
        except KeyError as e:
            return f"模板参数缺失：{e}"

    def prepare_analysis_data(self, df, case_details=None):
        """准备分析数据"""
        # 直接使用传入的参数，不重复获取数据
        if df is None or df.empty:
            print("❌ 无法获取数据，无法进行分析")
            return None

        telecom_data = df['电信'].tolist()
        mean_val = sum(telecom_data) / len(telecom_data)
        std_val = np.std(telecom_data) if len(telecom_data) > 1 else 0
        growth_rate = 0
        if len(telecom_data) > 1 and telecom_data[-2] > 0:
            growth_rate = (telecom_data[-1] - telecom_data[-2]) / telecom_data[-2]

        # 处理案情样本
        case_samples = "无案情数据"
        if case_details is not None and not case_details.empty:
            # 取前3个案例作为样本
            samples = []
            for _, row in case_details.head(3).iterrows():
                brief = row.get('简要案情', '无详情')[:50] + "..."
                event_seq = row.get('事件顺序', '无序列')[:30] + "..."
                samples.append(f"案例：{brief} | 事件序列：{event_seq}")
            case_samples = "; ".join(samples)

        analysis_data = {
            'days': len(df),
            'current_cases': telecom_data[-1],
            'avg_cases': mean_val,
            'total_cases': sum(telecom_data),
            'data_sequence': telecom_data,
            'mean': mean_val,
            'std': std_val,
            'growth_rate': growth_rate,
            'threshold': self.risk_config['电信']['threshold'],
            'high_risk_threshold': self.risk_config['电信']['high_risk'],
            'anomaly_threshold': mean_val + 2 * std_val,
            'daily_avg': mean_val,
            'case_samples': case_samples
        }

        print(f"✅ 数据准备完成: {len(telecom_data)}天数据，当前案件{telecom_data[-1]}起")
        return analysis_data

    def enhanced_api_analysis(self, df, case_details, analysis_type):
        """使用API进行分析"""
        try:
            # 准备数据
            data_params = self.prepare_analysis_data(df, case_details)
            if not data_params:
                return "数据不足，无法进行分析"

            # 生成提示词
            prompt = self.format_prompt(analysis_type, **data_params)
            if "模板不存在" in prompt or "模板参数缺失" in prompt:
                return f"提示词生成失败: {prompt}"

            # 显示进度
            stages = [("🔍 准备分析数据", 20), ("📊 生成提示词", 10), ("🤖 调用AI模型", 60), ("✨ 处理结果", 10)]
            total_progress = 0
            for stage_name, stage_weight in stages:
                print(f"{stage_name}...")
                for i in range(stage_weight):
                    sys.stdout.write(
                        f"\r总进度: [{'=' * (total_progress // 5)}{'.' * (20 - total_progress // 5)}] {total_progress}%")
                    sys.stdout.flush()
                    time.sleep(0.05)
                    total_progress += 1

            # 构建API请求数据 - 尝试多种格式
            system_prompt = "你是专业的反诈数据分析师，请基于提供的数据进行分析，给出专业、完整的分析结果。请确保输出完整的结论，不要截断。"

            # 尝试不同的API格式
            api_formats = [
                # 格式1: OpenAI风格
                {
                    "messages": [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.3,
                    "max_tokens": 2000,
                    "top_p": 0.9
                },
                # 格式2: 简单格式
                {
                    "prompt": f"{system_prompt}\n\n{prompt}",
                    "temperature": 0.3,
                    "max_tokens": 2000,
                    "top_p": 0.9
                },
                # 格式3: 另一种格式
                {
                    "input": prompt,
                    "system": system_prompt,
                    "temperature": 0.3,
                    "max_length": 2000
                }
            ]

            api_url = "http://**************:8050/generate"
            headers = {"Content-Type": "application/json"}

            result = None
            last_error = None

            # 尝试不同的API格式
            for i, api_data in enumerate(api_formats, 1):
                try:
                    print(f"🔄 尝试API格式 {i}/3...")
                    response = requests.post(api_url, json=api_data, headers=headers, timeout=60)

                    if response.status_code == 200:
                        result_data = response.json()

                        # 提取结果内容
                        if 'choices' in result_data and len(result_data['choices']) > 0:
                            result = result_data['choices'][0]['message']['content'].strip()
                        elif 'content' in result_data:
                            result = result_data['content'].strip()
                        elif 'response' in result_data:
                            result = result_data['response'].strip()
                        elif 'output' in result_data:
                            result = result_data['output'].strip()
                        elif 'text' in result_data:
                            result = result_data['text'].strip()
                        else:
                            # 如果是字符串直接返回
                            if isinstance(result_data, str):
                                result = result_data.strip()
                            else:
                                result = str(result_data).strip()

                        if result and len(result) > 50:  # 确保有有效内容
                            print(f"✅ API格式 {i} 成功")
                            break
                        else:
                            print(f"⚠️ API格式 {i} 返回内容过短")

                    else:
                        print(f"❌ API格式 {i} 失败: HTTP {response.status_code}")
                        last_error = f"HTTP {response.status_code}: {response.text[:200]}"

                except Exception as e:
                    print(f"❌ API格式 {i} 异常: {str(e)[:100]}")
                    last_error = str(e)
                    continue

            if not result:
                return f"❌ 所有API格式都失败了。最后错误: {last_error}"

            # 更精确的清理正则表达式
            # 清理 <think> 标签及其内容
            result = re.sub(r'<think>.*?</think>', '', result, flags=re.DOTALL | re.IGNORECASE)
            # 清理思考过程
            result = re.sub(r'思考[：:][^。]*。?', '', result)
            result = re.sub(r'让我[分析思考][^。]*。?', '', result)
            # 清理多余的换行
            result = re.sub(r'\n\s*\n', '\n', result).strip()

            # 如果结果太短，可能是被截断了
            if len(result) < 100:
                return f"⚠️ 分析结果可能不完整，建议重新尝试。当前结果：\n{result}"

            return result

        except requests.exceptions.ConnectionError:
            return "❌ 无法连接到API服务器，请检查网络连接和API地址"
        except requests.exceptions.Timeout:
            return "❌ API请求超时，请稍后重试"
        except Exception as e:
            return f"❌ 分析失败：{e}"

    def generate_report(self, df, case_details, analysis_results, selected_options):
        """生成报告"""
        print("\n" + "=" * 70)
        print("📊 中国电信风险监控报告")
        print("=" * 70)

        if df is None or df.empty:
            print("⚠️ 无数据可生成报告")
            return

        # 基本信息
        latest_data = df.iloc[-1]
        print(f"📅 报告日期: {latest_data['日期'].strftime('%Y-%m-%d')}")
        print(f"📊 数据周期: {len(df)}天")
        print(f"📈 当前案件: {int(latest_data['电信'])}起")
        print(f"💰 统计口径: 涉案金额 ≥ 50万元")

        # 案情数据概况
        if case_details is not None and not case_details.empty:
            print(f"📋 案情样本: {len(case_details)}起")

        # 显示选择的分析类型
        selected_names = [self.analysis_options[opt]['name'] for opt in selected_options]
        print(f"🎯 分析范围: {' + '.join(selected_names)}")

        # 输出分析结果
        print("\n🤖 AI分析结果:")
        print("-" * 70)

        for i, (analysis_name, result) in enumerate(analysis_results.items(), 1):
            print(f"\n【{i}. {analysis_name}】")
            print(result)
            print("-" * 50)

        # 技术指标
        analysis = self.analyze_data(df)
        print("\n📋 技术指标:")
        for operator, info in analysis.items():
            status_text = []
            if info['is_high_risk']:
                status_text.append("🚨 高风险")
            elif info['is_warning']:
                status_text.append("🟡 警告")
            else:
                status_text.append("🟢 正常")

            if info['is_high_growth']:
                status_text.append("📈 高增长")
            if info['consecutive_growth']:
                status_text.append("🔺 连续增长")

            print(f"  {operator}: {int(info['current'])}起 ({info['growth_rate']:+.1%}) - {' | '.join(status_text)}")

        print("=" * 70)

    def connect_database(self):
        """连接数据库"""
        try:
            if self.is_connected and self.connection:
                return True

            self.connection = pymysql.connect(**self.db_config)
            self.is_connected = True
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            self.is_connected = False
            return False

    def get_data(self, days=30, use_cache=True):
        """获取数据，支持缓存机制"""
        # 检查缓存
        if use_cache and self.cache_file.exists():
            try:
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)

                cache_date = cache_data.get('cache_date')
                today = datetime.now().strftime('%Y-%m-%d')

                if cache_date == today:
                    print("📦 使用缓存数据")
                    df = pd.DataFrame(cache_data['data'])
                    case_details = pd.DataFrame(cache_data.get('case_details', []))
                    if not df.empty:
                        df['日期'] = pd.to_datetime(df['日期'])
                    return df, case_details
            except Exception:
                pass

        # 查询数据库
        df, case_details = self._query_database(days)

        # 保存缓存
        if use_cache and df is not None and not df.empty:
            try:
                cache_data = {
                    'cache_date': datetime.now().strftime('%Y-%m-%d'),
                    'data': df.to_dict('records'),
                    'case_details': case_details.to_dict('records') if case_details is not None else []
                }
                with open(self.cache_file, 'w', encoding='utf-8') as f:
                    json.dump(cache_data, f, ensure_ascii=False, indent=2, default=str)
                print("💾 数据已缓存")
            except Exception:
                pass

        return df, case_details

    def _query_database(self, days=30):
        """查询数据库 - 增强版，包含案情详情"""
        if not self.is_connected and not self.connect_database():
            return None, None

        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

            # 查询统计数据
            query = f"""
            SELECT 
                DATE(insert_day) as date_col,
                SUM(CASE WHEN suspect_phone_info LIKE '%电信%' THEN 1 ELSE 0 END) as telecom
            FROM anti_fraud_case_new 
            WHERE DATE(insert_day) BETWEEN '{start_date}' AND '{end_date}'
              AND involved_amount >= 500000
            GROUP BY DATE(insert_day)
            ORDER BY DATE(insert_day)
            """

            df = pd.read_sql(query, self.connection)

            # 查询案情详情
            case_query = f"""
            SELECT 
                DATE(insert_day) as case_date,
                brief_case_description as 简要案情,
                event_sequence as 事件顺序,
                victim_age as 年龄,
                case_sub_type as 案件子类,
                involved_amount,
                suspect_phone_info
            FROM anti_fraud_case_new 
            WHERE DATE(insert_day) BETWEEN '{start_date}' AND '{end_date}'
              AND involved_amount >= 500000
              AND suspect_phone_info LIKE '%电信%'
            ORDER BY insert_day DESC
            LIMIT 50
            """

            case_details = pd.read_sql(case_query, self.connection)

            if not df.empty:
                df = df.rename(columns={'date_col': '日期', 'telecom': '电信'})
                df['日期'] = pd.to_datetime(df['日期'])
                print(f"✅ 查询成功，共 {len(df)} 条统计记录，{len(case_details)} 条案情详情")
            else:
                print("⚠️ 查询结果为空")

            return df, case_details

        except Exception as e:
            print(f"❌ 查询失败: {e}")
            return None, None

    def analyze_data(self, df):
        """数据分析"""
        if df is None or df.empty:
            return {}

        analysis = {}
        for operator in df.columns:
            if operator == '日期' or operator not in self.risk_config:
                continue

            values = df[operator].values
            current = int(values[-1]) if len(values) > 0 else 0
            previous = int(values[-2]) if len(values) > 1 else 0
            growth_rate = (current - previous) / previous if previous > 0 else 0

            consecutive_growth = False
            if len(values) >= 3:
                last_three = values[-3:]
                consecutive_growth = all(last_three[i] < last_three[i + 1] for i in range(len(last_three) - 1))

            config = self.risk_config[operator]
            analysis[operator] = {
                'current': current,
                'previous': previous,
                'growth_rate': growth_rate,
                'consecutive_growth': consecutive_growth,
                'is_high_risk': current >= config['high_risk'],
                'is_high_growth': growth_rate > 0.05,
                'is_warning': current >= config['threshold']
            }

        return analysis

    def selective_analysis(self, df, case_details, selected_options):
        """根据用户选择进行分析"""
        if not selected_options or df is None or df.empty:
            return {}

        results = {}
        print(f"\n🔍 开始执行 {len(selected_options)} 项分析...")
        print("-" * 50)

        for i, option_num in enumerate(selected_options, 1):
            if option_num not in self.analysis_options:
                continue

            option_info = self.analysis_options[option_num]
            analysis_name = option_info['name']
            analysis_key = option_info['key']

            print(f"[{i}/{len(selected_options)}] 正在进行{analysis_name}...")

            try:
                result = self.enhanced_api_analysis(df, case_details, analysis_key)
                results[analysis_name] = result
                print(f"✅ {analysis_name} 完成")
            except Exception as e:
                results[analysis_name] = f"分析异常：{str(e)}"
                print(f"❌ {analysis_name} 异常: {e}")

        return results

    def export_chart(self, filename=None):
        """导出图表到文件"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"电信(>50w)涉诈案情统计_{timestamp}.png"

        filepath = self.export_dir / filename

        try:
            plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
            print(f"📁 图表已保存到: {filepath}")
            return True
        except Exception as e:
            print(f"❌ 图表保存失败: {e}")
            return False

    def add_risk_markers(self, ax, x_data, y_data, operator):
        """在高风险数据点上添加红色标记"""
        high_risk_threshold = self.risk_config[operator]['high_risk']
        for x, y in zip(x_data, y_data):
            if y >= high_risk_threshold:
                ax.scatter(x, y, s=120, c=self.colors['danger'], marker='o',
                           alpha=0.9, edgecolors='white', linewidths=2, zorder=15)

    def create_chart(self, df, analysis, save_chart=False):
        """创建监控图表"""
        if df is None or df.empty:
            print("⚠️ 无数据可绘制图表")
            return

        fig, ax = plt.subplots(figsize=(16, 9))
        fig.patch.set_facecolor(self.colors['background'])

        # 绘制趋势线
        for operator in df.columns:
            if operator == '日期' or operator not in self.risk_config:
                continue

            x_data = df['日期']
            y_data = df[operator]

            # 绘制主趋势线
            ax.plot(x_data, y_data, color=self.colors['primary'], linewidth=4.0,
                    label=operator, marker='o', markersize=8)
            ax.fill_between(x_data, y_data, alpha=0.15, color=self.colors['primary'])

            # 添加风险标记
            self.add_risk_markers(ax, x_data, y_data, operator)

            # 绘制风险阈值线
            config = self.risk_config[operator]
            ax.axhline(y=config['threshold'], color=self.colors['warning'],
                       linestyle='--', alpha=0.8, linewidth=2,
                       label=f'{operator}警告阈值({config["threshold"]})')
            ax.axhline(y=config['high_risk'], color=self.colors['danger'],
                       linestyle='-.', alpha=0.9, linewidth=2.5,
                       label=f'{operator}高风险阈值({config["high_risk"]})')

        # 美化图表
        ax.set_title('电信涉诈案件监控图表(涉案金额 ≥ 50万元)',
                     fontsize=24, fontweight='bold', pad=20, color=self.colors['text'])
        ax.set_xlabel('日期', fontsize=14, color=self.colors['text'])
        ax.set_ylabel('案件数量', fontsize=14, color=self.colors['text'])

        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=3))

        plt.setp(ax.xaxis.get_majorticklabels(), rotation=0, ha='center', fontsize=18, color=self.colors['text'])
        if not df.empty:
            max_val = df.iloc[:, 1:].max().max()
            if max_val > 0:
                ax.set_ylim(0, max_val + 1)
        plt.setp(ax.yaxis.get_majorticklabels(), fontsize=18, color=self.colors['text'])

        ax.grid(True, alpha=0.3, color=self.colors['grid'], linewidth=0.8)
        ax.set_facecolor(self.colors['background'])

        legend = ax.legend(loc='upper right', frameon=True, fancybox=True, shadow=True,
                           fontsize=14, markerscale=1.5, handlelength=2.5, handletextpad=0.8)
        legend.get_frame().set_facecolor('white')
        legend.get_frame().set_alpha(0.95)
        legend.get_frame().set_linewidth(1.2)

        for spine in ax.spines.values():
            spine.set_visible(False)

        plt.tight_layout()

        if save_chart:
            self.export_chart()

        plt.show()

    def clear_cache(self):
        """清除缓存"""
        try:
            if self.cache_file.exists():
                self.cache_file.unlink()
                print("🗑️ 缓存已清除")
        except Exception as e:
            print(f"❌ 清除缓存失败: {e}")

    def close_connection(self):
        """关闭数据库连接"""
        if self.is_connected and self.connection:
            try:
                if self.connection.open:
                    self.connection.close()
                    print("🔒 数据库连接已关闭")
            except Exception:
                pass
            finally:
                self.is_connected = False
                self.connection = None

    def display_chart(self):
        """显示图表"""
        print("\n📊 正在生成最近30天的监控图表...")
        df, _ = self.get_data(30)
        if df is None or df.empty:
            print("❌ 无法获取数据")
            return
        analysis = self.analyze_data(df)
        self.create_chart(df, analysis, save_chart=False)

    def save_chart_only(self):
        """保存图表"""
        print("\n💾 正在生成并保存图表...")
        df, _ = self.get_data(30)
        if df is None or df.empty:
            print("❌ 无法获取数据")
            return

        analysis = self.analyze_data(df)
        fig, ax = plt.subplots(figsize=(16, 9))
        fig.patch.set_facecolor(self.colors['background'])

        # 绘制图表（简化版）
        for operator in df.columns:
            if operator == '日期' or operator not in self.risk_config:
                continue

            x_data = df['日期']
            y_data = df[operator]
            ax.plot(x_data, y_data, color=self.colors['primary'], linewidth=4.0,
                    label=operator, marker='o', markersize=8)
            ax.fill_between(x_data, y_data, alpha=0.15, color=self.colors['primary'])
            self.add_risk_markers(ax, x_data, y_data, operator)

            config = self.risk_config[operator]
            ax.axhline(y=config['threshold'], color=self.colors['warning'],
                       linestyle='--', alpha=0.9, linewidth=2.5,
                       label=f'{operator}高风险阈值({config["high_risk"]})')
        # 美化图表
        ax.set_title('电信涉诈案件监控图表(涉案金额 ≥ 50万元)',
                     fontsize=24, fontweight='bold', pad=20, color=self.colors['text'])
        ax.set_xlabel('日期', fontsize=14, color=self.colors['text'])
        ax.set_ylabel('案件数量', fontsize=14, color=self.colors['text'])

        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=3))

        plt.setp(ax.xaxis.get_majorticklabels(), rotation=0, ha='center', fontsize=18, color=self.colors['text'])
        if not df.empty:
            max_val = df.iloc[:, 1:].max().max()
            if max_val > 0:
                ax.set_ylim(0, max_val + 1)
        plt.setp(ax.yaxis.get_majorticklabels(), fontsize=18, color=self.colors['text'])

        ax.grid(True, alpha=0.3, color=self.colors['grid'], linewidth=0.8)
        ax.set_facecolor(self.colors['background'])

        legend = ax.legend(loc='upper right', frameon=True, fancybox=True, shadow=True,
                           fontsize=14, markerscale=1.5, handlelength=2.5, handletextpad=0.8)
        legend.get_frame().set_facecolor('white')
        legend.get_frame().set_alpha(0.95)
        legend.get_frame().set_linewidth(1.2)

        for spine in ax.spines.values():
            spine.set_visible(False)

        plt.tight_layout()

        # 保存图表
        if self.export_chart():
            plt.close(fig)  # 保存后关闭图形，避免显示
        else:
            plt.close(fig)

    def run_ai_analysis(self):
        """运行AI分析功能"""
        print("\n🤖 启动AI分析模块...")

        # 获取数据
        df, case_details = self.get_data(30)
        if df is None or df.empty:
            print("❌ 无法获取数据，无法进行分析")
            return

        while True:
            try:
                # 显示菜单
                self.display_analysis_menu()

                # 获取用户选择
                selected_options = self.get_user_selection()
                if selected_options is None:
                    print("👋 退出AI分析")
                    break

                # 执行分析
                analysis_results = self.selective_analysis(df, case_details, selected_options)

                # 生成报告
                self.generate_report(df, case_details, analysis_results, selected_options)

                # 询问是否继续
                continue_choice = input("\n❓ 是否继续分析？(y/n): ").strip().lower()
                if continue_choice not in ['y', 'yes', '是', '']:
                    print("👋 退出AI分析")
                    break

            except KeyboardInterrupt:
                print("\n👋 用户中断，退出AI分析")
                break
            except Exception as e:
                print(f"❌ 分析过程出错: {e}")
                continue

    def run(self):
        """运行主程序"""
        print("🚀 电信涉诈案件监控系统启动")
        print("=" * 50)

        try:
            while True:
                print("\n📋 请选择功能:")
                print("1️⃣  显示监控图表")
                print("2️⃣  保存图表到文件")
                print("3️⃣  AI智能分析")
                print("4️⃣  清除缓存")
                print("5️⃣  退出系统")
                print("-" * 30)

                choice = input("🔍 请输入选择 (1-5): ").strip()

                if choice == '1':
                    self.display_chart()
                elif choice == '2':
                    self.save_chart_only()
                elif choice == '3':
                    self.run_ai_analysis()
                elif choice == '4':
                    self.clear_cache()
                elif choice == '5':
                    print("👋 正在退出系统...")
                    break
                else:
                    print("❌ 无效选择，请重新输入")

        except KeyboardInterrupt:
            print("\n👋 用户中断，系统退出")
        except Exception as e:
            print(f"❌ 系统运行异常: {e}")
        finally:
            self.close_connection()
            print("✅ 系统已安全退出")


def main():
    """主函数"""
    monitor = TelecomMonitor()
    monitor.run()


if __name__ == "__main__":
    main()