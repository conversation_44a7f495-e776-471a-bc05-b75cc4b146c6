from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH,WD_PARAGRAPH_ALIGNMENT
import io
from flask import send_file
from docx.shared import Pt, Cm
from docx.oxml.ns import qn
from docx.shared import RGBColor
import ast
import json
import matplotlib.pyplot as plt
import io
from collections import defaultdict

class WordReportUtils:
    @staticmethod
    def generate_case_analysis_report(data,file_stream):
        """
        生成案情分析报告 Word 文件
        :param data: 包含案件数据的字典，必须包含以下键：
                     date_range, overall_situation, case_distribution_desc,
                     case_types, app_case_desc, app_top_list, loss_distribution
        :return: BytesIO 对象，包含 Word 文件
        """
        try:
            doc = Document()
            # 设置默认样式（正文）
            style = doc.styles['Normal']
            font = style.font
            font.name = '仿宋_GB2312'
            font.size = Pt(12)  # 3号字体大小（pt）
            font.color.rgb = RGBColor(0, 0, 0)
            style._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋_GB2312')
            style.paragraph_format.first_line_indent = Cm(0.67)
            # 设置标题样式：Heading 1 和 Heading 2
            for heading_style in ['Heading 1', 'Heading 2']:
                h_style = doc.styles[heading_style]
                h_font = h_style.font
                h_font.name = '黑体'
                h_font.size = Pt(15)  # 可选：设置标题1字号，如小二（约20pt）
                h_font.color.rgb = RGBColor(0, 0, 0)
                h_style._element.rPr.rFonts.set(qn('w:eastAsia'), '黑体')
            # 添加标题
            title = doc.add_heading('案情分析日报', level=1)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER

            for run in title.runs:
                run.font.name = 'SimSun'  # 字体为宋体
                run.font.size = Pt(20)  # 字号更大，例如20号
                run.font.bold = True  # 加粗
                run.font.color.rgb = RGBColor(0, 0, 0)  # 红色字体
                run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimSun')  # 强


            # 添加日期范围段落
            # 案情总体形势
            title_two = doc.add_heading('一、案情总体形势', level=2)
            for run in title_two.runs:
                run.font.name = 'SimSun'  # 字体为宋体
                run.font.size = Pt(15)  # 字号更大，例如20号
                run.font.bold = True  # 加粗
                run.font.color.rgb = RGBColor(0, 0, 0)  # 红色字体
                run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimSun')  # 强

            height_data = data.get("phone_height_1000", [])
            length = len(height_data)
            result = "，".join([item["case_number"] for item in height_data])

            WordReportUtils._add_paragraph(doc,data.get("date_range","") +"共接收案情"+data.get("case_count", "0")+"条，"
           "案情涉案总金额"+data.get("loss_amount", "")+"亿元。本日案情"+data.get("case_count", "0")+"条，"
           "环比昨日"+data.get("day_hb","")+"%。本月累计案情"+data.get("mon_count", "0")+"条，环比上月"+data.get("mon_hb","")+"%。"
           "中国电信手机号"+data.get("dx_sa_count","")+"个，环比昨日"+data.get("dx_hb_count","")+"%。"
            f"涉案金额大于1000万以上案件{length}起{f'，案件编号：{result}' if length > 0 else '。'}")

            WordReportUtils._add_paragraph(doc,"【涉诈APP情况】今日录入涉诈APP总数"+data.get("app_now_count","")+"，涉案"+data.get("app_total","")+"起，"
            "金额"+data.get("app_now_money","")+"万元，"+data.get("add_app_str","")+"。")

            title_th = doc.add_heading('二、分布情况', level=2)
            for run in title_th.runs:
                run.font.name = 'SimSun'  # 字体为宋体
                run.font.size = Pt(15)  # 字号更大，例如20号
                run.font.bold = True  # 加粗
                run.font.color.rgb = RGBColor(0, 0, 0)  # 红色字体
                run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimSun')  # 强

            WordReportUtils._add_paragraph(doc,"1、涉案类型。其中url类涉案"+data.get("url_count","")+"起"
            "；APP类涉案"+data.get("app_count","")+"起；"
            "电话、短信引流"+data.get("sms_count","")+"起；"
            "国际号码涉案"+data.get("intel_count","")+"起，106开头行短端口涉案"+data.get("international_count","")+"起。")
            # 案件分布类型
            WordReportUtils._add_paragraph(doc, "2、运营商涉案手机号码：本日运营商涉案手机记录"+data.get("phone_total","")+"个，电信"+data.get("tele_phone_total","")+"个（占比"+data.get("tele_phone_proportion","")+"%），本月累计"+data.get("dx_month_count","")+"起（占比"+data.get("dx_month_round","")+"%）；"
                "联通"+data.get("unicom_phone_total","")+"个（占比"+data.get("unicom_phone_proportion","")+"%）；"
                "移动"+data.get("move_phone_total","")+"个（占比"+data.get("move_phone_proportion","")+"%）；"
                "广电"+data.get("sva_phone_total","")+"个（占比"+data.get("sva_phone_proportion","")+"%）；"
                "虚商"+data.get("vc_phone_total","")+ "个（占比"+data.get("vc_phone_proportion","")+"%）；")


            # 设置中文字体支持
            plt.rcParams['font.sans-serif'] = ['SimHei']
            plt.rcParams['axes.unicode_minus'] = False

            # 创建折线图
            plt.figure()
            plt.plot(data.get("dx_x_data",[]), data.get("dx_y_data",[]),label='电信涉案数量', marker='o')
            plt.plot(data.get("yd_x_data", []), data.get("yd_y_data", []), label='移动涉案数量', marker='o')
            plt.plot(data.get("lt_x_data", []), data.get("lt_y_data", []), label='联通涉案数量', marker='o')
            plt.plot(data.get("gd_x_data", []), data.get("gd_y_data", []), label='广电涉案数量', marker='o')
            plt.plot(data.get("xs_x_data", []), data.get("xs_y_data", []), label='虚商涉案数量', marker='o')
            plt.title('涉案数量趋势图')
            plt.xlabel('日期')
            plt.ylabel('涉案数量')
            plt.xticks(rotation=45)  # 旋转X轴标签避免重叠
            plt.tight_layout()  # 自动调整布局
            plt.legend()
            plt.grid(True)

            # 将图像保存到 BytesIO 对象
            image_stream = io.BytesIO()
            plt.savefig(image_stream, format='png')
            plt.close()
            image_stream.seek(0)

            # 在指定段落前插入图像
            doc.add_picture(image_stream, width=Cm(12))  # 设置图像宽度为 12 厘米

            WordReportUtils._add_paragraph(doc,"3、短信端口涉案：行业短信端口涉案共"+data.get("international_count","")+"起,本月累计"+data.get("international_count_mon","")+"起。")


            WordReportUtils._add_paragraph(doc,"4、50万以上案情"+data.get("case_50_up","")+"起,运营商相关"+data.get("operator_50_up","")+"起，其中电信"+data.get("operator_dx_50_up","")+"起。")

            title_th = doc.add_heading('三、重点监测', level=2)
            for run in title_th.runs:
                run.font.name = 'SimSun'  # 字体为宋体
                run.font.size = Pt(15)  # 字号更大，例如20号
                run.font.bold = True  # 加粗
                run.font.color.rgb = RGBColor(0, 0, 0)  # 红色字体
                run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimSun')  # 强

            WordReportUtils._add_paragraph(doc,"1."+data.get("key_monitor_data","")+"涉诈案件"+data.get("key_monitor_count","")+"起。")



            title_th = doc.add_heading('四、APP涉案分析', level=2)
            for run in title_th.runs:
                run.font.name = 'SimSun'  # 字体为宋体
                run.font.size = Pt(15)  # 字号更大，例如20号
                run.font.bold = True  # 加粗
                run.font.color.rgb = RGBColor(0, 0, 0)  # 红色字体
                run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimSun')  # 强

            WordReportUtils._add_paragraph(doc, "1.趋势分析：本日案件APP涉案"+data.get("app_total","")+"起，"+data.get("app_list_top10_str",""))
            WordReportUtils._add_paragraph(doc, "2."+data.get("app_str_list","")+"")
            WordReportUtils._add_app_top_ten_table(doc, data.get("app_list_top10",[]))
            title_th = doc.add_heading('五、重大案件分析', level=2)
            for run in title_th.runs:
                run.font.name = 'SimSun'  # 字体为宋体
                run.font.size = Pt(15)  # 字号更大，例如20号
                run.font.bold = True  # 加粗
                run.font.color.rgb = RGBColor(0, 0, 0)  # 红色字体
                run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimSun')  # 强

            WordReportUtils._add_paragraph(doc, WordReportUtils.build_case_amount_description_with_phones(data))

            WordReportUtils._add_paragraph(doc, WordReportUtils.build_case_amount_description_with_phones_lt(data))

            WordReportUtils._add_paragraph(doc, WordReportUtils.build_case_amount_description_with_phones_yd(data))

            WordReportUtils._add_paragraph(doc, WordReportUtils.build_case_amount_description_with_phones_gd(data))

            WordReportUtils._add_paragraph(doc, WordReportUtils.build_case_amount_description_with_phones_xs(data))
            WordReportUtils._phone_province_table(doc,data)
            title_th = doc.add_heading('六、案件分布类型', level=2)
            for run in title_th.runs:
                run.font.name = 'SimSun'  # 字体为宋体
                run.font.size = Pt(15)  # 字号更大，例如20号
                run.font.bold = True  # 加粗
                run.font.color.rgb = RGBColor(0, 0, 0)  # 红色字体
                run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimSun')  # 强
            WordReportUtils._add_paragraph(doc, "本日案件高发的5种案件类型分别为；" + data.get("case_distribution_desc", ""))

            # title_th = doc.add_heading('三、邮件附件目录', level=2)
            # for run in title_th.runs:
            #     run.font.name = 'SimSun'  # 字体为宋体
            #     run.font.size = Pt(15)  # 字号更大，例如20号
            #     run.font.bold = True  # 加粗
            #     run.font.color.rgb = RGBColor(0, 0, 0)  # 红色字体
            #     run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimSun')  # 强
            # WordReportUtils._add_paragraph(doc, "1.附件1："+data.get("date_range","")+"全量案情")
            # WordReportUtils._add_paragraph(doc, "2.附件2："+data.get("date_range","")+"电话短信引流案件")
            # WordReportUtils._add_paragraph(doc, "3.附件3："+data.get("date_range","")+"url类案件")
            # WordReportUtils._add_paragraph(doc, "4.附件4："+data.get("date_range","")+"国际号码案件")
            # WordReportUtils._add_paragraph(doc, "5.附件5："+data.get("date_range","")+"涉APP类案件")
            # WordReportUtils._add_paragraph(doc, "6.附件6："+data.get("date_range","")+"106号码案件")
            # WordReportUtils._add_paragraph(doc, "7.附件7："+data.get("date_range","")+"运营商归属地统计结果")
            title_th = doc.add_heading('七、附件表', level=2)
            for run in title_th.runs:
                run.font.name = 'SimSun'  # 字体为宋体
                run.font.size = Pt(15)  # 字号更大，例如20号
                run.font.bold = True  # 加粗
                run.font.color.rgb = RGBColor(0, 0, 0)  # 红色字体
                run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimSun')  # 强
            # 添加案件类型表格
            WordReportUtils._add_paragraph(doc, "1．案件类型排序见下表：")
            WordReportUtils._add_case_table(doc, data.get("case_types", []))
            # 添加APP涉案表格
            WordReportUtils._add_paragraph(doc, "2.涉案APP TOP20详情如下：")
            WordReportUtils._add_app_table(doc, data.get("app_top_list", []))
            # 保存到内存
            # file_stream = io.BytesIO()
            doc.save(file_stream)
            # file_stream.seek(0)
            # return file_stream
        except Exception as e:
            raise RuntimeError("生成案情分析报告失败") from e

    @staticmethod
    def _add_paragraph(doc, text):
        """
        添加普通段落
        """
        if text:
            paragraph = doc.add_paragraph(text)
            paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.JUSTIFY

    @staticmethod
    def _add_centered_paragraph(doc, text):
        """
        添加居中段落
        """
        if text:
            p = doc.add_paragraph(text)
            p.alignment = WD_ALIGN_PARAGRAPH.CENTER

    @staticmethod
    def _add_case_table(doc, case_data):
        table = doc.add_table(rows=1, cols=5)
        table.style = 'Table Grid'
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '序号'
        hdr_cells[1].text = '类型'
        hdr_cells[2].text = '总数（起）'
        hdr_cells[3].text = '金额(万元)'
        hdr_cells[4].text = '占比（%）'
        for item in case_data:
            row_cells = table.add_row().cells
            row_cells[0].text = str(int(item["id"])) #if isinstance(item["id"], float) and item["id"].is_integer() else str(item["id"])
            row_cells[1].text = str(item["type"])
            row_cells[2].text = str(item["total_count"])
            row_cells[3].text = str(item["amount"])
            row_cells[4].text = str(item["percentage"])
        WordReportUtils.set_table_font_style(table)

    @staticmethod
    def _phone_province_table(doc, data):
        table = doc.add_table(rows=1, cols=4)
        table.style = 'Table Grid'
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '金额区间'
        hdr_cells[1].text = '地域'
        hdr_cells[2].text = '数量'
        hdr_cells[3].text = '具体号码及归属地'
        dx_list_50 = WordReportUtils._phone_province_group(data.get("dx_phone_list_50",[]), '中国电信-50万以上', '电信')
        dx_list_100 = WordReportUtils._phone_province_group(data.get("dx_phone_list_100", []), '中国电信-100万以上',
                                                           '电信')
        dx_list_1000 = WordReportUtils._phone_province_group(data.get("dx_phone_list_1000", []), '中国电信-1000万以上',
                                                           '电信')
        lt_list_50 = WordReportUtils._phone_province_group(data.get("lt_phone_list_50", []), '中国联通-50万以上',
                                                           '联通')
        lt_list_100 = WordReportUtils._phone_province_group(data.get("lt_phone_list_100", []), '中国联通-100万以上',
                                                           '联通')
        lt_list_1000 = WordReportUtils._phone_province_group(data.get("lt_phone_list_1000", []), '中国联通-1000万以上',
                                                           '联通')
        yd_list_50 = WordReportUtils._phone_province_group(data.get("yd_phone_list_50", []), '中国移动-50万以上',
                                                           '移动')
        yd_list_100 = WordReportUtils._phone_province_group(data.get("yd_phone_list_100", []), '中国移动-100万以上',
                                                            '移动')
        yd_list_1000 = WordReportUtils._phone_province_group(data.get("yd_phone_list_1000", []),
                                                             '中国移动-1000万以上',
                                                             '移动')
        gd_list_50 = WordReportUtils._phone_province_group(data.get("gd_phone_list_50", []), '中国广电-50万以上',
                                                           '广电')
        gd_list_100 = WordReportUtils._phone_province_group(data.get("gd_phone_list_100", []), '中国广电-100万以上',
                                                            '广电')
        gd_list_1000 = WordReportUtils._phone_province_group(data.get("gd_phone_list_1000", []),
                                                             '中国广电-1000万以上',
                                                             '广电')
        xs_list_50 = WordReportUtils._phone_province_group(data.get("xs_phone_list_50", []), '中国虚商-50万以上',
                                                           '虚商')
        xs_list_100 = WordReportUtils._phone_province_group(data.get("xs_phone_list_100", []), '中国虚商-100万以上',
                                                            '虚商')
        xs_list_1000 = WordReportUtils._phone_province_group(data.get("xs_phone_list_1000", []),
                                                             '中国虚商-1000万以上',
                                                             '虚商')
        phont_list = dx_list_50 + dx_list_100 + dx_list_1000 + lt_list_50 + lt_list_100 + lt_list_1000 + yd_list_50 + yd_list_100 + yd_list_1000 + gd_list_50 + gd_list_100 + gd_list_1000 + xs_list_50 + xs_list_100 + xs_list_1000
        for item in phont_list:
            row_cells = table.add_row().cells
            row_cells[0].text = str(item['金额区间'])
            row_cells[1].text = str(item['地域'])
            row_cells[2].text = str(item['数量'])
            row_cells[3].text = str(item['具体号码及归属地'])
        WordReportUtils.set_table_font_style(table)
    @staticmethod
    def _phone_province_group(data: list,my_str: str,type: str):
        result = []
        grouped = defaultdict(list)
        if not data:
            key = (my_str, '无')
            phone_dict = {}
            grouped[key].append(phone_dict)
        for da in data:
            loads = json.loads(da['suspect_phone_info'])
            for item in loads:
                if item['operator'] == type:
                    key = (my_str, str(item['province']))
                    phone = f"{item['phone']}({item['province']})"
                    grouped[key].append(phone)
        for key, value in grouped.items():
            num = 0
            phone = '无'
            if key[1] != '无':
                num = len(value)
            if num != 0:
                phone = "、".join(value)
            result.append({
                '金额区间': key[0],
                '地域': key[1],
                '数量': num,
                '具体号码及归属地': phone
            })
        return result
    @staticmethod
    def _add_app_top_ten_table(doc, case_data):
        table = doc.add_table(rows=1, cols=4)
        table.style = 'Table Grid'
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '序号'
        hdr_cells[1].text = 'APP名称'
        hdr_cells[2].text = '总数（起）'
        hdr_cells[3].text = '涉案金额'
        num = 1
        for item in case_data:
            row_cells = table.add_row().cells
            row_cells[0].text = str(num)
            row_cells[1].text = str(item["app_name"])
            row_cells[2].text = str(item["num"])
            row_cells[3].text = str(item["amount"])
            num += 1
        WordReportUtils.set_table_font_style(table)

    @staticmethod
    def _add_app_table(doc, app_data):
        table = doc.add_table(rows=1, cols=6)
        table.style = 'Table Grid'
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '序号'
        hdr_cells[1].text = 'APP名称'
        hdr_cells[2].text = '总数（起）'
        hdr_cells[3].text = '占比（%）'
        hdr_cells[4].text = '30天累计（起）'
        hdr_cells[5].text = '是否新增'
        for item in app_data:
            row_cells = table.add_row().cells
            row_cells[0].text = str(int(item["id"])) #if isinstance(item["id"], float) and item["id"].is_integer() else str(item["id"])
            row_cells[1].text = str(item["app_name"])
            row_cells[2].text = str(item["total_count"])
            row_cells[3].text = str(item["percentage"])
            row_cells[4].text = str(item["accumulate"])
            row_cells[5].text = str(item["is_addition"])
        WordReportUtils.set_table_font_style(table)

    @staticmethod
    def set_table_font_style(table, font_name='仿宋_GB2312', title_font_size=Pt(12),font_size=Pt(10.5), font_color=RGBColor(0, 0, 0)):
        header_widths = {
            '序号':1.6,
            '数量':1.6,
            '地域':1.6,
            '总数（起）':2.6,
            '占比（%）':2.6,
        }
        table.alignment = WD_ALIGN_PARAGRAPH.CENTER
        for cell in table.rows[0].cells:
            # 首行缩进
            cell.paragraphs[0].paragraph_format.first_line_indent = Pt(0)
            # 单倍行距
            cell.paragraphs[0].paragraph_format.line_spacing = Pt(12)
            # 段落间距，段后间距
            cell.paragraphs[0].paragraph_format.space_after = Pt(0)
            cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
            cell_font = cell.paragraphs[0].runs[0].font
            cell_font.name = font_name
            cell_font.size = title_font_size
            cell_font.bold = True
        for row in table.rows[1:]:
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    # 首行缩进
                    paragraph.paragraph_format.first_line_indent = Pt(0)
                    # 单倍行距
                    paragraph.paragraph_format.line_spacing = Pt(12)
                    # 段落间距，段后间距
                    paragraph.paragraph_format.space_after = Pt(0)
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                    for run in paragraph.runs:
                        run.font.name = font_name
                        run.font.size = font_size
                        run.font.color.rgb = font_color
                        run._element.rPr.rFonts.set(qn('w:eastAsia'), font_name)
        for col_index,column_cells in enumerate(table.columns):
            header_text = column_cells.cells[0].text
            if header_text in header_widths:
                for cel in table.columns[col_index].cells:
                    cel.width = Cm(header_widths[header_text])
        # table.autofit = True
        # for row in table.rows:
        #     for cell in row.cells:
        #         for paragraph in cell.paragraphs:
        #             paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        #             for run in paragraph.runs:
        #                 run.font.name = font_name
        #                 run.font.size = font_size
        #                 run.font.color.rgb = font_color
        #                 run._element.rPr.rFonts.set(qn('w:eastAsia'), font_name)

    @staticmethod
    def send_word_report(file_stream, filename="CaseAnalysisReport.docx"):
        return send_file(
            file_stream,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )

    @staticmethod
    def format_telecom_phones(results,type):
        """
        从查询结果中筛选出运营商为"电信"的手机号，并按归属地拼接成指定格式的字符串。

        :param results: 查询结果列表，每个元素是包含 phone、operator、province、city 的字典
        :return: 拼接后的字符串
        """
        telecom_phones = [
            f"{info['phone']}(归属地{info['province']})"
            for item in results
            for info in json.loads(item["suspect_phone_info"])
            if info.get("operator") == type
        ]
        return "、".join(telecom_phones)

    @staticmethod
    def build_case_amount_description_with_phones(data):
        """
        从查询结果中筛选出运营商为"电信"的手机号，并按归属地拼接成指定格式的字符串。
        """
        desc_parts = [ "其中中国电信号码涉案金额" + data.get("telephone_amount", "")+"万，50万以上的案件" + data.get("high_amount_5", "") + "个"]
        if data.get("high_amount_5", "") != "0":
            desc_parts.append(":"+WordReportUtils.format_telecom_phones(data.get("dx_phone_list_50",[]),"电信"))
        else:
            desc_parts.append(",")
        desc_parts.append("100万以上的案件"+data.get("high_amount_10", "")+"个")
        if data.get("high_amount_10", "") != "0":
            desc_parts.append(":"+WordReportUtils.format_telecom_phones(data.get("dx_phone_list_100",[]),"电信"))
        else:
            desc_parts.append(",")
        desc_parts.append("1000万以上的案件"+data.get("high_amount_100", "")+"个")
        if data.get("high_amount_100", "") != "0":
            desc_parts.append(":"+WordReportUtils.format_telecom_phones(data.get("dx_phone_list_1000",[]),"电信"))
        else:
            desc_parts.append("。")
        return desc_parts

    @staticmethod
    def build_case_amount_description_with_phones_lt(data):
        """
        从查询结果中筛选出运营商为"电信"的手机号，并按归属地拼接成指定格式的字符串。
        """
        desc_parts = ["其中中国联通号码涉案金额" + data.get("lt_telephone_amount", "") + "万，50万以上的案件" + data.get(
            "lt_high_amount_5", "") + "个"]
        if data.get("lt_high_amount_5", "") != "0":
            desc_parts.append(":" + WordReportUtils.format_telecom_phones(data.get("lt_phone_list_50",[]), "联通"))
        else:
            desc_parts.append(",")
        desc_parts.append("100万以上的案件" + data.get("lt_high_amount_10", "") + "个")
        if data.get("lt_high_amount_10", "") != "0":
            desc_parts.append(":" + WordReportUtils.format_telecom_phones(data.get("lt_phone_list_100", []), "联通"))
        else:
            desc_parts.append(",")
        desc_parts.append("1000万以上的案件" + data.get("lt_high_amount_100", "") + "个")
        if data.get("lt_high_amount_100", "") != "0":
            desc_parts.append(":" + WordReportUtils.format_telecom_phones(data.get("lt_phone_list_1000",[]), "联通"))
        else:
            desc_parts.append("。")
        return desc_parts


    @staticmethod
    def build_case_amount_description_with_phones_yd(data):
        """
        从查询结果中筛选出运营商为"电信"的手机号，并按归属地拼接成指定格式的字符串。
        """
        desc_parts = ["其中中国移动号码涉案金额" + data.get("yd_telephone_amount", "") + "万，50万以上的案件" + data.get(
            "yd_high_amount_5", "") + "个"]
        if data.get("yd_high_amount_5", "") != "0":
            desc_parts.append(":" + WordReportUtils.format_telecom_phones(data.get("yd_phone_list_50",[]), "移动"))
        else:
            desc_parts.append(",")
        desc_parts.append("100万以上的案件" + data.get("yd_high_amount_10", "") + "个")
        if data.get("yd_high_amount_10", "") != "0":
            desc_parts.append(":" + WordReportUtils.format_telecom_phones(data.get("yd_phone_list_100", []), "移动"))
        else:
            desc_parts.append(",")
        desc_parts.append("1000万以上的案件" + data.get("yd_high_amount_100", "") + "个")
        if data.get("yd_high_amount_100", "") != "0":
            desc_parts.append(":" + WordReportUtils.format_telecom_phones(data.get("yd_phone_list_1000",[]), "移动"))
        else:
            desc_parts.append("。")
        return desc_parts

    @staticmethod
    def build_case_amount_description_with_phones_gd(data):
        """
        从查询结果中筛选出运营商为"电信"的手机号，并按归属地拼接成指定格式的字符串。
        """
        desc_parts = ["其中广电号码涉案金额" + data.get("gd_telephone_amount", "") + "万，50万以上的案件" + data.get(
            "gd_high_amount_5", "") + "个"]
        if data.get("gd_high_amount_5", "") != "0":
            desc_parts.append(
                ":" + WordReportUtils.format_telecom_phones(data.get("gd_phone_list_50", []), "广电"))
        else:
            desc_parts.append(",")
        desc_parts.append("100万以上的案件" + data.get("gd_high_amount_10", "") + "个")
        if data.get("gd_high_amount_10", "") != "0":
            desc_parts.append(
                ":" + WordReportUtils.format_telecom_phones(data.get("gd_phone_list_100", []), "广电"))
        else:
            desc_parts.append(",")
        desc_parts.append("1000万以上的案件" + data.get("gd_high_amount_100", "") + "个")
        if data.get("gd_high_amount_100", "") != "0":
            desc_parts.append(
                ":" + WordReportUtils.format_telecom_phones(data.get("gd_phone_list_1000", []), "广电"))
        else:
            desc_parts.append("。")
        return desc_parts

    @staticmethod
    def build_case_amount_description_with_phones_xs(data):
        """
        从查询结果中筛选出运营商为"电信"的手机号，并按归属地拼接成指定格式的字符串。
        """
        desc_parts = ["其中虚商号码涉案金额" + data.get("xs_telephone_amount", "") + "万，50万以上的案件" + data.get(
            "xs_high_amount_5", "") + "个"]
        if data.get("xs_high_amount_5", "") != "0":
            desc_parts.append(":" + WordReportUtils.format_telecom_phones(data.get("xs_phone_list_50",[]), "虚商"))
        else:
            desc_parts.append(",")
        desc_parts.append("100万以上的案件" + data.get("xs_high_amount_10", "") + "个")
        if data.get("xs_high_amount_10", "") != "0":
            desc_parts.append(":" + WordReportUtils.format_telecom_phones(data.get("xs_phone_list_100", []), "虚商"))
        else:
            desc_parts.append(",")
        desc_parts.append("1000万以上的案件" + data.get("xs_high_amount_100", "") + "个")
        if data.get("xs_high_amount_100", "") != "0":
            desc_parts.append(":" + WordReportUtils.format_telecom_phones(data.get("xs_phone_list_1000",[]), "虚商"))
        else:
            desc_parts.append("。")
        return desc_parts

