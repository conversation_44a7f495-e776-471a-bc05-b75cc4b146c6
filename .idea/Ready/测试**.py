import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import numpy as np
from datetime import datetime, timedelta
import warnings
import pymysql
import json
import os
from pathlib import Path
from tqdm import tqdm
import time
import sys
from collections import defaultdict
import ollama
from functools import lru_cache
import sqlite3
from concurrent.futures import ThreadPoolExecutor
import hashlib
import threading
import calendar

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False


class TelecomMonitor:
    """运营商监控系统 - 电信专项版 (优化版)"""

    def __init__(self):
        self.connection = None
        self.is_connected = False
        self._cache = {}  # 内存缓存
        self._df_cache = {}  # DataFrame缓存
        self._lock = threading.Lock()  # 线程锁

        # 风险阈值配置
        self.risk_config = {
            '电信': {'threshold': 2.5, 'high_risk': 5, 'color': '#0769B6'},
            '联通': {'threshold': 2.0, 'high_risk': 4, 'color': '#E60012'},
            '移动': {'threshold': 3.0, 'high_risk': 6, 'color': '#00A0E9'}
        }

        # 配色方案
        self.colors = {
            'primary': '#0769B6', 'danger': '#E34234', 'warning': '#F0BE38',
            'success': '#28A745', 'neutral': '#6C757D', 'background': '#F8F9FA',
            'grid': '#E9ECEF', 'text': '#2C3E50'
        }

        # 数据库配置
        self.db_config = {
            'host': '************', 'port': 57861, 'user': 'FzUser',
            'password': 'jZ4%fQ3}oI8(jH7)', 'database': 'antiFraudPlatform',
            'charset': 'utf8mb4'
        }

        # 缓存配置
        self.cache_dir = Path("../cache")
        self.cache_dir.mkdir(exist_ok=True)
        self.cache_file = self.cache_dir / "fraud_cases_cache.json"

        # 图表导出配置
        self.export_dir = Path("/Users/<USER>/Documents/测试")
        self.export_dir.mkdir(exist_ok=True)

        # 分析选项配置
        self.analysis_options = {
            '1': {'name': '趋势分析', 'key': 'trend_analysis'},
            '2': {'name': '案情特征分析', 'key': 'case_pattern_analysis'},
            '3': {'name': '综合分析', 'key': 'comprehensive_analysis'}
        }

        # Ollama配置
        self.ollama_config = {
            'model': 'gemma3n:e2b',
            'timeout': 90,
            'max_retries': 2,
            'temperature': 0.7,
            'num_ctx': 4096
        }

        # 优化后的提示词模板配置
        self.prompt_templates = {
            'trend_analysis': """作为反诈数据分析专家，请基于真实数据进行电信诈骗案件趋势分析。

**严格要求：所有分析和结论必须严格基于提供的数据进行真实输出，严禁编纂、臆造数据或得出无数据支撑的结论。**

**核心数据输入**：
- 分析周期：{analysis_start_date} - {analysis_end_date}
- 周期内总案件数：{total_cases}起，日均{daily_avg:.1f}起
- 近7天累计案件数：{last_7_days_total}起
- 对比前7天变化率：{change_rate_vs_prev_7_days:+.1%}
- 本周案件数：{current_week_total}起 ({current_week_range})
- 上周案件数：{last_week_total}起 ({last_week_range})
- 上上周案件数：{two_weeks_ago_total}起 ({two_weeks_ago_range})
- 本周vs上周变化率：{current_vs_last_week_change:+.1%}
- 上周vs上上周变化率：{last_vs_two_weeks_change:+.1%}
- 对比上月同期：{last_month_start_date} - {last_month_end_date}
- 本月累计案件数：{current_month_total}起
- 上个月同期案件数：{last_month_same_period_total}起
- 月度变化率：{change_rate_vs_last_month:+.1%}
- 最新单日案件数：{current_cases}起，对比近7天日均值变化率：{current_vs_avg_7_days:+.1%}

**分析要点**：
1. 近7天的趋势与波动：结合近7天的数量序列分析近期案件总量变化、日均水平及增长/下降趋势
2. 周维度的数据趋势与波动：基于本周、上周、上上周的自然周数据分析案件总量变化趋势和周变化率
3. 月维度的数据趋势与波动：取自然月的数据序列分析近期案件总量的变化、日均水平及增长/下降趋势，对比上一个自然月同期数量，分析得出变化趋势
4. 环比与同比洞察：详细解读近7天与前7天、本周与上周、上周与上上周、本月与上月同期的案件量变化率
5. 运营商分布特征：基于近3个月数据分析电信、联通、移动案件数量变化趋势

**输出要求**：300-500字，必须基于真实数据，严禁编造内容，客观角度分析数据变化。""",

'case_pattern_analysis': """作为反诈策略专家，请基于真实案情数据进行运营商交叉案情对比分析。

**严格要求：所有分析和结论必须严格基于提供的数据进行真实输出，严禁编纂、臆造数据或得出无数据支撑的结论。**

**核心数据输入**：
- 分析周期：{analysis_start_date} - {analysis_end_date}（近{days}天）
- 筛选条件：金额大于{amount_threshold}元
- 总案件数：{total_cases}起，日均{daily_avg:.1f}起
- 案件样本详情：{case_samples}
- 电信样本数：{telecom_samples_count}起
- 联通样本数：{unicom_samples_count}起
- 移动样本数：{mobile_samples_count}起

**分析要点**：
1. 各运营商案情特征：深入分析电信、联通、移动三类运营商用户所涉诈骗案件在诈骗手法、受害人画像、作案时间、涉案金额分布及技术手段上的各自主要特点
2. 各运营商案情共性：深入分析电信、联通、移动三类运营商用户所涉诈骗案件在案情序列、诈骗流程、涉案APP有什么共性，挖掘可以形成的特点和预警
2. 跨运营商预警机制：重点对比分析发生在联通、移动用户身上，但目前在电信用户中尚未大量出现或刚有苗头的新型诈骗手法
3. 提前布防建议：针对潜在风险，提出具体的业务应对建议

**输出要求**：400-600字，必须基于真实案情数据，严禁编造内容，突出运营商间差异对比和预警价值。""",

'comprehensive_analysis': """作为资深业务数据分析师 & 反诈策略专家，请基于多维度数据对当前电信诈骗案件趋势进行综合分析。

**严格要求：所有分析和结论必须严格基于提供的数据进行真实输出，严禁编纂、臆造数据或得出无数据支撑的结论。**

**核心数据输入**：
- 分析周期：{analysis_start_date} - {analysis_end_date}（近{days}天）
- 周期内总案件数：{total_cases}起，日均{daily_avg:.1f}起
- 近7天累计案件数：{last_7_days_total}起，日均：{last_7_days_avg:.1f}起
- 对比前7天变化率：{change_rate_vs_prev_7_days:+.1%}
- 上个月统计周期：{last_month_start_date} - {last_month_end_date}
- 上月同期总案件数：{last_month_same_period_total}起，日均{last_month_daily_avg:.1f}起
- 本月统计周期：{current_month_start_date} - {current_month_end_date}
- 本月累计总案件数：{current_month_total}起，日均{current_month_daily_avg:.1f}起
- 对比上月同期变化率：{change_rate_vs_last_month:+.1%}
- 最新单日案件数：{current_cases}起，对比近7天日均值变化率：{current_vs_avg_7_days:+.1%}
- 每日案件数量序列：{daily_sequence}
- 案件样本详情：{case_samples}
- 电信样本数：{telecom_samples_count}起
- 联通样本数：{unicom_samples_count}起
- 移动样本数：{mobile_samples_count}起

**分析框架**：
1. 案件趋势量化分析与预警：
   - 近7天的趋势与波动：结合近7天的数量序列分析近期案件总量变化、日均水平及增长/下降趋势
   - 周维度的数据趋势与波动：取自然周的数据序列分析近期案件总量的变化、日均水平及增长/下降趋势，对比上一个自然周同期数量，分析得出变化趋势
   - 月维度的数据趋势与波动：取自然月的数据序列分析近期案件总量的变化、日均水平及增长/下降趋势，对比上一个自然月同期数量，分析得出变化趋势
   - 环比与同比洞察：详细解读近7天与前7天、本周与上周同期、本月与上月同期的案件量变化率
   - 运营商分布特征：基于近3个月数据分析电信、联通、移动案件数量变化趋势

2. 运营商交叉案情对比与提前布防：
   - 各运营商案情特征：深入分析电信、联通、移动三类运营商用户所涉诈骗案件的各自主要特点，突出典型性、差异性
   - 跨运营商预警机制：重点对比分析发生在联通、移动用户身上，但目前在电信用户中尚未大量出现或刚有苗头的新型诈骗手法、技术工具或受害群体特征
   - 应对建议：针对潜在风险，提出具体的业务应对建议，实现提前发现、提前防范

**输出要求**：300-650字，严格根据提供的数据进行分析，严禁编纂或臆造任何数据和结论。结构清晰，逻辑严密，具有较强的决策支持价值。"""
    }

    def _generate_df_hash(self, df):
        """生成DataFrame的哈希值"""
        if df is None or df.empty:
            return None

        # 使用DataFrame的内容生成哈希
        df_str = df.to_string()
        return hashlib.md5(df_str.encode()).hexdigest()

    def _store_df_in_cache(self, df_hash, df):
        """将DataFrame存储到缓存"""
        with self._lock:
            self._df_cache[df_hash] = df.copy()

    def _get_df_from_hash(self, df_hash):
        """从哈希获取DataFrame"""
        with self._lock:
            return self._df_cache.get(df_hash)

    @lru_cache(maxsize=32)
    def check_ollama_service(self):
        """检查Ollama服务状态 - 简化版本"""
        try:
            # 简单的服务连接测试，不获取模型列表
            ollama.list()
            return True, f"✅ Ollama服务正常，使用模型: {self.ollama_config['model']}"
        except Exception as e:
            return False, f"❌ Ollama服务异常: {str(e)}"

    def call_ollama_api(self, prompt):
        """调用Ollama API - 简化版本"""
        try:
            # 重试机制
            for attempt in range(self.ollama_config['max_retries'] + 1):
                try:
                    # 直接调用API，使用默认模型
                    response = ollama.chat(
                        model=self.ollama_config['model'],
                        messages=[
                            {'role': 'system', 'content': '你是专业的反诈数据分析师，请基于数据分析'},
                            {'role': 'user', 'content': prompt}
                        ],
                        options={
                            'temperature': self.ollama_config['temperature'],
                            'num_ctx': self.ollama_config['num_ctx']
                        }
                    )

                    # 修复响应格式处理
                    if hasattr(response, 'message') and hasattr(response.message, 'content'):
                        # 处理对象格式的响应
                        return response.message.content
                    elif isinstance(response, dict):
                        # 处理字典格式的响应
                        if 'message' in response:
                            message = response['message']
                            if isinstance(message, dict) and 'content' in message:
                                return message['content']
                            elif hasattr(message, 'content'):
                                return message.content
                        # 直接返回content字段
                        elif 'content' in response:
                            return response['content']

                    # 如果都不匹配，尝试直接使用响应
                    if isinstance(response, str):
                        return response

                    # 最后尝试转换为字符串
                    return str(response)

                except Exception as e:
                    if attempt < self.ollama_config['max_retries']:
                        print(f"⚠️ 第{attempt + 1}次尝试失败，正在重试...")
                        time.sleep(1)
                        continue
                    else:
                        return f"❌ 分析失败 (尝试{attempt + 1}次): {str(e)}"

        except Exception as e:
            return f"❌ API调用异常: {str(e)}"

    def display_main_menu(self):
        """显示主菜单"""
        menu_text = """
================================================================================
🎯 中国电信反诈监控系统
================================================================================
📋 功能菜单：
【1】📅 查看日维度统计图表
【2】📊 查看周维度统计图表
【3】🤖 AI智能分析
【4】📈 查看统计数据
【5】🗑️ 清除缓存
【6】⚙️ 系统设置
【0】🚪 退出"""
        print(menu_text)

    def display_analysis_menu(self):
        """显示分析选项菜单"""
        print("\n" + "=" * 50)
        print("🤖 AI分析选项")
        print("=" * 50)
        for key, option in self.analysis_options.items():
            print(f"【{key}】{option['name']}")

    def get_user_selection(self):
        """获取用户选择 - 优化版本"""
        while True:
            try:
                user_input = input("\n🔍 请输入您的选择: ").strip()

                if user_input.lower() in ['exit', 'q', '退出']:
                    return None
                if user_input.lower() in ['all', '全部']:
                    return list(self.analysis_options.keys())

                # 处理多选
                selections = [s.strip() for s in user_input.replace('+', ',').split(',')]
                valid_selections = [s for s in selections if s in self.analysis_options]

                if valid_selections:
                    return valid_selections
                else:
                    print("❌ 无效选择，请重新输入")
            except KeyboardInterrupt:
                return None

    def ask_save_chart(self, chart_type):
        """询问是否保存图表 - 简化版"""
        save_choice = input(f"\n💾 是否保存{chart_type}图表？(y/n，默认n): ").strip().lower()
        if save_choice in ['y', 'yes', '是', '保存']:
            # 自动生成保存路径
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{chart_type}_{timestamp}.png"
            save_path = self.export_dir / filename
            print(f"📁 图表将保存至: {save_path}")
            return save_path
        return None

    def get_save_path(self, chart_type):
        """获取保存路径"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        default_filename = f"{chart_type}_{timestamp}.png"

        print(f"\n📁 默认保存路径: {self.export_dir}")
        print(f"📄 默认文件名: {default_filename}")

        choice = input("\n使用默认设置？(y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            return self.export_dir / default_filename

        # 自定义设置
        custom_path = input("自定义保存路径（留空使用默认）: ").strip()
        if custom_path:
            custom_path = Path(custom_path)
            custom_path.mkdir(parents=True, exist_ok=True)
        else:
            custom_path = self.export_dir

        custom_filename = input(f"文件名（留空使用默认）: ").strip()
        if not custom_filename:
            custom_filename = default_filename
        elif not custom_filename.endswith('.png'):
            custom_filename += '.png'

        return custom_path / custom_filename

    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            self.is_connected = True
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False

    def get_data(self, days=30, use_cache=True, include_full_comparison=False):
        """获取数据 - 支持完整月度对比"""
        actual_days = days

        if include_full_comparison:
            # 为了月度对比，扩大查询范围到上个月1日
            today = datetime.now()
            current_month = today.month
            current_year = today.year

            if current_month == 1:
                prev_month = 12
                prev_year = current_year - 1
            else:
                prev_month = current_month - 1
                prev_year = current_year

            # 从上个月1日开始查询
            extended_start_date = datetime(prev_year, prev_month, 1)
            actual_days = (today - extended_start_date).days + 1

        cache_key = f"data_{actual_days}_{datetime.now().strftime('%Y-%m-%d')}"

        # 检查内存缓存
        if use_cache and cache_key in self._cache:
            return self._cache[cache_key]

        # 检查文件缓存
        if use_cache and self.cache_file.exists():
            try:
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)

                if cache_data.get('cache_date') == datetime.now().strftime('%Y-%m-%d'):
                    df = pd.DataFrame(cache_data['data'])
                    case_details = pd.DataFrame(cache_data.get('case_details', []))
                    if not df.empty:
                        df['日期'] = pd.to_datetime(df['日期'])
                    self._cache[cache_key] = (df, case_details)
                    return df, case_details
            except Exception as e:
                pass

        # 查询数据库
        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=actual_days)).strftime('%Y-%m-%d')

            query = f"""
            SELECT 
                DATE(insert_day) as date_col,
                SUM(CASE WHEN suspect_phone_info LIKE '%电信%' THEN 1 ELSE 0 END) as 电信,
                SUM(CASE WHEN suspect_phone_info LIKE '%联通%' THEN 1 ELSE 0 END) as 联通,
                SUM(CASE WHEN suspect_phone_info LIKE '%移动%' THEN 1 ELSE 0 END) as 移动
            FROM anti_fraud_case_new 
            WHERE DATE(insert_day) BETWEEN '{start_date}' AND '{end_date}'
              AND involved_amount >= 500000
            GROUP BY DATE(insert_day)
            ORDER BY DATE(insert_day)
            """

            df = pd.read_sql(query, self.connection)

            if not df.empty:
                df.rename(columns={'date_col': '日期'}, inplace=True)
                df['日期'] = pd.to_datetime(df['日期'])

                # 确保运营商列存在
                for operator in ['电信', '联通', '移动']:
                    if operator not in df.columns:
                        df[operator] = 0

                df = self._fill_missing_dates(df, start_date, end_date)
            else:
                df = self._create_empty_df(start_date, end_date)

            # 查询案情详情
            case_details = self._query_case_details(start_date, end_date)

            # 缓存数据
            if df is not None and not df.empty:
                self._cache[cache_key] = (df, case_details)

                if use_cache:
                    try:
                        cache_data = {
                            'cache_date': datetime.now().strftime('%Y-%m-%d'),
                            'data': df.to_dict('records'),
                            'case_details': case_details.to_dict('records') if case_details is not None else []
                        }
                        with open(self.cache_file, 'w', encoding='utf-8') as f:
                            json.dump(cache_data, f, ensure_ascii=False, indent=2, default=str)
                    except Exception as e:
                        pass

            return df, case_details

        except Exception as e:
            return None, None

    def _save_cache_async(self, df, case_details):
        """异步保存缓存"""

        def save_cache():
            try:
                cache_data = {
                    'cache_date': datetime.now().strftime('%Y-%m-%d'),
                    'data': df.to_dict('records'),
                    'case_details': case_details.to_dict('records') if case_details is not None else []
                }
                with open(self.cache_file, 'w', encoding='utf-8') as f:
                    json.dump(cache_data, f, ensure_ascii=False, indent=2, default=str)
                print("💾 数据已异步缓存")
            except Exception as e:
                print(f"⚠️ 缓存保存失败: {e}")

        # 使用线程池异步执行
        with ThreadPoolExecutor(max_workers=1) as executor:
            executor.submit(save_cache)

    def _query_database(self, days):
        """查询数据库 - 添加调试信息"""
        if not self.connect_database():
            return None, None

        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

            print(f"🔍 SQL查询参数:")
            print(f"   查询天数: {days}")
            print(f"   开始日期: {start_date}")
            print(f"   结束日期: {end_date}")

            # 优化的SQL查询
            query = f"""
            SELECT 
                DATE(insert_day) as date_col,
                SUM(CASE WHEN suspect_phone_info LIKE '%电信%' THEN 1 ELSE 0 END) as 电信,
                SUM(CASE WHEN suspect_phone_info LIKE '%联通%' THEN 1 ELSE 0 END) as 联通,
                SUM(CASE WHEN suspect_phone_info LIKE '%移动%' THEN 1 ELSE 0 END) as 移动
            FROM anti_fraud_case_new 
            WHERE DATE(insert_day) BETWEEN '{start_date}' AND '{end_date}'
              AND involved_amount >= 500000
            GROUP BY DATE(insert_day)
            ORDER BY DATE(insert_day)
            """

            print(f"🔍 执行的SQL查询:")
            print(f"   WHERE DATE(insert_day) BETWEEN '{start_date}' AND '{end_date}'")

            df = pd.read_sql(query, self.connection)

            if not df.empty:
                df.rename(columns={'date_col': '日期'}, inplace=True)
                df['日期'] = pd.to_datetime(df['日期'])

                # 显示查询到的日期范围
                actual_start = df['日期'].min().strftime('%Y-%m-%d')
                actual_end = df['日期'].max().strftime('%Y-%m-%d')

                # 按月份显示数据分布
                june_count = len(df[df['日期'].dt.month == 6])
                july_count = len(df[df['日期'].dt.month == 7])
                print(f"   6月份: {june_count}天")
                print(f"   7月份: {july_count}天")

                # 确保运营商列存在
                for operator in ['电信', '联通', '移动']:
                    if operator not in df.columns:
                        df[operator] = 0

                df = self._fill_missing_dates(df, start_date, end_date)
                print(f"✅ 填充缺失日期后，共 {len(df)} 条记录")
            else:
                print("⚠️ 查询结果为空")
                df = self._create_empty_df(start_date, end_date)

            # 并行查询案情详情
            case_details = self._query_case_details(start_date, end_date)

            return df, case_details

        except Exception as e:
            print(f"❌ 查询失败: {e}")
            return None, None

    def _create_empty_df(self, start_date, end_date):
        """创建空数据框"""
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        return pd.DataFrame({
            '日期': date_range,
            '电信': 0,
            '联通': 0,
            '移动': 0
        })

    def _query_case_details(self, start_date, end_date):
        """查询案情详情"""
        try:
            detail_query = f"""
            SELECT 
                DATE(insert_day) as date_col,
                suspect_phone_info,
                involved_amount,
                case_type
            FROM anti_fraud_case_new 
            WHERE DATE(insert_day) BETWEEN '{start_date}' AND '{end_date}'
              AND involved_amount >= 500000
            ORDER BY DATE(insert_day)
            """

            case_details = pd.read_sql(detail_query, self.connection)
            if not case_details.empty:
                case_details.rename(columns={'date_col': '日期'}, inplace=True)
                case_details['日期'] = pd.to_datetime(case_details['日期'])

            return case_details
        except Exception:
            return pd.DataFrame()

    def _fill_missing_dates(self, df, start_date, end_date):
        """填充缺失日期"""
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        full_df = pd.DataFrame({'日期': date_range})

        # 合并数据，缺失值填充为0
        result = full_df.merge(df, on='日期', how='left').fillna(0)

        # 确保运营商列为整数
        for col in ['电信', '联通', '移动']:
            if col in result.columns:
                result[col] = result[col].astype(int)

        return result

    def calculate_statistics(self, df):
        """计算统计数据"""
        if df is None or df.empty:
            return {}

        stats = {}

        for operator in ['电信', '联通', '移动']:
            if operator not in df.columns:
                continue

            operator_data = df[operator]

            # 基础统计
            total_cases = operator_data.sum()
            daily_new = operator_data.iloc[-1] if len(operator_data) > 0 else 0
            daily_avg = operator_data.mean()
            max_daily = operator_data.max()
            min_daily = operator_data.min()

            # 周统计
            week_total = operator_data.tail(7).sum()
            prev_week_total = operator_data.iloc[-14:-7].sum() if len(operator_data) >= 14 else 0
            week_change = (week_total - prev_week_total) / prev_week_total if prev_week_total > 0 else 0

            # 月统计
            month_total = operator_data.tail(30).sum()
            prev_month_total = operator_data.iloc[-60:-30].sum() if len(operator_data) >= 60 else 0
            month_change = (month_total - prev_month_total) / prev_month_total if prev_month_total > 0 else 0

            stats[operator] = {
                'total_cases': int(total_cases),
                'daily_new': int(daily_new),
                'daily_avg': daily_avg,
                'max_daily': int(max_daily),
                'min_daily': int(min_daily),
                'week_total': int(week_total),
                'week_change': week_change,
                'month_total': int(month_total),
                'month_change': month_change
            }

        return stats

    def calculate_weekly_data(self, df):
        """计算周维度数据"""
        if df is None or df.empty:
            return None

        df_copy = df.copy()
        operators = ['电信', '联通', '移动']

        # 确保列存在
        for operator in operators:
            if operator not in df_copy.columns:
                df_copy[operator] = 0

        # 添加周信息
        df_copy['year'] = df_copy['日期'].dt.year
        df_copy['week'] = df_copy['日期'].dt.isocalendar().week
        df_copy['year_week'] = df_copy['year'].astype(str) + '-W' + df_copy['week'].astype(str).str.zfill(2)

        # 分组聚合
        agg_dict = {op: 'sum' for op in operators}
        weekly_stats = df_copy.groupby(['year', 'week', 'year_week']).agg(agg_dict).reset_index()

        # 计算周起始日期
        weekly_stats['week_start'] = pd.to_datetime(
            weekly_stats['year'].astype(str) + '-W' + weekly_stats['week'].astype(str).str.zfill(2) + '-1',
            format='%Y-W%W-%w'
        )

        return weekly_stats.sort_values('week_start')

    def create_daily_chart(self, df, save_path=None):
        """创建日维度图表 - 优化版本"""
        if df is None or df.empty:
            print("⚠️ 无数据可绘制图表")
            return

        # 使用更高效的绘图方式
        fig, ax = plt.subplots(figsize=(16, 9))
        fig.patch.set_facecolor(self.colors['background'])

        if '电信' in df.columns:
            x_data = df['日期'].values
            y_data = df['电信'].values
            color = self.risk_config['电信']['color']

            # 优化绘图
            ax.plot(x_data, y_data, color=color, linewidth=3.0,
                    label='电信', marker='o', markersize=6)
            ax.fill_between(x_data, y_data, alpha=0.25, color=color)

            # 风险阈值线
            config = self.risk_config['电信']
            ax.axhline(y=config['threshold'], color=self.colors['warning'],
                       linestyle='--', alpha=0.8, linewidth=2,
                       label=f'电信警告阈值({config["threshold"]})')
            ax.axhline(y=config['high_risk'], color=self.colors['danger'],
                       linestyle='-.', alpha=0.9, linewidth=2.5,
                       label=f'电信高风险阈值({config["high_risk"]})')

        # 统一样式设置
        self._setup_chart_style(ax, '中国电信反诈案件日维度监控图表(涉案金额 ≥ 50万元)')

        # 优化日期格式
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=2))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=0, ha='center', fontsize=12)

        plt.tight_layout()
        plt.show()  # 先显示预览

        # 显示后询问保存
        if save_path is None:
            save_path = self.ask_save_chart('日维度')

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✅ 图表已保存至: {save_path}")

    def create_weekly_chart(self, df, save_path=None):
        """创建周维度柱状图 - 优化版本"""
        weekly_data = self.calculate_weekly_data(df)
        if weekly_data is None or weekly_data.empty:
            print("⚠️ 无数据可绘制周维度图表")
            return

        # 临时调试：查看实际有多少周的数据
        print(f"📊 周维度数据统计：共 {len(weekly_data)} 周")
        if len(weekly_data) < 8:
            print(f"⚠️ 数据不足8周，实际显示 {len(weekly_data)} 周")

        weekly_data = weekly_data.tail(8)

        fig, ax = plt.subplots(figsize=(16, 9))
        fig.patch.set_facecolor(self.colors['background'])

        # 准备数据
        weeks = weekly_data['year_week'].values
        operators = ['电信', '联通', '移动']
        colors = ['#0769AD', '#E60012', '#00A0E9']

        # 高效绘制柱状图
        bar_width = 0.25
        index = np.arange(len(weeks))

        for i, operator in enumerate(operators):
            if operator in weekly_data.columns:
                ax.bar(index + i * bar_width, weekly_data[operator].values,
                       bar_width, label=operator, color=colors[i],
                       alpha=0.85, edgecolor='white', linewidth=1.2)

        # 设置样式
        self._setup_chart_style(ax, '三大运营商反诈案件周累计数对比(涉案金额 ≥ 50万元)')

        ax.set_xticks(index + bar_width)
        ax.set_xticklabels(weeks, rotation=0, ha='center', fontsize=12)
        ax.grid(True, alpha=0.3, color=self.colors['grid'], axis='y')

        plt.tight_layout()
        plt.show()  # 先显示预览

        # 显示后询问保存
        if save_path is None:
            save_path = self.ask_save_chart('周维度')

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✅ 图表已保存至: {save_path}")

    def _setup_chart_style(self, ax, title):
        """设置图表样式 - 统一样式配置"""
        # 标题样式
        ax.set_title(title, fontsize=18, fontweight='bold',
                     color=self.colors['text'], pad=20)

        # 坐标轴样式
        ax.set_xlabel('日期', fontsize=14, fontweight='bold', color=self.colors['text'])
        ax.set_ylabel('案件数量', fontsize=14, fontweight='bold', color=self.colors['text'])

        # 网格线
        ax.grid(True, alpha=0.3, color=self.colors['grid'], linestyle='-', linewidth=0.8)
        ax.set_axisbelow(True)

        # 图例样式
        legend = ax.legend(loc='upper left', fontsize=12, frameon=True,
                           fancybox=True, shadow=True, framealpha=0.9)
        legend.get_frame().set_facecolor(self.colors['background'])
        legend.get_frame().set_edgecolor(self.colors['grid'])

        # 坐标轴刻度样式
        ax.tick_params(axis='both', which='major', labelsize=11,
                       colors=self.colors['text'], width=1.2)

        # 边框样式
        for spine in ax.spines.values():
            spine.set_color(self.colors['grid'])
            spine.set_linewidth(1.5)

        # 背景色
        ax.set_facecolor(self.colors['background'])

    def calculate_date_ranges(self, days):
        """计算各种日期范围"""
        today = datetime.now().date()

        # 分析周期
        analysis_start_date = today - timedelta(days=days - 1)
        analysis_end_date = today

        # 本月范围（1日到今天）
        current_month_start = today.replace(day=1)
        current_month_end = today

        # 上月同期范围计算
        if today.month == 1:
            last_month_year = today.year - 1
            last_month = 12
        else:
            last_month_year = today.year
            last_month = today.month - 1

        # 上月1日
        last_month_start = datetime(last_month_year, last_month, 1).date()

        # 上月的同一天
        try:
            last_month_same_day = datetime(last_month_year, last_month, today.day).date()
        except ValueError:
            last_month_days = calendar.monthrange(last_month_year, last_month)[1]
            last_month_same_day = datetime(last_month_year, last_month, min(today.day, last_month_days)).date()

        return {
            'analysis_start_date': analysis_start_date.strftime('%m月%d日'),
            'analysis_end_date': analysis_end_date.strftime('%m月%d日'),
            'current_month_start_date': current_month_start.strftime('%m月%d日'),
            'current_month_end_date': current_month_end.strftime('%m月%d日'),
            'last_month_start_date': last_month_start.strftime('%m月%d日'),
            'last_month_end_date': last_month_same_day.strftime('%m月%d日'),

            # 内部使用的date对象
            '_current_month_start_date': current_month_start,
            '_current_month_end_date': current_month_end,
            '_last_month_start_date': last_month_start,
            '_last_month_end_date': last_month_same_day
        }

    def calculate_natural_week_stats(self, df):
        """计算自然周统计数据"""
        if df is None or df.empty:
            return {
                'current_week_total': 0,
                'last_week_total': 0,
                'two_weeks_ago_total': 0,
                'current_vs_last_week_change': 0,
                'last_vs_two_weeks_change': 0,
                'current_week_range': '',
                'last_week_range': '',
                'two_weeks_ago_range': ''
            }

        # 动态获取当前日期
        today = datetime.now().date()

        # 计算本周（周一到周日）
        current_week_start = today - timedelta(days=today.weekday())
        current_week_end = current_week_start + timedelta(days=6)

        # 计算上周
        last_week_start = current_week_start - timedelta(days=7)
        last_week_end = current_week_start - timedelta(days=1)

        # 计算上上周
        two_weeks_ago_start = last_week_start - timedelta(days=7)
        two_weeks_ago_end = last_week_start - timedelta(days=1)

        # 本周数据（只统计到今天）
        current_week_mask = (df['日期'].dt.date >= current_week_start) & \
                            (df['日期'].dt.date <= min(current_week_end, today))
        current_week_total = df[current_week_mask]['电信'].sum()

        # 上周数据（完整一周）
        last_week_mask = (df['日期'].dt.date >= last_week_start) & \
                         (df['日期'].dt.date <= last_week_end)
        last_week_total = df[last_week_mask]['电信'].sum()

        # 上上周数据（完整一周）
        two_weeks_ago_mask = (df['日期'].dt.date >= two_weeks_ago_start) & \
                             (df['日期'].dt.date <= two_weeks_ago_end)
        two_weeks_ago_total = df[two_weeks_ago_mask]['电信'].sum()

        # 计算变化率
        current_vs_last_week_change = 0
        if last_week_total > 0:
            current_vs_last_week_change = (current_week_total - last_week_total) / last_week_total

        last_vs_two_weeks_change = 0
        if two_weeks_ago_total > 0:
            last_vs_two_weeks_change = (last_week_total - two_weeks_ago_total) / two_weeks_ago_total

        return {
            'current_week_total': int(current_week_total),
            'last_week_total': int(last_week_total),
            'two_weeks_ago_total': int(two_weeks_ago_total),
            'current_vs_last_week_change': current_vs_last_week_change,
            'last_vs_two_weeks_change': last_vs_two_weeks_change,
            'current_week_range': f"{current_week_start.strftime('%m月%d日')}-{min(current_week_end, today).strftime('%m月%d日')}",
            'last_week_range': f"{last_week_start.strftime('%m月%d日')}-{last_week_end.strftime('%m月%d日')}",
            'two_weeks_ago_range': f"{two_weeks_ago_start.strftime('%m月%d日')}-{two_weeks_ago_end.strftime('%m月%d日')}"
        }

    def calculate_monthly_comparison(df):
        """计算本月和上月同期案件数"""
        if df is None or df.empty:
            return {'current_month_total': 0, 'last_month_same_period_total': 0}

        # 确保date列是datetime类型
        if not pd.api.types.is_datetime64_any_dtype(df['date']):
            df['date'] = pd.to_datetime(df['date'])

        # 获取当前日期
        today = datetime.now().date()

        # ===== 本月统计 =====
        current_month_start = today.replace(day=1)  # 本月1日
        current_month_days = today.day  # 本月已过天数

        current_mask = (df['date'].dt.date >= current_month_start) & \
                       (df['date'].dt.date <= today)
        current_month_cases = df[current_mask]
        current_month_total = len(current_month_cases)

        # ===== 上月同期统计 =====
        # 计算上月同日范围（考虑不同月份的天数差异）
        if today.month == 1:
            last_month_year = today.year - 1
            last_month = 12
        else:
            last_month_year = today.year
            last_month = today.month - 1

        # 上月1日
        last_month_start = today.replace(year=last_month_year, month=last_month, day=1)

        # 上月同期结束日（考虑月末日期）
        last_month_end_day = min(today.day, calendar.monthrange(last_month_year, last_month)[1])
        last_month_end = last_month_start.replace(day=last_month_end_day)

        last_month_mask = (df['date'].dt.date >= last_month_start) & \
                          (df['date'].dt.date <= last_month_end)
        last_month_cases = df[last_month_mask]
        last_month_same_period_total = len(last_month_cases)

        return {
            'current_month_total': current_month_total,
            'current_month_days': current_month_days,
            'last_month_same_period_total': last_month_same_period_total,
            'last_month_days': last_month_end_day,
            # 格式化日期用于显示
            'current_month_range': f"{current_month_start.strftime('%m月%d日')}-{today.strftime('%m月%d日')}",
            'last_month_range': f"{last_month_start.strftime('%m月%d日')}-{last_month_end.strftime('%m月%d日')}"
        }

    def calculate_monthly_stats(self, df, date_info):
        """计算月度统计数据"""
        if df is None or df.empty:
            return {
                'current_month_total': 0,
                'last_month_same_period_total': 0,
                'current_month_daily_avg': 0,
                'last_month_daily_avg': 0,
                'change_rate_vs_last_month': 0,
                'current_month_start_date': '',
                'current_month_end_date': '',
                'last_month_start_date': '',
                'last_month_end_date': ''
            }

        # 确保日期列是datetime类型
        if not pd.api.types.is_datetime64_any_dtype(df['日期']):
            df['日期'] = pd.to_datetime(df['日期'])

        today = datetime.now().date()

        # 本月统计
        current_month_start = today.replace(day=1)
        current_month_end = today

        # 上月同期统计
        if today.month == 1:
            last_month_start = today.replace(year=today.year - 1, month=12, day=1)
            last_month_end = today.replace(year=today.year - 1, month=12, day=today.day)
        else:
            last_month_start = today.replace(month=today.month - 1, day=1)
            # 处理月末日期
            try:
                last_month_end = today.replace(month=today.month - 1, day=today.day)
            except ValueError:
                # 如果上月没有这一天（如31号），则取上月最后一天
                import calendar
                last_day = calendar.monthrange(today.year if today.month > 1 else today.year - 1,
                                               today.month - 1 if today.month > 1 else 12)[1]
                last_month_end = today.replace(month=today.month - 1 if today.month > 1 else 12,
                                               year=today.year if today.month > 1 else today.year - 1,
                                               day=min(today.day, last_day))

        # 本月数据
        current_month_mask = (df['日期'].dt.date >= current_month_start) & \
                             (df['日期'].dt.date <= current_month_end)
        current_month_total = df[current_month_mask]['电信'].sum()
        current_month_days = (current_month_end - current_month_start).days + 1
        current_month_daily_avg = current_month_total / current_month_days if current_month_days > 0 else 0

        # 上月同期数据
        last_month_mask = (df['日期'].dt.date >= last_month_start) & \
                          (df['日期'].dt.date <= last_month_end)
        last_month_same_period_total = df[last_month_mask]['电信'].sum()
        last_month_days = (last_month_end - last_month_start).days + 1
        last_month_daily_avg = last_month_same_period_total / last_month_days if last_month_days > 0 else 0

        # 计算月度变化率
        change_rate_vs_last_month = 0
        if last_month_same_period_total > 0:
            change_rate_vs_last_month = (
                                                    current_month_total - last_month_same_period_total) / last_month_same_period_total

        return {
            'current_month_total': int(current_month_total),
            'last_month_same_period_total': int(last_month_same_period_total),
            'current_month_daily_avg': current_month_daily_avg,
            'last_month_daily_avg': last_month_daily_avg,
            'change_rate_vs_last_month': change_rate_vs_last_month,
            'current_month_start_date': current_month_start.strftime('%m月%d日'),
            'current_month_end_date': current_month_end.strftime('%m月%d日'),
            'last_month_start_date': last_month_start.strftime('%m月%d日'),
            'last_month_end_date': last_month_end.strftime('%m月%d日')
        }

    def debug_available_dates(self):
        """调试：查看数据库中实际有哪些日期的数据"""
        if not self.connect_database():
            return

        try:
            # 查询最近90天内所有有数据的日期
            query = """
            SELECT 
                DATE(insert_day) as date_col,
                COUNT(*) as total_cases,
                SUM(CASE WHEN suspect_phone_info LIKE '%电信%' THEN 1 ELSE 0 END) as telecom_cases
            FROM anti_fraud_case_new 
            WHERE DATE(insert_day) >= DATE_SUB(CURDATE(), INTERVAL 90 DAY)
              AND involved_amount >= 500000
            GROUP BY DATE(insert_day)
            ORDER BY DATE(insert_day) DESC
            """

            df = pd.read_sql(query, self.connection)

            if not df.empty:
                print(f"📊 数据库中最近90天的数据分布:")
                print(f"   总共有 {len(df)} 天有数据")
                print(f"   日期范围: {df['date_col'].min()} 到 {df['date_col'].max()}")

                # 按月份统计
                df['date_col'] = pd.to_datetime(df['date_col'])
                monthly_stats = df.groupby(df['date_col'].dt.strftime('%Y-%m')).agg({
                    'total_cases': 'sum',
                    'telecom_cases': 'sum'
                }).reset_index()

                print(f"\n📈 按月份统计:")
                for _, row in monthly_stats.iterrows():
                    print(f"   {row['date_col']}: {row['total_cases']}起总案件, {row['telecom_cases']}起电信案件")

                # 检查6月份具体有哪些日期
                june_data = df[df['date_col'].dt.month == 6]
                if not june_data.empty:
                    print(f"\n📅 6月份具体日期:")
                    for _, row in june_data.iterrows():
                        print(f"   {row['date_col'].strftime('%Y-%m-%d')}: {row['telecom_cases']}起电信案件")
                else:
                    print(f"\n⚠️ 6月份没有任何数据！")

                # 检查7月份具体日期
                july_data = df[df['date_col'].dt.month == 7]
                if not july_data.empty:
                    print(f"\n📅 7月份具体日期:")
                    for _, row in july_data.iterrows():
                        print(f"   {row['date_col'].strftime('%Y-%m-%d')}: {row['telecom_cases']}起电信案件")
            else:
                print("❌ 最近90天内没有任何数据")

        except Exception as e:
            print(f"❌ 调试查询失败: {e}")

    def _generate_daily_sequence(self, df, days=30):
        """
        生成每日案件数量序列
        df: 包含案件数据的DataFrame，必须包含 '日期' 列
        days: 序列天数
        """
        # 确保日期列是datetime类型
        if not pd.api.types.is_datetime64_any_dtype(df['日期']):
            df['日期'] = pd.to_datetime(df['日期'])

        # 获取日期范围
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days - 1)

        # 筛选数据
        mask = (df['日期'].dt.date >= start_date) & (df['日期'].dt.date <= end_date)
        period_data = df[mask]

        # 按日期分组统计
        daily_counts = period_data.groupby(period_data['日期'].dt.date)['电信'].sum()

        # 创建完整的日期序列（包括没有案件的日期）
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        daily_sequence = []

        for date in date_range:
            date_key = date.date()
            count = daily_counts.get(date_key, 0)
            daily_sequence.append(f"{date.strftime('%m-%d')}: {count}起")

        return daily_sequence

    def calculate_additional_stats(self, df, days=30, amount_threshold=1000):
        """
        计算所有新增的统计项
        df: 包含案件数据的DataFrame
        days: 分析天数
        amount_threshold: 金额阈值
        """
        # 计算日期范围
        date_info = self.calculate_date_ranges(days)

        # 计算月度统计
        monthly_stats = self.calculate_monthly_stats(df, date_info)

        # 生成表格
        three_month_table = self._generate_three_month_table(df)
        three_month_operator_table = self._generate_three_month_operator_table(df)
        three_month_detailed_table = self._generate_three_month_detailed_table(df)

        # 生成日序列
        daily_sequence = self._generate_daily_sequence(df, days)

        # 合并所有统计项
        all_stats = {
            **date_info,
            **monthly_stats,
            'three_month_table': three_month_table,
            'three_month_operator_table': three_month_operator_table,
            'three_month_detailed_table': three_month_detailed_table,
            'daily_sequence': daily_sequence,
            'amount_threshold': amount_threshold
        }

        # 移除内部使用的日期对象
        for key in list(all_stats.keys()):
            if key.startswith('_'):
                del all_stats[key]

        return all_stats

    def display_statistics(self, df):
        """展示统计数据 - 优化版本"""
        if df is None or df.empty:
            print("⚠️ 无数据可显示统计信息")
            return

        stats = self.calculate_statistics(df)
        if not stats:
            print("⚠️ 统计计算失败")
            return

        print("\n" + "=" * 80)
        print("📊 中国电信反诈案件统计数据")
        print("=" * 80)

        # 按运营商显示统计
        for operator in ['电信', '联通', '移动']:
            if operator in stats:
                operator_stats = stats[operator]
                config = self.risk_config.get(operator, {})

                print(f"\n【{operator}】运营商统计:")
                print(f"  📈 今日新增: {operator_stats['daily_new']} 起")
                print(f"  📊 总案件数: {operator_stats['total_cases']} 起")
                print(f"  📉 日均案件: {operator_stats['daily_avg']:.1f} 起")
                print(f"  🔝 单日最高: {operator_stats['max_daily']} 起")
                print(f"  🔻 单日最低: {operator_stats['min_daily']} 起")

                # 周统计
                if 'week_total' in operator_stats:
                    week_change = operator_stats.get('week_change', 0)
                    change_icon = "📈" if week_change > 0 else "📉" if week_change < 0 else "➡️"
                    print(f"  🗓️ 近7天: {operator_stats['week_total']} 起 "
                          f"({change_icon} {week_change:+.1%})")

                # 月统计
                if 'month_total' in operator_stats:
                    month_change = operator_stats.get('month_change', 0)
                    change_icon = "📈" if month_change > 0 else "📉" if month_change < 0 else "➡️"
                    print(f"  📅 近30天: {operator_stats['month_total']} 起 "
                          f"({change_icon} {month_change:+.1%})")

                # 风险评估
                daily_new = operator_stats['daily_new']
                threshold = config.get('threshold', 0)
                high_risk = config.get('high_risk', 0)

                if daily_new >= high_risk:
                    risk_level = "🔴 高风险"
                elif daily_new >= threshold:
                    risk_level = "🟡 警告"
                else:
                    risk_level = "🟢 正常"

                print(f"  ⚠️ 风险等级: {risk_level}")
                print("-" * 40)

        print("\n" + "=" * 80)

    def display_analysis_results(self, results, analysis_data):
        """根据用户选择动态显示分析结果 - 修复版"""
        print("\n" + "=" * 80)
        print("🤖 AI智能分析报告")
        print("=" * 80)

        # 分析类型映射
        analysis_blocks = {
            'trend_analysis': {
                'title': '📊 趋势分析报告',
                'data_focus': 'trend'
            },
            'case_pattern_analysis': {
                'title': '📑 案情特征分析报告',
                'data_focus': 'pattern'
            },
            'comprehensive_analysis': {
                'title': '📈 综合分析报告',
                'data_focus': 'comprehensive'
            }
        }

        # 根据实际分析结果动态显示
        block_count = 1
        for analysis_key, result_data in results.items():
            if analysis_key in analysis_blocks:
                block_info = analysis_blocks[analysis_key]

                print(f"\n{block_info['title']}")
                print("-" * 70)

                # 显示对应的核心数据
                self._display_core_data(analysis_data, block_info['data_focus'])

                # 显示AI分析结论 - 适配新的数据格式
                result_content = result_data.get('result') if isinstance(result_data, dict) else result_data
                if result_content:
                    print("\n� AI分析结论：")
                    conclusions = self._extract_conclusions(result_content)
                    for i, line in enumerate(conclusions[:5], 1):  # 显示最多5条结论
                        print(f"{i}. {line}")

                # 如果有多个分析，添加分隔符
                if block_count < len(results):
                    print("\n" + "=" * 50)
                block_count += 1

        print("\n" + "=" * 80)

    def _display_core_data(self, analysis_data, focus_type):
        """根据分析类型显示对应的核心数据"""
        # 基础数据（所有类型都显示）
        print(f"📅 分析周期：{analysis_data['analysis_start_date']} - {analysis_data['analysis_end_date']}")
        print(f"📋 周期内总案件数：{analysis_data['total_cases']:,}起，日均{analysis_data['daily_avg']:.1f}起")

        if focus_type == 'trend':
            # 趋势分析重点数据
            print(f"📱 近7天累计案件数：{analysis_data['last_7_days_total']:,}起")
            print(f"🔄 对比前7天变化率：{analysis_data['change_rate_vs_prev_7_days']:+.1%}")
            print(f"📊 本月累计案件数：{analysis_data['current_month_total']:,}起")
            print(f"📅 对比上月同期：{analysis_data['last_month_start_date']} - {analysis_data['last_month_end_date']}")
            print(f"📊 上个月同期案件数：{analysis_data['last_month_same_period_total']:,}起")
            print(f"🔄 月度变化率：{analysis_data['change_rate_vs_last_month']:+.1%}")
            print(f"📈 本周案件数：{analysis_data['current_cases']:,}起")
            print(f"📉 对比近7天日均值变化率：{analysis_data['current_vs_avg_7_days']:+.1%}")
            print(f"📅 对比上月同期：{analysis_data['last_month_start_date']} - {analysis_data['last_month_end_date']}")
            print(f"📊 上个月同期案件数：{analysis_data['last_month_same_period_total']:,}起")
            print(f"🔄 月度变化率：{analysis_data['change_rate_vs_last_month']:+.1%}")
            print(f"📈 最新单日案件数：{analysis_data['current_cases']:,}起")
            print(f"📉 对比近7天日均值变化率：{analysis_data['current_vs_avg_7_days']:+.1%}")


        elif focus_type == 'pattern':
            # 案情特征分析重点数据
            print(f" 筛选条件：金额 ≥ {analysis_data['amount_threshold']:,}元")

            # 案件样本详情（显示前3个样本）
            if analysis_data.get('case_samples'):
                print("\n🔎 案件样本详情（前3例）：")
                for i, sample in enumerate(analysis_data['case_samples'][:3], 1):
                    print(f"  {i}. 日期：{sample.get('date', '')} | 金额：{sample.get('amount', 0):,.2f}元 | 年龄：{sample.get('age', '未知')} | 类型：{sample.get('type', '')}")

            print(f"\n📱 运营商分布：")
            print(f"  电信：{analysis_data['telecom_samples_count']:,}起")
            print(f"  联通：{analysis_data['unicom_samples_count']:,}起")
            print(f"  移动：{analysis_data['mobile_samples_count']:,}起")

            # 近3个月运营商分布
            print("\n📊 近3个月运营商案件分布：")
            print(analysis_data['three_month_operator_table'])

        elif focus_type == 'comprehensive':
            # 综合分析重点数据
            print(f"📈 近7天累计案件数：{analysis_data['last_7_days_total']:,}起，日均：{analysis_data['last_7_days_avg']:.1f}起")
            print(f"🔄 对比前7天变化率：{analysis_data['change_rate_vs_prev_7_days']:+.1%}")
            print(f"📅 上个月统计周期：{analysis_data['last_month_start_date']} - {analysis_data['last_month_end_date']}")
            print(f"📊 上月同期总案件数：{analysis_data['last_month_same_period_total']:,}起，日均{analysis_data['last_month_daily_avg']:.1f}起")
            print(f"📅 本月统计周期：{datetime.now().strftime('%m月%d日')} - {analysis_data['analysis_end_date']}")
            print(f"📈 本月累计总案件数：{analysis_data['current_month_total']:,}起，日均{analysis_data['current_month_daily_avg']:.1f}起")
            print(f"🔄 对比上月同期变化率：{analysis_data['change_rate_vs_last_month']:+.1%}")
            print(f"📊 最新单日案件数：{analysis_data['current_cases']:,}起")
            print(f"📉 对比近7天日均值变化率：{analysis_data['current_vs_avg_7_days']:+.1%}")

            # 每日案件序列（简化显示）
            if len(analysis_data['daily_sequence']) <= 14:
                print("\n📅 每日案件序列：", analysis_data['daily_sequence'])
            else:
                print("\n📅 近期每日案件数：", analysis_data['daily_sequence'][-7:])

    def _extract_conclusions(self, text):
        """从AI回复中提取有效结论"""
        return [line.strip() for line in text.split('\n')
                if line.strip() and not line.startswith(('**', '【', '---'))]

    def display_progress_bar(self, current, total, message="", bar_length=30, show_eta=False, start_time=None):
        """显示进度条（增强版）"""
        # 确保current不超过total
        current = min(current, total)
        percentage = int((current / total) * 100)
        filled_length = int(bar_length * current // total)
        bar = "█" * filled_length + "░" * (bar_length - filled_length)

        # 添加颜色和图标
        if current == total:
            color = "\033[32m"  # 绿色
            icon = "✅"
        elif percentage < 30:
            color = "\033[33m"  # 黄色
            icon = "🔄"
        elif percentage < 70:
            color = "\033[36m"  # 青色
            icon = "⏳"
        else:
            color = "\033[32m"  # 绿色
            icon = "🚀"

        # 计算预估剩余时间
        eta_str = ""
        if show_eta and start_time and current > 0 and current < total:
            elapsed = time.time() - start_time
            eta = (elapsed / current) * (total - current)
            # 计算预估剩余时间
            eta_str = ""
            if show_eta and start_time and current > 0 and current < total:
                elapsed = time.time() - start_time
                eta = (elapsed / current) * (total - current)

                # 实时动态读秒
                if eta < 1:
                    eta_str = f" ETA: <1s"
                elif eta < 60:
                    eta_str = f" ETA: {eta:.0f}s"
                elif eta < 3600:
                    minutes = int(eta // 60)
                    seconds = int(eta % 60)
                    eta_str = f" ETA: {minutes}:{seconds:02d}"
                else:
                    hours = int(eta // 3600)
                    minutes = int((eta % 3600) // 60)
                    eta_str = f" ETA: {hours}:{minutes:02d}:00"

        # 格式化输出
        progress_str = f"\r{icon} {message.ljust(25)} {color}|{bar}| {percentage}%{eta_str}\033[0m"
        print(progress_str, end="", flush=True)

        # 完成时换行并显示完成信息
        if current == total:
            print()  # 换行

    def display_analysis_status(self, step_name, details=""):
        """显示分析状态信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        if details:
            print(f"[{timestamp}] 🔍 {step_name} - {details}")
        else:
            print(f"[{timestamp}] 🔍 {step_name}")

    def display_step_summary(self, step_name, duration, success=True):
        """显示步骤完成摘要"""
        status_icon = "✅" if success else "❌"
        status_text = "成功" if success else "失败"
        print(f"{status_icon} {step_name} {status_text} (耗时: {duration:.1f}s)")

    def prepare_analysis_data(self, df, case_details):
        """准备分析数据的包装方法，增加状态显示"""
        step_start = time.time()
        self.display_analysis_status("数据预处理", "正在整理案件数据...")

        try:
            analysis_data = self._prepare_analysis_data(df, case_details)
            duration = time.time() - step_start
            self.display_step_summary("数据预处理", duration, True)
            return analysis_data
        except Exception as e:
            duration = time.time() - step_start
            self.display_step_summary("数据预处理", duration, False)
            print(f"❌ 数据预处理失败: {str(e)}")
            return None

    def ai_analysis(self, df, case_details, selected_options):
        """AI分析 - 带进度条版本"""
        if df is None or df.empty:
            print("⚠️ 无数据进行AI分析")
            return

        print(f"\n🤖 开始AI分析，使用模型: {self.ollama_config['model']}")

        # 使用新的带进度条的分析方法
        results = self.run_analysis_with_progress(selected_options, df, case_details)

        # 显示分析结果
        if results and not results.get("error"):
            analysis_data = self._prepare_analysis_data(df, case_details)
            self.display_analysis_results(results, analysis_data)
        elif results and results.get("error"):
            print(f"\n{results['error']}")
        else:
            print("\n❌ 分析失败，未返回结果")

    def run_analysis_with_progress(self, selections, df, case_details):
        """带进度条的分析流程"""
        if not selections or df is None or df.empty:
            return {"error": "❌ 无法进行分析：数据不足或选择无效"}

        # 初始化进度跟踪
        start_time = time.time()
        total_steps = len(selections) * 2 + 2  # 每个分析有2个步骤 + 准备数据 + 结果处理
        current_step = 0

        # 步骤1: 准备分析数据
        self.display_progress_bar(current_step, total_steps, "准备分析数据", show_eta=True, start_time=start_time)

        try:
            analysis_data = self._prepare_analysis_data(df, case_details)
            if not analysis_data:
                return {"error": "❌ 无法准备分析数据"}
        except Exception as e:
            return {"error": f"❌ 数据预处理失败: {str(e)}"}

        # 执行分析
        results = {}
        for idx, selection in enumerate(selections, 1):
            if selection in self.analysis_options:
                option = self.analysis_options[selection]

                # 显示当前分析项信息
                self.display_analysis_status(f"分析任务 {idx}/{len(selections)}", f"开始{option['name']}")

                step_start = time.time()

                # 生成提示词
                current_step += 1
                self.display_progress_bar(current_step, total_steps, f"提示词处理中",
                                          show_eta=True, start_time=start_time)
                prompt = self._generate_analysis_prompt(option['key'], analysis_data)
                self.display_analysis_status("提示词生成", f"长度: {len(prompt)} 字符")

                # 调用AI模型
                current_step += 1
                self.display_progress_bar(current_step, total_steps, f"完成{option['name']}",
                                          show_eta=True, start_time=start_time)

                result = self.call_ollama_api(prompt)

                step_duration = time.time() - step_start
                self.display_step_summary(f"{option['name']}", step_duration,
                                          not result.startswith("❌") if isinstance(result, str) else True)

                results[option['key']] = {
                    'name': option['name'],
                    'result': result
                }

                # 分析间隔
                if idx < len(selections):
                    time.sleep(0.3)

        # 步骤最后: 处理结果
        current_step += 1
        self.display_progress_bar(current_step, total_steps, "整理分析结果", show_eta=True, start_time=start_time)
        time.sleep(0.2)

        total_duration = time.time() - start_time
        print(f"🎉 分析流程完成！总耗时: {total_duration:.1f}秒")
        print("=" * 80)
        return results

    def _prepare_analysis_data(self, df, case_details, days=30):
        """准备分析数据"""
        # 获取正确的日期范围
        date_info = self.calculate_date_ranges(days)

        # 基础统计
        telecom_data = df['电信'].tolist()
        total_cases = sum(telecom_data)
        daily_avg = total_cases / days
        current_cases = telecom_data[-1] if telecom_data else 0

        # 近期趋势
        last_7_days = telecom_data[-7:] if len(telecom_data) >= 7 else telecom_data
        last_7_days_total = sum(last_7_days)
        last_7_days_avg = last_7_days_total / 7

        # 前7天对比
        prev_7_days = telecom_data[-14:-7] if len(telecom_data) >= 14 else []
        prev_7_days_total = sum(prev_7_days) if prev_7_days else 0
        change_rate_vs_prev_7_days = 0
        if prev_7_days_total > 0:
            change_rate_vs_prev_7_days = (last_7_days_total - prev_7_days_total) / prev_7_days_total

        # 使用修正后的月度统计方法
        monthly_stats = self.calculate_monthly_stats(df, date_info)

        # 计算自然周统计
        week_stats = self.calculate_natural_week_stats(df)

        # 案件样本统计和详情
        case_samples = []
        telecom_samples_count = 0
        unicom_samples_count = 0
        mobile_samples_count = 0

        if case_details is not None and not case_details.empty:
            for _, row in case_details.iterrows():
                phone_info = str(row.get('suspect_phone_info', ''))
                amount = row.get('involved_amount', 0)
                case_type = row.get('case_type', '未知')

                if '电信' in phone_info:
                    telecom_samples_count += 1
                elif '联通' in phone_info:
                    unicom_samples_count += 1
                elif '移动' in phone_info:
                    mobile_samples_count += 1

                case_samples.append({
                    'date': row.get('日期', ''),
                    'phone_info': phone_info,
                    'amount': amount,
                    'case_type': case_type
                })

        return {
            **date_info,
            **monthly_stats,  # 这里包含了 change_rate_vs_last_month
            **week_stats,  # 添加自然周统计
            'days': days,
            'total_cases': total_cases,
            'daily_avg': daily_avg,
            'current_cases': current_cases,
            'last_7_days_total': last_7_days_total,
            'last_7_days_avg': last_7_days_avg,
            'change_rate_vs_prev_7_days': change_rate_vs_prev_7_days,
            'current_vs_avg_7_days': (current_cases - last_7_days_avg) / last_7_days_avg if last_7_days_avg > 0 else 0,
            'telecom_samples_count': int(telecom_samples_count),
            'unicom_samples_count': int(unicom_samples_count),
            'mobile_samples_count': int(mobile_samples_count),
            'case_samples': case_samples,
            'daily_sequence': telecom_data,
            'three_month_table': self._generate_three_month_table(df),
            'three_month_operator_table': self._generate_three_month_table(df),
            'amount_threshold': 500000  # 添加金额阈值
        }

    def _generate_analysis_prompt(self, analysis_key, data):
        """生成分析提示词"""
        template = self.prompt_templates.get(analysis_key, '')

        try:
            return template.format(**data)
        except KeyError as e:
            print(f"⚠️ 提示词模板缺少参数: {e}")
            return f"请分析电信诈骗案件数据: {data}"

    def validate_analysis_data(self, data):
        """验证分析数据的准确性（动态日期版本）"""
        issues = []

        # 动态获取当前日期
        today = datetime.now().date()

        # 检查日期一致性
        if '分析周期' in str(data):
            end_date_str = data.get('analysis_end_date', '')
            expected_end = today.strftime('%m月%d日')

            if end_date_str != expected_end:
                issues.append(f"分析结束日期应为{expected_end}，当前为{end_date_str}")

        # 检查数据计算一致性
        if 'last_7_days_total' in data and 'last_7_days_avg' in data:
            expected_avg = round(data['last_7_days_total'] / 7, 1)
            if abs(data['last_7_days_avg'] - expected_avg) > 0.1:
                issues.append(f"近7天日均值计算错误: {data['last_7_days_avg']}，应为{expected_avg}")

        # 检查变化率计算
        if 'last_7_days_total' in data and 'prev_7_days_total' in data and 'change_rate_vs_prev_7_days' in data:
            if data['prev_7_days_total'] > 0:
                expected_rate = (data['last_7_days_total'] - data['prev_7_days_total']) / data['prev_7_days_total']
                if abs(data['change_rate_vs_prev_7_days'] - expected_rate) > 0.01:
                    issues.append(
                        f"环比变化率计算错误: {data['change_rate_vs_prev_7_days']:.1%}，应为{expected_rate:.1%}")

        return issues

    def _display_analysis_result(self, analysis_name, result, analysis_data=None):
        """显示分析结果 - 增强版本"""
        print(f"\n{'=' * 70}")
        print(f"🤖 {analysis_name}")
        print("=" * 70)

        # 先显示核心数据概览
        if analysis_data:
            print("📊 核心数据概览：")
            print("-" * 40)
            print(f"📈 案件总量：{analysis_data.get('total_cases', 0)}起（近{analysis_data.get('days', 0)}天）")
            print(f"📊 日均水平：{analysis_data.get('daily_avg', 0):.1f}起")
            print(f"📋 最新单日：{analysis_data.get('current_cases', 0)}起")

            if 'last_7_days_total' in analysis_data:
                print(f"🗓️ 近7天累计：{analysis_data['last_7_days_total']}起")

            if 'change_rate_vs_prev_7_days' in analysis_data:
                change_rate = analysis_data['change_rate_vs_prev_7_days']
                trend_icon = "📈" if change_rate > 0 else "📉" if change_rate < 0 else "➡️"
                print(f"📊 环比变化：{trend_icon} {change_rate:+.1%}")

            print("-" * 40)

        # 再显示AI分析结论
        print("🧠 AI分析结论：")
        print("-" * 40)

        if result:
            if isinstance(result, str):
                if result.startswith("❌"):
                    print(result)
                else:
                    # 格式化分析结果，突出重点
                    formatted_result = result.replace('**', '').replace('*', '•')

                    # 尝试提取关键结论（查找数字编号或项目符号）
                    lines = formatted_result.split('\n')
                    conclusion_lines = []

                    for line in lines:
                        line = line.strip()
                        if line and (
                                line.startswith(('1.', '2.', '3.', '•', '-', '①', '②', '③')) or
                                '结论' in line or '建议' in line or '预警' in line
                        ):
                            conclusion_lines.append(f"  💡 {line}")
                        elif line and len(conclusion_lines) < 5:  # 限制显示行数
                            conclusion_lines.append(f"     {line}")

                    if conclusion_lines:
                        for line in conclusion_lines:
                            print(line)
                    else:
                        # 如果没有找到结构化内容，显示前300字符
                        display_text = formatted_result[:300] + "..." if len(
                            formatted_result) > 300 else formatted_result
                        print(f"  {display_text}")
            else:
                print(f"收到响应: {result}")
        else:
            print("❌ 分析结果为空")

        print("-" * 70)

    def clear_cache(self):
        """清除缓存"""
        try:
            with self._lock:
                self._cache.clear()
                self._df_cache.clear()

            if self.cache_file.exists():
                self.cache_file.unlink()

            print("✅ 缓存已清除")
        except Exception as e:
            print(f"❌ 清除缓存失败: {e}")

    def show_settings(self):
        """显示系统设置"""
        print("\n" + "=" * 60)
        print("⚙️ 系统设置")
        print("=" * 60)

        print("📝 提示词模板管理:")
        print("【1】查看当前提示词模板")
        print("【2】修改趋势分析提示词")
        print("【3】修改案情特征分析提示词")
        print("【4】修改综合分析提示词")
        print("【0】返回主菜单")

        while True:
            choice = input("\n请选择操作: ").strip()

            if choice == '0':
                break
            elif choice == '1':
                self._show_prompt_templates()
            elif choice == '2':
                self._edit_prompt_template('trend_analysis', '趋势分析')
            elif choice == '3':
                self._edit_prompt_template('case_pattern_analysis', '案情特征分析')
            elif choice == '4':
                self._edit_prompt_template('comprehensive_analysis', '综合分析')
            else:
                print("❌ 无效选择，请重新输入")

    def display_analysis_data(self, analysis_data, focus_type='trend'):
        """显示分析数据"""
        if not analysis_data:
            print("❌ 无分析数据")
            return

        if focus_type == 'trend':
            # 趋势分析重点数据
            print(f"📱 近7天累计案件数：{analysis_data['last_7_days_total']:,}起")
            print(f"🔄 对比前7天变化率：{analysis_data['change_rate_vs_prev_7_days']:+.1%}")

            # 自然周统计
            print(f"📅 本周案件数：{analysis_data['current_week_total']:,}起 ({analysis_data['current_week_range']})")
            print(f"📅 上周案件数：{analysis_data['last_week_total']:,}起 ({analysis_data['last_week_range']})")
            print(f"📅 上上周案件数：{analysis_data['two_weeks_ago_total']:,}起 ({analysis_data['two_weeks_ago_range']})")
            print(f"🔄 本周vs上周变化率：{analysis_data['current_vs_last_week_change']:+.1%}")
            print(f"🔄 上周vs上上周变化率：{analysis_data['last_vs_two_weeks_change']:+.1%}")

            print(f"📅 对比上月同期：{analysis_data['last_month_start_date']} - {analysis_data['last_month_end_date']}")
            print(f"📊 本月累计案件数：{analysis_data['current_month_total']:,}起")
            print(f"📅 上个月同期案件数：{analysis_data['last_month_same_period_total']:,}起")
            print(f"🔄 月度变化率：{analysis_data['change_rate_vs_last_month']:+.1%}")
            print(f"📈 最新单日案件数：{analysis_data['current_cases']:,}起")
            print(f"📉 对比近7天日均值变化率：{analysis_data['current_vs_avg_7_days']:+.1%}")


    def _show_prompt_templates(self):
        """显示当前提示词模板"""
        print("\n" + "=" * 50)
        print("📝 当前提示词模板")
        print("=" * 50)

        template_names = {
            'trend_analysis': '趋势分析',
            'case_pattern_analysis': '案情特征分析',
            'comprehensive_analysis': '综合分析'
        }

        for key, template in self.prompt_templates.items():
            name = template_names.get(key, key)
            print(f"\n🔍 {name}:")
            print("-" * 30)
            preview = template[:200] + "..." if len(template) > 200 else template
            print(preview)

    def _edit_prompt_template(self, template_key, template_name):
        """编辑提示词模板"""
        print(f"\n📝 编辑{template_name}提示词")
        print("=" * 40)

        current_template = self.prompt_templates.get(template_key, '')
        print(f"当前模板长度: {len(current_template)} 字符")
        print(f"预览: {current_template[:100]}...")

        print("\n选择操作:")
        print("【1】查看完整模板")
        print("【2】替换整个模板")
        print("【0】返回")

        choice = input("\n请选择: ").strip()

        if choice == '1':
            print(f"\n完整模板:\n{current_template}")
        elif choice == '2':
            print("\n请输入新的提示词模板 (输入 'END' 结束):")
            lines = []
            while True:
                line = input()
                if line.strip() == 'END':
                    break
                lines.append(line)

            new_template = '\n'.join(lines)
            if new_template.strip():
                self.prompt_templates[template_key] = new_template
                print(f"✅ {template_name}提示词已更新")
            else:
                print("❌ 提示词不能为空")
        elif choice == '0':
            return
        else:
            print("❌ 无效选择")

    def run(self):
        """运行主程序"""
        # 初始化检查
        if not self.connect_database():
            print("❌ 数据库连接失败，程序退出")
            return

        while True:
            try:
                self.display_main_menu()
                choice = input("\n请选择功能: ").strip()

                if choice == '0':
                    print("👋 感谢使用，再见！")
                    break
                elif choice == '1':
                    # 日维度图表 - 固定30天，独立获取数据
                    print("\n📊 正在生成日维度图表...")
                    df_daily, _ = self.get_data(days=30)
                    if df_daily is not None:
                        self.create_daily_chart(df_daily)

                elif choice == '2':
                    # 周维度图表 - 固定70天，独立获取数据
                    print("\n📊 正在生成周维度图表...")
                    df_weekly, _ = self.get_data(days=70)
                    if df_weekly is not None:
                        self.create_weekly_chart(df_weekly)

                elif choice == '3':
                    # AI分析 - 独立获取数据
                    print("\n🤖 准备AI分析...")
                    df_analysis, case_details = self.get_data(days=30, include_full_comparison=True)
                    if df_analysis is not None:
                        self.display_analysis_menu()
                        selected_options = self.get_user_selection()
                        if selected_options:
                            self.ai_analysis(df_analysis, case_details, selected_options)

                elif choice == '4':
                    # 统计数据 - 独立获取数据
                    print("\n📊 正在计算统计数据...")
                    df_stats, _ = self.get_data(days=90, include_full_comparison=True)
                    if df_stats is not None:
                        self.display_statistics(df_stats)

                elif choice == '5':
                    # 清除缓存
                    self.clear_cache()

                elif choice == '6':
                    # 系统设置
                    self.show_settings()

                else:
                    print("❌ 无效选择，请重新输入")

                # 操作间隔
                if choice != '0':
                    input("\n按回车键继续...")

            except KeyboardInterrupt:
                print("\n\n👋 程序已中断，再见！")
                break
            except Exception as e:
                print(f"❌ 程序异常: {e}")
                continue

        # 清理资源
        if self.connection:
            self.connection.close()

    def run_analysis(self, selections, df, case_details):
        """执行分析（安全调用版）"""
        try:
            analysis_data = self._prepare_analysis_data(df, case_details)
            # 执行选定的分析
            self.ai_analysis(df, case_details, selections)
            return analysis_data
        except Exception as e:
            print(f"❌ 分析失败: {str(e)}")
            return None

# 主程序入口
def main():
    """主函数"""
    try:
        print("🚀 正在启动中国电信反诈监控系统...")
        monitor = TelecomMonitor()
        monitor.run()
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

# 程序入口点
if __name__ == "__main__":
    main()