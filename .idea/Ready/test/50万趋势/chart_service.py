"""
图表服务
负责生成各种数据可视化图表
"""
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import pandas as pd
import numpy as np
from datetime import datetime
from pathlib import Path


class ChartService:
    """图表服务类"""

    def __init__(self):
        # 使用与测试文件相同的配色方案
        self.colors = {
            'primary': '#0769B6',
            'danger': '#E34234',
            'warning': '#F0BE38',
            'success': '#28A745',
            'neutral': '#6C757D',
            'background': '#F8F9FA',
            'grid': '#E9ECEF',
            'text': '#2C3E50'
        }

        # 添加运营商配置 - 修复 operators 属性缺失
        self.operators = {
            'telecom': {'name': '电信', 'color': '#0769B6'},
            'unicom': {'name': '联通', 'color': '#E60012'},
            'mobile': {'name': '移动', 'color': '#00A0E9'}
        }

        # 添加风险阈值配置 - 与测试文件一致
        self.risk_config = {
            '电信': {'threshold': 2.5, 'high_risk': 5, 'color': '#0769B6'},
            '联通': {'threshold': 2.0, 'high_risk': 4, 'color': '#E60012'},
            '移动': {'threshold': 3.0, 'high_risk': 6, 'color': '#00A0E9'}
        }

        self.export_dir = Path('/Users/<USER>/Documents/测试')
        self.export_dir.mkdir(exist_ok=True)

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
        plt.rcParams['axes.unicode_minus'] = False

        print("📊 图表服务初始化完成")

    def create_daily_chart(self, df: pd.DataFrame, save_chart: bool = False, save_path=None) -> bool:
        """创建日维度图表 - 完全参考测试*.py的样式"""
        try:
            if df is None or df.empty:
                print("⚠️ 无数据可绘制日维度图表")
                return False

            # 使用更高效的绘图方式 - 完全参考测试文件
            fig, ax = plt.subplots(figsize=(16, 9))
            fig.patch.set_facecolor(self.colors['background'])

            if '电信' in df.columns:
                x_data = df['日期'].values
                y_data = df['电信'].values
                color = self.risk_config['电信']['color']

                # 优化绘图 - 完全参考测试文件
                ax.plot(x_data, y_data, color=color, linewidth=3.0,
                        label='电信', marker='o', markersize=6)
                ax.fill_between(x_data, y_data, alpha=0.25, color=color)

                # 风险阈值线 - 完全参考测试文件
                config = self.risk_config['电信']
                ax.axhline(y=config['threshold'], color=self.colors['warning'],
                           linestyle='--', alpha=0.8, linewidth=2,
                           label=f'电信警告阈值({config["threshold"]})')
                ax.axhline(y=config['high_risk'], color=self.colors['danger'],
                           linestyle='-.', alpha=0.9, linewidth=2.5,
                           label=f'电信高风险阈值({config["high_risk"]})')

            # 统一样式设置 - 完全参考测试文件
            self._setup_chart_style(ax, '中国电信反诈案件日维度监控图表(涉案金额 ≥ 50万元)')

            # 优化日期格式 - 完全参考测试文件
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=2))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=0, ha='center', fontsize=12)

            plt.tight_layout()

            # 先显示图表预览
            plt.show()

            # 显示后询问保存
            if save_path is None:
                save_path = self.ask_save_chart('日维度')

            if save_path:
                # 使用保存的图表对象引用来保存
                fig.savefig(save_path, dpi=300, bbox_inches='tight',
                           facecolor='white', edgecolor='none')
                print(f"✅ 图表已保存至: {save_path}")

            return True

        except Exception as e:
            print(f"❌ 日维度图表生成失败: {e}")
            return False

    def _setup_chart_style(self, ax, title):
        """设置图表样式 - 完全参考测试文件的_setup_chart_style"""
        # 标题样式
        ax.set_title(title, fontsize=18, fontweight='bold',
                     color=self.colors['text'], pad=20)

        # 设置背景色
        ax.set_facecolor(self.colors['background'])

        # 坐标轴标签
        ax.set_xlabel('日期', fontsize=14, fontweight='bold', color=self.colors['text'])
        ax.set_ylabel('案件数量', fontsize=14, fontweight='bold', color=self.colors['text'])

        # 网格设置 - 只显示y轴网格
        ax.grid(True, alpha=0.3, color=self.colors['grid'], axis='y')

        # 图例设置
        legend = ax.legend(loc='upper left', frameon=True, fancybox=True, shadow=True,
                           fontsize=12, markerscale=1.2)
        if legend:
            legend.get_frame().set_facecolor('white')
            legend.get_frame().set_alpha(0.9)

        # 刻度样式
        ax.tick_params(axis='both', which='major', labelsize=12, colors=self.colors['text'])

        # 设置坐标轴颜色
        ax.spines['bottom'].set_color(self.colors['text'])
        ax.spines['left'].set_color(self.colors['text'])
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)

    def ask_save_chart(self, chart_type):
        """询问是否保存图表 - 简化版，参考测试文件"""
        save_choice = input(f"\n💾 是否保存{chart_type}图表？(y/n，默认n): ").strip().lower()
        if save_choice in ['y', 'yes', '是', '保存']:
            # 自动生成保存路径
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{chart_type}_{timestamp}.png"
            save_path = self.export_dir / filename
            print(f"📁 图表将保存至: {save_path}")
            return str(save_path)
        return None

    def create_weekly_chart(self, df: pd.DataFrame, save_chart: bool = False, save_path=None) -> bool:
        """创建周维度图表 - 使用配置文件中的颜色"""
        try:
            if df is None or df.empty:
                print("⚠️ 无数据可绘制周维度图表")
                return False

            # 检查week_label列是否存在
            if 'week_label' not in df.columns:
                print("❌ 数据格式不正确，缺少week_label列")
                print(f"📊 当前数据列: {list(df.columns)}")
                return False

            # 确保运营商列存在
            operators = ['电信', '联通', '移动']
            for operator in operators:
                if operator not in df.columns:
                    df[operator] = 0

            fig, ax = plt.subplots(figsize=(16, 9))
            fig.patch.set_facecolor(self.colors['background'])

            # 修复颜色获取逻辑 - 从risk_config中获取颜色
            x_pos = range(len(df))
            width = 0.25

            for i, operator in enumerate(operators):
                if operator in df.columns:
                    # 正确获取颜色值
                    color = self.risk_config.get(operator, {}).get('color', self.colors['primary'])
                    ax.bar([x + i * width for x in x_pos], df[operator],
                           width, label=f'{operator}用户案件',
                           color=color, alpha=0.85,
                           edgecolor='white', linewidth=1.2)

            # 美化图表
            ax.set_title('三大运营商涉诈案件周维度统计(涉案金额 ≥ 50万元)',
                         fontsize=24, fontweight='bold', pad=20, color=self.colors['text'])
            ax.set_xlabel('周期', fontsize=14, color=self.colors['text'])
            ax.set_ylabel('案件数量', fontsize=14, color=self.colors['text'])

            # 设置x轴标签
            ax.set_xticks([x + width for x in x_pos])
            ax.set_xticklabels(df['week_label'], fontsize=12, color=self.colors['text'], rotation=0)

            # 设置纵坐标
            plt.setp(ax.yaxis.get_majorticklabels(), fontsize=12, color=self.colors['text'])

            # 网格和样式
            ax.grid(True, alpha=0.3, color=self.colors['grid'], linewidth=0.8, axis='y')
            ax.set_facecolor(self.colors['background'])

            # 图例
            legend = ax.legend(loc='upper right', frameon=True, fancybox=True, shadow=True,
                               fontsize=14, markerscale=1.5)
            legend.get_frame().set_facecolor('white')
            legend.get_frame().set_alpha(0.95)

            # 移除边框
            for spine in ax.spines.values():
                spine.set_visible(False)

            plt.tight_layout()

            # 先显示图表预览
            plt.show()

            # 显示后询问保存
            if save_path is None:
                save_path = self.ask_save_chart('周维度')

            if save_path:
                # 使用保存的图表对象引用来保存
                fig.savefig(save_path, dpi=300, bbox_inches='tight',
                           facecolor='white', edgecolor='none')
                print(f"✅ 图表已保存至: {save_path}")

            return True

        except Exception as e:
            print(f"❌ 周维度图表生成失败: {e}")
            return False

    def export_chart(self, chart_type: str = "daily", filename: str = None) -> bool:
        """导出图表到文件"""
        try:
            if filename is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"{chart_type}_chart_{timestamp}.png"

            filepath = self.export_dir / filename

            # 这里应该基于chart_type调用相应的图表生成方法
            # 暂时返回True，实际使用时需要配合数据
            print(f"📁 图表导出功能准备就绪: {filepath}")
            return True

        except Exception as e:
            print(f"❌ 图表导出失败: {e}")
            return False