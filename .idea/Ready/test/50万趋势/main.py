#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电信反诈监控系统启动脚本
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from telecom_monitor import main

    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保所有依赖已安装: pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"❌ 程序运行失败: {e}")
    sys.exit(1)