"""
数据服务
统一的数据获取和处理接口
"""
import pandas as pd
import pymysql
import time
from datetime import datetime, timedelta
from typing import Optional, Tuple
import sqlalchemy
from sqlalchemy import create_engine, text
from urllib.parse import quote_plus
import sys
import os


# 数据库配置类
class DatabaseConfig:
    def __init__(self):
        self.host = '************'
        self.port = 57861
        self.user = 'FzUser'
        self.password = 'jZ4%fQ3}oI8(jH7)'
        self.database = 'antiFraudPlatform'
        self.charset = 'utf8mb4'


class DataService:
    def __init__(self):
        self.config = DatabaseConfig()  # 直接实例化配置
        self.connection = None
        self.engine = None
        self.is_connected = False
        self._operators = ['电信', '联通', '移动']
        print("📊 数据服务初始化完成")

    def connect(self) -> bool:
        """连接数据库"""
        try:
            password_encoded = quote_plus(self.config.password)
            connection_string = (
                f"mysql+pymysql://{self.config.user}:{password_encoded}@"
                f"{self.config.host}:{self.config.port}/{self.config.database}"
                f"?charset={self.config.charset}"
            )

            self.engine = create_engine(
                connection_string,
                pool_size=10,           # 连接池大小
                max_overflow=20,        # 最大溢出连接
                pool_timeout=30,        # 获取连接超时
                pool_recycle=3600,      # 连接回收时间
                pool_pre_ping=True,     # 连接前ping测试
                echo=False              # SQL日志控制
            )

            # 测试连接
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))

            self.is_connected = True
            print(f"✅ 数据库连接成功: {self.config.host}:{self.config.port}/{self.config.database}")
            return True

        except sqlalchemy.exc.SQLAlchemyError as e:
            print(f"❌ 数据库连接失败 (SQLAlchemy错误): {e}")
            self.is_connected = False
            return False
        except Exception as e:
            print(f"❌ 数据库连接失败 (未知错误): {e}")
            self.is_connected = False
            return False

    def _execute_query(self, query: str) -> pd.DataFrame:
        """执行SQL查询 - 增强错误处理和重试机制"""
        max_retries = 3
        retry_delay = 1

        for attempt in range(max_retries):
            try:
                if not self.is_connected:
                    if not self.connect():
                        print(f"⚠️ 第{attempt + 1}次连接尝试失败")
                        if attempt < max_retries - 1:
                            time.sleep(retry_delay)
                            continue
                        return pd.DataFrame()

                # 使用SQLAlchemy引擎执行查询
                df = pd.read_sql(query, self.engine)
                return df

            except sqlalchemy.exc.DisconnectionError as e:
                print(f"⚠️ 数据库连接断开 (第{attempt + 1}次尝试): {e}")
                self.is_connected = False
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
            except sqlalchemy.exc.TimeoutError as e:
                print(f"⚠️ 查询超时 (第{attempt + 1}次尝试): {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
            except sqlalchemy.exc.SQLAlchemyError as e:
                print(f"❌ SQL执行错误: {e}")
                return pd.DataFrame()
            except Exception as e:
                print(f"❌ 查询执行失败 (未知错误): {e}")
                return pd.DataFrame()

        print(f"❌ 查询失败，已重试{max_retries}次")
        return pd.DataFrame()

    def get_daily_data(self, days: int = 30) -> Tuple[Optional[pd.DataFrame], Optional[pd.DataFrame]]:
        """获取日维度数据 - 只显示电信数据，统计截止到昨天"""
        if not self.is_connected and not self.connect():
            return None, None

        try:
            # 修改：统计截止到昨天，因为当天数据可能还未及时入库
            yesterday = datetime.now() - timedelta(days=1)
            end_date = yesterday.strftime('%Y-%m-%d')
            start_date = (yesterday - timedelta(days=days-1)).strftime('%Y-%m-%d')

            print("查询数据：" + start_date + " 到 " + end_date + " (近30天，截止昨天)")

            # 只查询电信数据
            query = text("""
            SELECT
                DATE(insert_day) as date_col,
                SUM(CASE WHEN suspect_phone_info LIKE :telecom THEN 1 ELSE 0 END) as telecom
            FROM anti_fraud_case_new
            WHERE DATE(insert_day) BETWEEN :start_date AND :end_date
              AND involved_amount >= 500000
            GROUP BY DATE(insert_day)
            ORDER BY DATE(insert_day)
            """)

            # 添加查询超时控制
            with self.engine.connect() as conn:
                # 设置查询超时为60秒
                conn = conn.execution_options(autocommit=True)
                df = pd.read_sql(query, conn, params={
                    'telecom': '%电信%',
                    'start_date': start_date,
                    'end_date': end_date
                })

            detail_query = text("""
            SELECT
                DATE(insert_day) as case_date,
                brief_case_description as case_desc,
                involved_amount,
                suspect_phone_info
            FROM anti_fraud_case_new
            WHERE DATE(insert_day) BETWEEN :start_date AND :end_date
              AND involved_amount >= 500000
              AND suspect_phone_info LIKE :telecom
            ORDER BY insert_day DESC
            LIMIT 50
            """)

            # 详情查询也添加超时控制
            with self.engine.connect() as conn:
                conn = conn.execution_options(autocommit=True)
                case_details = pd.read_sql(detail_query, conn, params={
                    'telecom': '%电信%',
                    'start_date': start_date,
                    'end_date': end_date
                })

            if not df.empty:
                df = df.rename(columns={
                    'date_col': '日期',
                    'telecom': '电信'
                })
                df['日期'] = pd.to_datetime(df['日期'])
                df = self._fill_missing_dates(df, start_date, end_date)

                print("查询成功，共 " + str(len(df)) + " 条记录")
                return df, case_details
            else:
                print("查询结果为空")
                return self._create_empty_df(start_date, end_date), case_details

        except Exception as e:
            print("查询失败: " + str(e))
            return None, None

    def get_weekly_data(self) -> Tuple[Optional[pd.DataFrame], None]:
        """获取周维度数据 - 近8周完整数据"""
        if not self.is_connected and not self.connect():
            return None, None

        try:
            # 计算8周的完整日期范围
            today = datetime.now()
            yesterday = today - timedelta(days=1)  # 统计到昨天

            # 找到昨天所在周的周一
            current_week_monday = yesterday - timedelta(days=yesterday.weekday())

            # 向前推8周，找到起始周的周一
            start_week_monday = current_week_monday - timedelta(weeks=7)

            # 计算实际查询的日期范围
            start_date = start_week_monday.strftime('%Y-%m-%d')
            end_date = yesterday.strftime('%Y-%m-%d')

            print(f"🔍 查询周维度数据: {start_date} 到 {end_date} (近8周)")

            query = text("""
            SELECT
                YEARWEEK(insert_day, 1) as year_week,
                MIN(DATE(insert_day)) as week_start,
                MAX(DATE(insert_day)) as week_end,
                COUNT(DISTINCT DATE(insert_day)) as days_count,
                SUM(CASE WHEN suspect_phone_info LIKE '%电信%' THEN 1 ELSE 0 END) as telecom,
                SUM(CASE WHEN suspect_phone_info LIKE '%移动%' THEN 1 ELSE 0 END) as mobile,
                SUM(CASE WHEN suspect_phone_info LIKE '%联通%' THEN 1 ELSE 0 END) as unicom
            FROM anti_fraud_case_new
            WHERE DATE(insert_day) BETWEEN :start_date AND :end_date
              AND involved_amount >= 500000
            GROUP BY YEARWEEK(insert_day, 1)
            ORDER BY YEARWEEK(insert_day, 1)
            """)

            df = pd.read_sql(query, self.engine, params={
                'start_date': start_date,
                'end_date': end_date
            })

            print(f"📊 查询结果: {len(df)} 行数据")

            if not df.empty:
                # 补充完整的8周数据结构
                df = self._fill_complete_weeks(df, start_week_monday, current_week_monday)

                print("📋 完整周数据预览:")
                print(df[['year_week', 'week_start', 'days_count', 'telecom', 'mobile', 'unicom']])
                print(
                    f"📈 各运营商总数: 电信={df['telecom'].sum()}, 移动={df['mobile'].sum()}, 联通={df['unicom'].sum()}")

                df['week_start'] = pd.to_datetime(df['week_start'])
                # 基于year_week生成正确的week_label
                df['week_label'] = df['year_week'].apply(lambda x: f"{str(x)[:4]}-{str(x)[4:]}周")

                # 重命名列以匹配图表服务的期望
                df = df.rename(columns={
                    'telecom': '电信',
                    'mobile': '移动',
                    'unicom': '联通'
                })
            else:
                print("⚠️ 查询结果为空，创建空的8周数据结构")
                df = self._create_empty_weeks(start_week_monday, current_week_monday)

            return df, None

        except Exception as e:
            print(f"❌ 周维度查询失败: {e}")
            return None, None

    def _fill_complete_weeks(self, df: pd.DataFrame, start_monday: datetime, end_monday: datetime) -> pd.DataFrame:
        """填充完整的8周数据结构"""
        try:
            # 生成完整的8周列表
            complete_weeks = []
            current_monday = start_monday

            while current_monday <= end_monday:
                # 使用与MySQL YEARWEEK(date, 1)相同的计算逻辑
                year = current_monday.year
                # ISO周次计算（周一为一周开始）
                iso_year, iso_week, _ = current_monday.isocalendar()
                # 如果ISO年份与日历年份不同，需要调整
                if iso_year != year:
                    if current_monday.month == 1:  # 年初
                        week_num = iso_week
                    else:  # 年末
                        week_num = 1
                else:
                    week_num = iso_week
                year_week = f"{year}{week_num:02d}"
                week_start = current_monday.strftime('%Y-%m-%d')

                complete_weeks.append({
                    'year_week': year_week,
                    'week_start': week_start,
                    'days_count': 0,
                    'telecom': 0,
                    'mobile': 0,
                    'unicom': 0
                })
                current_monday += timedelta(weeks=1)

            # 创建完整周数据框
            complete_df = pd.DataFrame(complete_weeks)

            # 如果有查询数据，则合并
            if not df.empty:
                # 直接使用数据库返回的year_week值
                for _, row in df.iterrows():
                    db_year_week = str(row['year_week'])  # 数据库返回的YEARWEEK值
                    mask = complete_df['year_week'] == db_year_week
                    if mask.any():
                        complete_df.loc[mask, 'days_count'] = row['days_count']
                        complete_df.loc[mask, 'telecom'] = row['telecom']
                        complete_df.loc[mask, 'mobile'] = row['mobile']
                        complete_df.loc[mask, 'unicom'] = row['unicom']

            return complete_df

        except Exception as e:
            print(f"❌ 填充完整周数据失败: {e}")
            return df

    def _create_empty_weeks(self, start_monday: datetime, end_monday: datetime) -> pd.DataFrame:
        """创建空的8周数据结构"""
        try:
            weeks_data = []
            current_monday = start_monday

            while current_monday <= end_monday:
                # 使用与MySQL YEARWEEK(date, 1)相同的计算逻辑
                year = current_monday.year
                iso_year, iso_week, _ = current_monday.isocalendar()
                if iso_year != year:
                    if current_monday.month == 1:
                        week_num = iso_week
                    else:
                        week_num = 1
                else:
                    week_num = iso_week
                year_week = f"{year}{week_num:02d}"
                week_start = current_monday.strftime('%Y-%m-%d')

                weeks_data.append({
                    'year_week': year_week,
                    'week_start': week_start,
                    'week_label': f"{year_week[:4]}-{year_week[4:]}周",
                    'days_count': 0,
                    '电信': 0,
                    '移动': 0,
                    '联通': 0
                })
                current_monday += timedelta(weeks=1)

            return pd.DataFrame(weeks_data)

        except Exception as e:
            print(f"❌ 创建空周数据失败: {e}")
            return pd.DataFrame()

    def get_analysis_data(self) -> Tuple[Optional[pd.DataFrame], Optional[pd.DataFrame]]:
        """获取分析数据"""
        # 修改：为了计算月度对比，需要获取更长时间的数据（至少60天）
        return self._query_data(
            days=70,  # 扩展到70天，确保能覆盖上个月同期
            include_details=True  # 包含案情详情
        )

    def _query_data(self, days: int, include_details: bool = False) -> Tuple[
        Optional[pd.DataFrame], Optional[pd.DataFrame]]:
        """统一数据查询方法"""
        if not self.is_connected and not self.connect():
            return None, None

        try:
            # 计算日期范围 - 修改：统计截止到昨天
            yesterday = datetime.now() - timedelta(days=1)
            end_date = yesterday.strftime('%Y-%m-%d')
            start_date = (yesterday - timedelta(days=days-1)).strftime('%Y-%m-%d')

            print(f"🔍 查询数据：{start_date} 到 {end_date} ({days}天，截止昨天)")

            # 使用简化的查询，类似get_daily_data的方式
            query = text("""
            SELECT
                DATE(insert_day) as date_col,
                SUM(CASE WHEN suspect_phone_info LIKE '%电信%' THEN 1 ELSE 0 END) as telecom,
                SUM(CASE WHEN suspect_phone_info LIKE '%联通%' THEN 1 ELSE 0 END) as unicom,
                SUM(CASE WHEN suspect_phone_info LIKE '%移动%' THEN 1 ELSE 0 END) as mobile
            FROM anti_fraud_case_new
            WHERE DATE(insert_day) BETWEEN :start_date AND :end_date
              AND involved_amount >= 500000
            GROUP BY DATE(insert_day)
            ORDER BY DATE(insert_day)
            """)

            df = pd.read_sql(query, self.engine, params={
                'start_date': start_date,
                'end_date': end_date
            })

            if not df.empty:
                # 数据处理
                df = df.rename(columns={
                    'date_col': '日期',
                    'telecom': '电信',
                    'unicom': '联通',
                    'mobile': '移动'
                })
                df['日期'] = pd.to_datetime(df['日期'])
                df = self._fill_missing_dates(df, start_date, end_date)
                print(f"✅ 获取到 {len(df)} 天数据")
            else:
                print("⚠️ 查询结果为空")
                df = self._create_empty_df(start_date, end_date)

            # 获取案情详情
            case_details = None
            if include_details:
                case_details = self._get_case_details(start_date, end_date)

            return df, case_details

        except Exception as e:
            print(f"❌ 数据查询失败: {e}")
            return None, None

    def _fill_missing_dates(self, df: pd.DataFrame, start_date: str, end_date: str) -> pd.DataFrame:
        """填充缺失的日期 - 缺失日期填充为0（真实情况）"""
        try:
            # 创建完整的日期范围
            date_range = pd.date_range(start=start_date, end=end_date, freq='D')
            full_df = pd.DataFrame({'日期': date_range})

            # 合并数据，缺失值填充为0（表示当天确实没有案件）
            result = full_df.merge(df, on='日期', how='left')

            # 确保运营商列存在并填充0
            for operator in ['电信', '联通', '移动']:
                if operator not in result.columns:
                    result[operator] = 0
                else:
                    result[operator] = result[operator].fillna(0)

            return result.sort_values('日期')

        except Exception as e:
            print(f"❌ 填充日期失败: {e}")
            return df

    def _create_empty_df(self, start_date: str, end_date: str) -> pd.DataFrame:
        """创建空的DataFrame - 当数据库查询无结果时"""
        try:
            date_range = pd.date_range(start=start_date, end=end_date, freq='D')
            empty_df = pd.DataFrame({'日期': date_range})

            # 所有运营商案件数为0（真实反映无案件情况）
            for operator in ['电信', '联通', '移动']:
                empty_df[operator] = 0

            return empty_df

        except Exception as e:
            print(f"❌ 创建空DataFrame失败: {e}")
            return pd.DataFrame()

    def _get_case_details(self, start_date: str, end_date: str) -> pd.DataFrame:
        """获取案情详情"""
        try:
            # 使用简化的查询，类似get_daily_data的方式
            detail_query = text("""
            SELECT
                DATE(insert_day) as case_date,
                brief_case_description as case_desc,
                involved_amount,
                suspect_phone_info
            FROM anti_fraud_case_new
            WHERE DATE(insert_day) BETWEEN :start_date AND :end_date
              AND involved_amount >= 500000
              AND (suspect_phone_info LIKE '%电信%' OR suspect_phone_info LIKE '%联通%' OR suspect_phone_info LIKE '%移动%')
            ORDER BY insert_day DESC
            LIMIT 200
            """)

            return pd.read_sql(detail_query, self.engine, params={
                'start_date': start_date,
                'end_date': end_date
            })
        except Exception as e:
            print(f"⚠️ 案情详情查询失败: {e}")
            return pd.DataFrame()

    def close(self):
        """关闭数据库连接 - 增强资源清理"""
        try:
            if self.engine:
                self.engine.dispose()
                print("📊 数据库连接池已关闭")
            if self.connection:
                self.connection.close()
            self.is_connected = False
        except Exception as e:
            print(f"⚠️ 关闭数据库连接时出错: {e}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口 - 确保资源清理"""
        self.close()