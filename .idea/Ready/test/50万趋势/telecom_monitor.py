"""
电信反诈监控系统主应用
基于配置文件的完整架构
"""
from data_service import DataService
from cache_service import CacheService
from chart_service import ChartService
from statistics_service import StatisticsService
from ai_service import AIService
from ui_service import UIService
from config_manager import config


class TelecomMonitor:
    """电信反诈监控系统主类"""

    def __init__(self):
        print("🚀 正在初始化电信反诈监控系统...")

        # 初始化所有服务
        self.data_service = DataService()
        self.cache_service = CacheService()
        self.chart_service = ChartService()
        self.statistics_service = StatisticsService()
        self.ai_service = AIService()
        self.ui_service = UIService()

        # 加载配置
        self.system_config = config.get('system', {})

        print(f"✅ {self.system_config.get('name', '系统')} 初始化完成")

    def start(self):
        """启动系统"""
        if not self.data_service.connect():
            print("❌ 数据库连接失败，程序退出")
            return False

        return True

    def run(self):
        """运行主程序"""
        if not self.start():
            return

        while True:
            try:
                self.ui_service.display_main_menu()
                choice = input("\n请选择功能: ").strip()

                if choice == '0':
                    print("👋 感谢使用，再见！")
                    break
                elif choice == '1':
                    self._handle_daily_chart()
                elif choice == '2':
                    self._handle_weekly_chart()
                elif choice == '3':
                    self._handle_ai_analysis()
                elif choice == '4':
                    self._handle_statistics()
                elif choice == '5':
                    self._handle_clear_cache()
                elif choice == '6':
                    self._handle_settings()
                else:
                    print("❌ 无效选择，请重新输入")

                if choice != '0':
                    input("\n按回车键继续...")

            except KeyboardInterrupt:
                print("\n\n👋 程序已中断，再见！")
                break
            except Exception as e:
                print(f"❌ 程序异常: {e}")
                continue

        self.shutdown()

    def _handle_daily_chart(self):
        """处理日维度图表显示"""
        try:
            print("📊 正在生成日维度图表...")

            # 获取数据
            df, case_details = self.data_service.get_daily_data(30)

            if df is None or df.empty:
                print("❌ 无法获取数据")
                return

            # 生成图表
            success = self.chart_service.create_daily_chart(df)

            if success:
                print("✅ 日维度图表生成完成")
            else:
                print("❌ 日维度图表生成失败")

        except Exception as e:
            print(f"❌ 日维度图表处理失败: {e}")
            import traceback
            traceback.print_exc()

    def _handle_weekly_chart(self):
        """处理周维度图表"""
        print("\n📊 正在生成周维度图表...")
        df, _ = self.get_weekly_data()

        if df is None or df.empty:
            print("❌ 无法获取周维度数据")
            return

        success = self.chart_service.create_weekly_chart(df)
        if success:
            print("✅ 周维度图表生成完成")

    def _handle_ai_analysis(self):
        """处理AI分析"""
        print("\n🤖 准备AI分析...")

        # 检查AI服务
        is_available, message = self.ai_service.check_service()
        if not is_available:
            print(message)
            return

        # 获取数据
        df, case_details = self.get_analysis_data()
        if df is None:
            print("❌ 无法获取分析数据")
            return

        # 显示分析菜单并获取选择
        self.ui_service.display_analysis_menu(self.ai_service)
        selections = self.ui_service.get_analysis_selection(self.ai_service)

        if not selections:
            print("❌ 未选择分析类型")
            return

        # 执行分析
        self._execute_ai_analysis(df, case_details, selections)

    def _execute_ai_analysis(self, df, case_details, selections):
        """执行AI分析"""
        # 计算统计数据
        stats = self.statistics_service.calculate_comprehensive_stats(df, case_details)

        for analysis_type in selections:
            print(f"\n🤖 正在执行{self.ai_service.get_available_analyses().get(analysis_type, analysis_type)}...")

            # 先执行AI分析（包含进度条）
            result = self.ai_service.analyze(analysis_type, stats)

            # 然后显示核心数据概况
            print("\n" + "=" * 60)
            print(f"📋 {self.ai_service.get_available_analyses().get(analysis_type, analysis_type)}结果")
            print("=" * 60)
            self.ui_service.display_analysis_data_overview(stats)

            # 最后显示AI分析结论
            print("🧠 AI分析结论：")
            print("-" * 60)
            # 清理AI结果中的多余空行
            cleaned_result = self._clean_ai_result(result)
            print(cleaned_result)
            print("=" * 60)

    def _clean_ai_result(self, result: str) -> str:
        """清理AI分析结果，去掉所有空行"""
        if not result:
            return result

        # 按行分割，只保留有内容的行
        lines = result.split('\n')
        cleaned_lines = []

        for line in lines:
            stripped = line.strip()
            if stripped:  # 只保留有内容的行
                cleaned_lines.append(stripped)

        return '\n'.join(cleaned_lines)

    def _handle_statistics(self):
        """处理统计数据"""
        print("\n📊 正在计算统计数据...")
        df, case_details = self.get_analysis_data()
        if df is not None:
            stats = self.statistics_service.calculate_comprehensive_stats(df, case_details)
            self.ui_service.display_statistics(stats)
        else:
            print("❌ 无法获取数据")

    def _handle_clear_cache(self):
        """处理清除缓存"""
        self.clear_cache()

    def _handle_settings(self):
        """处理系统设置"""
        while True:
            self.ui_service.display_settings_menu()
            choice = input("\n请选择: ").strip()

            if choice == '0':
                break
            elif choice == '1':
                self.ui_service.display_current_config()
            elif choice == '2':
                self.show_cache_info()
            elif choice == '3':
                self.ui_service.display_ai_service_status(self.ai_service)
            elif choice == '4':
                self.ui_service.show_prompt_templates(self.ai_service)
            elif choice == '5':
                self.ui_service.edit_prompt_template(self.ai_service)
            else:
                print("❌ 无效选择")

            if choice != '0':
                input("\n按回车键继续...")

    def get_daily_data(self, use_cache=True):
        """获取日维度数据"""
        if use_cache:
            cached = self.cache_service.get("daily")
            if cached:
                return cached

        df, case_details = self.data_service.get_daily_data()

        if df is not None and use_cache:
            self.cache_service.set("daily", df, case_details)

        return df, case_details

    def get_weekly_data(self, use_cache=True):
        """获取周维度数据"""
        if use_cache:
            cached = self.cache_service.get("weekly")
            if cached:
                return cached

        df, _ = self.data_service.get_weekly_data()

        if df is not None and use_cache:
            self.cache_service.set("weekly", df)

        return df, None

    def _display_core_data(self, stats: dict):
        """显示核心数据"""
        # 基础数据
        print(f"📅 分析周期：{stats.get('start_date', 'N/A')} - {stats.get('end_date', 'N/A')}")
        print(f"📋 周期内总案件数：{stats.get('total_cases', 0)}起，日均{stats.get('daily_avg', 0):.1f}起")

        # 近期趋势数据
        print(f"📱 近7天累计案件数：{stats.get('last_7_days_total', 0)}起")
        print(f"🔄 对比前7天变化率：{stats.get('change_rate_vs_prev_7_days', 0):+.1%}")

        # 周维度对比
        print(f"📈 本周案件数：{stats.get('current_week_total', 0)}起")
        print(f"📉 上周案件数：{stats.get('last_week_total', 0)}起")
        print(f"🔄 本周vs上周变化率：{stats.get('current_vs_last_week_change', 0):+.1%}")
        print(f"🔄 上周vs上上周变化率：{stats.get('last_vs_two_weeks_change', 0):+.1%}")

        # 月维度对比
        print(f"📊 本月累计案件数：{stats.get('current_month_total', 0)}起")
        print(f"📅 对比上月同期：{stats.get('last_month_start_date', 'N/A')} - {stats.get('last_month_end_date', 'N/A')}")
        print(f"📊 上个月同期案件数：{stats.get('last_month_same_period_total', 0)}起")
        print(f"🔄 月度变化率：{stats.get('change_rate_vs_last_month', 0):+.1%}")

        # 运营商分布
        print(f"📱 运营商分布：")
        print(f"   电信：{stats.get('telecom_total', 0)}起（{stats.get('telecom_ratio', 0):.1%}）")
        print(f"   联通：{stats.get('unicom_total', 0)}起（{stats.get('unicom_ratio', 0):.1%}）")
        print(f"   移动：{stats.get('mobile_total', 0)}起（{stats.get('mobile_ratio', 0):.1%}）")

        # 最新状态
        print(f"📈 最新单日案件数：{stats.get('current_cases', 0)}起")
        print(f"📉 对比近7天日均值变化率：{stats.get('current_vs_avg_7_days', 0):+.1%}")

    def get_analysis_data(self, use_cache=True):
        """获取分析数据"""
        if use_cache:
            cached = self.cache_service.get("analysis")
            if cached:
                return cached

        df, case_details = self.data_service.get_analysis_data()

        if df is not None and use_cache:
            self.cache_service.set("analysis", df, case_details)

        return df, case_details

    def clear_cache(self):
        """清除缓存"""
        self.cache_service.clear()

    def show_cache_info(self):
        """显示缓存信息"""
        self.cache_service.get_cache_info()

    def shutdown(self):
        """关闭系统"""
        self.data_service.close()
        print("👋 系统已关闭")


def main():
    """主函数"""
    try:
        monitor = TelecomMonitor()
        monitor.run()
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()