"""
统计服务
负责计算各种统计指标
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from config_manager import config


class StatisticsService:
    """统计服务类"""

    def __init__(self):
        self.operators = config.get_operators()
        self.business_rules = config.get_business_rules()
        print("📈 统计服务初始化完成")

    def calculate_basic_stats(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算基础统计数据"""
        if df is None or df.empty:
            return {}

        stats = {}

        # 基础数据
        stats['total_cases'] = int(df[['电信', '联通', '移动']].sum().sum())
        stats['daily_avg'] = float(stats['total_cases'] / len(df))

        # 运营商统计
        for operator_key, operator_config in self.operators.items():
            operator_name = operator_config.name
            if operator_name in df.columns:
                total = int(df[operator_name].sum())
                stats[f'{operator_key}_total'] = total
                stats[f'{operator_key}_ratio'] = float(total / stats['total_cases']) if stats[
                                                                                            'total_cases'] > 0 else 0.0

        # 时间范围
        stats['start_date'] = df['日期'].min().strftime('%Y-%m-%d')
        stats['end_date'] = df['日期'].max().strftime('%Y-%m-%d')

        # 最新数据
        latest_row = df.iloc[-1]
        stats['current_cases'] = int(latest_row[['电信', '联通', '移动']].sum())

        return stats

    def calculate_trend_stats(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算趋势统计数据"""
        if df is None or df.empty or len(df) < 14:
            return {}

        stats = {}

        # 计算总案件数列
        df_copy = df.copy()
        df_copy['总案件'] = df_copy[['电信', '联通', '移动']].sum(axis=1)

        # 近7天数据
        recent_7_data = df_copy.tail(7)['总案件'].tolist()
        stats['recent_7_days'] = recent_7_data
        stats['recent_7_avg'] = float(np.mean(recent_7_data))

        # 前7天数据（用于对比）
        if len(df_copy) >= 14:
            prev_7_data = df_copy.iloc[-14:-7]['总案件'].tolist()
            prev_7_avg = float(np.mean(prev_7_data))
            stats['vs_prev_7_days'] = float(
                (stats['recent_7_avg'] - prev_7_avg) / prev_7_avg) if prev_7_avg > 0 else 0.0

        return stats

    def calculate_weekly_stats(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算周维度统计"""
        if df is None or df.empty:
            return {}

        stats = {}

        try:
            df_copy = df.copy()
            df_copy['总案件'] = df_copy[['电信', '联通', '移动']].sum(axis=1)
            df_copy['周次'] = df_copy['日期'].dt.isocalendar().week
            df_copy['年份'] = df_copy['日期'].dt.year

            # 按周汇总
            weekly_data = df_copy.groupby(['年份', '周次'])['总案件'].sum().reset_index()

            if len(weekly_data) >= 2:
                current_week = weekly_data.iloc[-1]['总案件']
                last_week = weekly_data.iloc[-2]['总案件']

                stats['current_week_total'] = int(current_week)
                stats['last_week_total'] = int(last_week)
                stats['current_vs_last_week'] = float((current_week - last_week) / last_week) if last_week > 0 else 0.0
                stats['week_change'] = stats['current_vs_last_week']

        except Exception as e:
            print(f"⚠️ 周维度统计计算失败: {e}")

        return stats

    def calculate_monthly_stats(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算月维度统计"""
        if df is None or df.empty:
            return {}

        stats = {}

        try:
            df_copy = df.copy()
            df_copy['总案件'] = df_copy[['电信', '联通', '移动']].sum(axis=1)

            # 当前月数据
            current_month = df_copy['日期'].max().month
            current_year = df_copy['日期'].max().year
            current_month_data = df_copy[
                (df_copy['日期'].dt.month == current_month) &
                (df_copy['日期'].dt.year == current_year)
                ]

            stats['current_month_total'] = int(current_month_data['总案件'].sum())

            # 上月同期数据（如果有的话）
            last_month = current_month - 1 if current_month > 1 else 12
            last_year = current_year if current_month > 1 else current_year - 1

            # 计算同期天数
            current_day = df_copy['日期'].max().day
            last_month_same_period = df_copy[
                (df_copy['日期'].dt.month == last_month) &
                (df_copy['日期'].dt.year == last_year) &
                (df_copy['日期'].dt.day <= current_day)
                ]

            if not last_month_same_period.empty:
                last_month_total = int(last_month_same_period['总案件'].sum())
                stats['last_month_same_period_total'] = last_month_total
                stats['last_month_same_period'] = last_month_total

                if last_month_total > 0:
                    change_rate = float(
                        (stats['current_month_total'] - last_month_total) / last_month_total
                    )
                    stats['current_vs_last_month'] = change_rate
                    stats['month_change'] = change_rate
                    stats['change_rate_vs_last_month'] = change_rate
                else:
                    stats['current_vs_last_month'] = 0.0
                    stats['month_change'] = 0.0
                    stats['change_rate_vs_last_month'] = 0.0

                # 时间范围
                stats['last_month_start_date'] = last_month_same_period['日期'].min().strftime('%Y-%m-%d')
                stats['last_month_end_date'] = last_month_same_period['日期'].max().strftime('%Y-%m-%d')
            else:
                # 没有上月数据时，设置默认值
                stats['last_month_same_period_total'] = 0
                stats['last_month_same_period'] = 0
                stats['current_vs_last_month'] = 0.0
                stats['month_change'] = 0.0
                stats['change_rate_vs_last_month'] = 0.0

        except Exception as e:
            print(f"⚠️ 月维度统计计算失败: {e}")

        return stats

    def calculate_comprehensive_stats(self, df, case_details=None):
        """计算综合统计数据"""
        if df is None or df.empty:
            return self._get_default_stats()

        try:
            # 确保有必要的列
            required_columns = ['电信', '联通', '移动', '日期']
            for col in required_columns:
                if col not in df.columns:
                    print(f"⚠️ 缺少必要列: {col}")
                    return self._get_default_stats()

            # 修改：分离显示数据和计算数据
            # 1. 获取近30天数据用于基础统计显示
            yesterday = datetime.now() - timedelta(days=1)
            display_start_date = yesterday - timedelta(days=29)  # 30天

            display_mask = (df['日期'].dt.date >= display_start_date.date()) & \
                          (df['日期'].dt.date <= yesterday.date())
            display_df = df[display_mask].copy()

            if display_df.empty:
                print("⚠️ 近30天数据为空")
                return self._get_default_stats()

            # 基础统计数据（基于近30天）
            display_days = len(display_df)
            display_telecom_data = display_df['电信'].tolist()
            display_unicom_data = display_df['联通'].tolist()
            display_mobile_data = display_df['移动'].tolist()

            # 运营商总计（近30天）- 确保是整数
            display_telecom_total = int(sum(display_telecom_data))
            display_unicom_total = int(sum(display_unicom_data))
            display_mobile_total = int(sum(display_mobile_data))

            # 总案件数（近30天）
            display_all_total = display_telecom_total + display_unicom_total + display_mobile_total

            # 计算运营商比例（近30天）
            display_telecom_ratio = float(display_telecom_total / display_all_total) if display_all_total > 0 else 0.0
            display_unicom_ratio = float(display_unicom_total / display_all_total) if display_all_total > 0 else 0.0
            display_mobile_ratio = float(display_mobile_total / display_all_total) if display_all_total > 0 else 0.0

            # 基础统计（近30天）
            display_total_cases = display_all_total
            display_daily_avg = float(display_total_cases / display_days) if display_days > 0 else 0.0

            # 显示日期范围（近30天）
            display_start_date_str = display_df['日期'].min().strftime('%Y-%m-%d')
            display_end_date_str = display_df['日期'].max().strftime('%Y-%m-%d')

            # 2. 使用全部数据（70天）进行电信专项计算
            telecom_data = df['电信'].tolist()

            # 电信专项统计
            # 1. 电信近7天统计
            telecom_recent_7_days = telecom_data[-7:] if len(telecom_data) >= 7 else telecom_data
            telecom_recent_7_total = int(sum(telecom_recent_7_days))
            telecom_recent_7_avg = float(telecom_recent_7_total / len(telecom_recent_7_days)) if telecom_recent_7_days else 0.0

            # 2. 电信前7天统计（用于对比）
            telecom_prev_7_days = telecom_data[-14:-7] if len(telecom_data) >= 14 else []
            telecom_prev_7_total = int(sum(telecom_prev_7_days)) if telecom_prev_7_days else 0
            telecom_prev_7_avg = float(telecom_prev_7_total / len(telecom_prev_7_days)) if telecom_prev_7_days else 0.0

            # 3. 电信7天对比变化率
            telecom_7days_change = float((telecom_recent_7_total - telecom_prev_7_total) / telecom_prev_7_total) if telecom_prev_7_total > 0 else 0.0
            telecom_7days_avg_change = float((telecom_recent_7_avg - telecom_prev_7_avg) / telecom_prev_7_avg) if telecom_prev_7_avg > 0 else 0.0

            # 4. 电信自然周统计
            telecom_week_stats = self._calculate_telecom_week_stats(df)

            # 5. 电信自然月统计
            telecom_month_stats = self._calculate_telecom_month_stats(df)

            # 最新数据（基于近30天）
            current_cases = int(display_telecom_data[-1]) if display_telecom_data else 0

            # 案情样本
            case_samples = self._format_case_samples(case_details)

            # 6. 上周数据计算（用于案情特征分析）
            today = datetime.now()
            # 计算上周的周一和周日
            last_week_monday = today - timedelta(days=today.weekday() + 7)
            last_week_sunday = last_week_monday + timedelta(days=6)

            last_week_start_str = last_week_monday.strftime('%Y-%m-%d')
            last_week_end_str = last_week_sunday.strftime('%Y-%m-%d')

            # 筛选上周数据
            last_week_mask = (df['日期'].dt.date >= last_week_monday.date()) & \
                            (df['日期'].dt.date <= last_week_sunday.date())
            last_week_df = df[last_week_mask].copy()

            # 上周统计
            if not last_week_df.empty:
                last_week_total_cases = int(last_week_df[['电信', '联通', '移动']].sum().sum())
                last_week_daily_avg = float(last_week_total_cases / 7)

                last_week_telecom_total = int(last_week_df['电信'].sum())
                last_week_unicom_total = int(last_week_df['联通'].sum())
                last_week_mobile_total = int(last_week_df['移动'].sum())

                last_week_telecom_ratio = float(last_week_telecom_total / last_week_total_cases) if last_week_total_cases > 0 else 0.0
                last_week_unicom_ratio = float(last_week_unicom_total / last_week_total_cases) if last_week_total_cases > 0 else 0.0
                last_week_mobile_ratio = float(last_week_mobile_total / last_week_total_cases) if last_week_total_cases > 0 else 0.0
            else:
                last_week_total_cases = 0
                last_week_daily_avg = 0.0
                last_week_telecom_total = 0
                last_week_unicom_total = 0
                last_week_mobile_total = 0
                last_week_telecom_ratio = 0.0
                last_week_unicom_ratio = 0.0
                last_week_mobile_ratio = 0.0

            # 完整的统计数据 - 修复后的版本
            stats = {
                # 基础统计（近30天显示）
                'days': display_days,
                'start_date': display_start_date_str,
                'end_date': display_end_date_str,
                'total_cases': display_total_cases,  # 近30天所有运营商总案件数
                'daily_avg': display_daily_avg,      # 近30天所有运营商日均案件数

                # 运营商分布（近30天）
                'telecom_total': display_telecom_total,
                'unicom_total': display_unicom_total,
                'mobile_total': display_mobile_total,
                'telecom_ratio': display_telecom_ratio,
                'unicom_ratio': display_unicom_ratio,
                'mobile_ratio': display_mobile_ratio,

                # 电信专项统计 - 近7天
                'telecom_recent_7_total': telecom_recent_7_total,
                'telecom_recent_7_avg': telecom_recent_7_avg,
                'telecom_prev_7_total': telecom_prev_7_total,
                'telecom_prev_7_avg': telecom_prev_7_avg,
                'telecom_7days_change': telecom_7days_change,
                'telecom_7days_avg_change': telecom_7days_avg_change,

                # 电信专项统计 - 自然周
                **telecom_week_stats,

                # 电信专项统计 - 自然月
                **telecom_month_stats,

                # 兼容性字段（保持原有接口）
                'current_cases': current_cases,
                'recent_7_days': telecom_recent_7_days,
                'recent_7_avg': telecom_recent_7_avg,
                'vs_prev_7_days': telecom_7days_change,
                'current_week_total': telecom_week_stats.get('telecom_current_week_total', 0),
                'last_week_total': telecom_week_stats.get('telecom_last_week_total', 0),
                'week_change': telecom_week_stats.get('telecom_week_change', 0.0),
                'current_vs_last_week': telecom_week_stats.get('telecom_week_change', 0.0),  # 添加缺失的参数
                'current_month_total': telecom_month_stats.get('telecom_current_month_total', 0),
                'last_month_same_period_total': telecom_month_stats.get('telecom_last_month_total', 0),
                'month_change': telecom_month_stats.get('telecom_month_change', 0.0),
                'current_vs_last_month': telecom_month_stats.get('telecom_month_change', 0.0),  # 添加缺失的参数

                # 其他数据
                'data_sequence': telecom_data,
                'case_samples': case_samples,

                # 上周数据（用于案情特征分析）
                'last_week_start_date': last_week_monday.strftime('%Y-%m-%d'),
                'last_week_end_date': last_week_sunday.strftime('%Y-%m-%d'),
                'last_week_days': 7,
                'last_week_total_cases': last_week_total_cases,
                'last_week_daily_avg': last_week_daily_avg,
                'last_week_telecom_total': last_week_telecom_total,
                'last_week_unicom_total': last_week_unicom_total,
                'last_week_mobile_total': last_week_mobile_total,
                'last_week_telecom_ratio': last_week_telecom_ratio,
                'last_week_unicom_ratio': last_week_unicom_ratio,
                'last_week_mobile_ratio': last_week_mobile_ratio
            }

            return stats

        except Exception as e:
            print(f"❌ 综合统计计算失败: {e}")
            return self._get_default_stats()

    def _get_default_stats(self):
        """获取默认统计数据"""
        return {
            'days': 0,
            'current_cases': 0,
            'total_cases': 0,
            'avg_cases': 0,
            'daily_avg': 0,
            'growth_rate': 0,
            'data_sequence': [],
            'mean': 0,
            'std': 0,
            'start_date': '未知',
            'end_date': '未知',
            'telecom_total': 0,
            'unicom_total': 0,
            'mobile_total': 0,
            'telecom_ratio': 0.0,
            'unicom_ratio': 0.0,
            'mobile_ratio': 0.0,
            'recent_7_days': [],
            'recent_7_avg': 0,
            'vs_prev_7_days': 0,
            'current_week_total': 0,
            'last_week_total': 0,
            'week_change': 0,
            'current_vs_last_week': 0.0,  # 添加缺失的参数
            'current_month_total': 0,
            'last_month_same_period': 0,
            'last_month_same_period_total': 0,  # 添加这个别名
            'month_change': 0,
            'current_vs_last_month': 0.0,  # 添加缺失的参数
            'threshold': 2.5,
            'high_risk_threshold': 5,
            'anomaly_threshold': 0,
            'case_samples': "无案情数据",
            # 上周数据默认值
            'last_week_start_date': '未知',
            'last_week_end_date': '未知',
            'last_week_days': 0,
            'last_week_total_cases': 0,
            'last_week_daily_avg': 0.0,
            'last_week_telecom_total': 0,
            'last_week_unicom_total': 0,
            'last_week_mobile_total': 0,
            'last_week_telecom_ratio': 0.0,
            'last_week_unicom_ratio': 0.0,
            'last_week_mobile_ratio': 0.0
        }

    def _format_case_samples(self, case_details: pd.DataFrame) -> str:
        """格式化案情样本"""
        if case_details is None or case_details.empty:
            return "暂无案情详情数据"

        try:
            # 从真实案情数据中提取样本
            samples = []
            for _, row in case_details.head(5).iterrows():
                case_desc = str(row.get('case_desc', row.get('简要案情', '案情描述缺失')))
                amount = row.get('involved_amount', 0)
                phone_info = str(row.get('suspect_phone_info', ''))

                # 格式化金额
                if amount >= 10000:
                    amount_str = f"{amount / 10000:.1f}万元"
                else:
                    amount_str = f"{amount}元"

                samples.append(f"• {case_desc[:50]}... (涉案金额: {amount_str}, 号码归属: {phone_info})")

            return "\n".join(samples)
        except Exception as e:
            print(f"⚠️ 案情样本格式化失败: {e}")
            return "案情数据格式异常"

    def _generate_operator_table(self, df: pd.DataFrame) -> str:
        """生成运营商分布表格 - 基于真实数据"""
        if df is None or df.empty:
            return "暂无数据"

        try:
            # 计算真实的运营商统计
            telecom_total = int(df['电信'].sum()) if '电信' in df.columns else 0
            unicom_total = int(df['联通'].sum()) if '联通' in df.columns else 0
            mobile_total = int(df['移动'].sum()) if '移动' in df.columns else 0
            total = telecom_total + unicom_total + mobile_total

            if total == 0:
                return "暂无案件数据"

            table = "运营商分布统计:\n"
            table += f"电信: {telecom_total}起 ({telecom_total/total*100:.1f}%)\n"
            table += f"联通: {unicom_total}起 ({unicom_total/total*100:.1f}%)\n"
            table += f"移动: {mobile_total}起 ({mobile_total/total*100:.1f}%)\n"
            table += f"总计: {total}起"

            return table
        except Exception as e:
            print(f"⚠️ 运营商表格生成失败: {e}")
            return "数据处理异常"

    def _generate_three_month_table(self, df: pd.DataFrame) -> str:
        """生成三个月对比表格 - 基于真实数据"""
        if df is None or df.empty:
            return "暂无数据"

        try:
            df_copy = df.copy()
            df_copy['年月'] = df_copy['日期'].dt.to_period('M')

            # 按月统计真实数据
            monthly_stats = df_copy.groupby('年月')[['电信', '联通', '移动']].sum()

            if monthly_stats.empty:
                return "暂无月度数据"

            table = "近期月度统计:\n"
            for period, row in monthly_stats.tail(3).iterrows():
                total = int(row['电信'] + row['联通'] + row['移动'])
                table += f"{period}: 电信{int(row['电信'])}起, 联通{int(row['联通'])}起, 移动{int(row['移动'])}起, 合计{total}起\n"

            return table.rstrip()
        except Exception as e:
            print(f"⚠️ 三月对比表格生成失败: {e}")
            return "月度数据处理异常"

    def _calculate_telecom_week_stats(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算电信自然周统计"""
        try:
            from datetime import datetime, timedelta

            # 获取昨天作为基准日期（因为数据截止到昨天）
            yesterday = datetime.now() - timedelta(days=1)

            # 计算本周（自然周：周一到周日）
            current_week_start = yesterday - timedelta(days=yesterday.weekday())
            current_week_end = current_week_start + timedelta(days=6)

            # 计算上周
            last_week_start = current_week_start - timedelta(days=7)
            last_week_end = current_week_start - timedelta(days=1)

            # 筛选本周数据（只统计到昨天）
            current_week_mask = (df['日期'].dt.date >= current_week_start.date()) & \
                               (df['日期'].dt.date <= min(current_week_end.date(), yesterday.date()))
            telecom_current_week_total = int(df[current_week_mask]['电信'].sum())

            # 筛选上周数据（完整一周）
            last_week_mask = (df['日期'].dt.date >= last_week_start.date()) & \
                            (df['日期'].dt.date <= last_week_end.date())
            telecom_last_week_total = int(df[last_week_mask]['电信'].sum())

            # 计算变化率
            telecom_week_change = float((telecom_current_week_total - telecom_last_week_total) / telecom_last_week_total) if telecom_last_week_total > 0 else 0.0

            return {
                'telecom_current_week_total': telecom_current_week_total,
                'telecom_last_week_total': telecom_last_week_total,
                'telecom_week_change': telecom_week_change,
                'current_week_start': current_week_start.strftime('%Y-%m-%d'),
                'current_week_end': min(current_week_end, yesterday).strftime('%Y-%m-%d'),
                'last_week_start': last_week_start.strftime('%Y-%m-%d'),
                'last_week_end': last_week_end.strftime('%Y-%m-%d')
            }

        except Exception as e:
            print(f"⚠️ 电信周统计计算失败: {e}")
            return {
                'telecom_current_week_total': 0,
                'telecom_last_week_total': 0,
                'telecom_week_change': 0.0,
                'current_week_start': '',
                'current_week_end': '',
                'last_week_start': '',
                'last_week_end': ''
            }

    def _calculate_telecom_month_stats(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算电信自然月统计"""
        try:
            from datetime import datetime, timedelta
            import calendar

            # 获取昨天作为基准日期
            yesterday = datetime.now() - timedelta(days=1)

            # 计算本月（自然月：1日到昨天）
            current_month_start = yesterday.replace(day=1)
            current_month_end = yesterday

            # 计算上月同期（上月1日到上月的昨天对应日期）
            if yesterday.month == 1:
                last_month_year = yesterday.year - 1
                last_month = 12
            else:
                last_month_year = yesterday.year
                last_month = yesterday.month - 1

            last_month_start = datetime(last_month_year, last_month, 1)

            # 上月的同一天
            try:
                last_month_same_day = datetime(last_month_year, last_month, yesterday.day)
            except ValueError:
                # 处理月末日期不存在的情况（如2月30日）
                last_month_days = calendar.monthrange(last_month_year, last_month)[1]
                last_month_same_day = datetime(last_month_year, last_month, min(yesterday.day, last_month_days))

            # 调试信息：显示计算的日期范围
            print(f"🔍 月统计调试信息:")
            print(f"   昨天: {yesterday.strftime('%Y-%m-%d')}")
            print(f"   本月范围: {current_month_start.strftime('%Y-%m-%d')} 至 {current_month_end.strftime('%Y-%m-%d')}")
            print(f"   上月同期范围: {last_month_start.strftime('%Y-%m-%d')} 至 {last_month_same_day.strftime('%Y-%m-%d')}")

            # 筛选本月数据
            current_month_mask = (df['日期'].dt.date >= current_month_start.date()) & \
                                (df['日期'].dt.date <= current_month_end.date())
            current_month_data = df[current_month_mask]
            telecom_current_month_total = int(current_month_data['电信'].sum())

            print(f"   本月电信案件: {telecom_current_month_total}")

            # 筛选上月同期数据
            last_month_mask = (df['日期'].dt.date >= last_month_start.date()) & \
                             (df['日期'].dt.date <= last_month_same_day.date())
            last_month_data = df[last_month_mask]
            telecom_last_month_total = int(last_month_data['电信'].sum())

            print(f"   上月同期电信案件: {telecom_last_month_total}")

            # if len(last_month_data) > 0:
            #     print(f"   上月同期数据样本:")
            #     print(last_month_data[['日期', '电信']].head())
            # else:
            #     print(f"   ⚠️ 上月同期数据为空，可能原因:")
            #     print(f"      - 数据库中没有 {last_month_start.strftime('%Y-%m-%d')} 至 {last_month_same_day.strftime('%Y-%m-%d')} 的数据")
            #     print(f"      - 查询的数据时间范围不够长")

            # 计算变化率
            telecom_month_change = float((telecom_current_month_total - telecom_last_month_total) / telecom_last_month_total) if telecom_last_month_total > 0 else 0.0

            return {
                'telecom_current_month_total': telecom_current_month_total,
                'telecom_last_month_total': telecom_last_month_total,
                'telecom_month_change': telecom_month_change,
                'current_month_start': current_month_start.strftime('%Y-%m-%d'),
                'current_month_end': current_month_end.strftime('%Y-%m-%d'),
                'last_month_start': last_month_start.strftime('%Y-%m-%d'),
                'last_month_end': last_month_same_day.strftime('%Y-%m-%d')
            }

        except Exception as e:
            print(f"⚠️ 电信月统计计算失败: {e}")
            return {
                'telecom_current_month_total': 0,
                'telecom_last_month_total': 0,
                'telecom_month_change': 0.0,
                'current_month_start': '',
                'current_month_end': '',
                'last_month_start': '',
                'last_month_end': ''
            }