# 电信反诈监控系统配置文件
system:
  name: "中国电信反诈监控系统"
  version: "2.0"
  author: "AI Assistant"

# 数据库配置
database:
  host: "************"
  port: 57861
  user: "FzUser"
  password: "jZ4%fQ3}oI8(jH7)"
  database: "antiFraudPlatform"
  charset: "utf8mb4"
  timeout: 30

# 数据查询配置
data_query:
  daily:
    days: 30
    description: "日维度数据"
    cache_key: "daily_data"
  weekly:
    days: 70
    description: "周维度数据"
    cache_key: "weekly_data"
    min_complete_weeks: 8
  analysis:
    days: 30
    description: "AI分析数据"
    cache_key: "analysis_data"
    include_case_details: true
    case_details_limit: 200

# 运营商配置
operators:
  telecom:
    name: "电信"
    color: "#0769B6"
    sql_pattern: "%电信%"
    risk_threshold: 2.5
    high_risk: 5
  unicom:
    name: "联通"
    color: "#E60012"
    sql_pattern: "%联通%"
    risk_threshold: 2.0
    high_risk: 4
  mobile:
    name: "移动"
    color: "#00A0E9"
    sql_pattern: "%移动%"
    risk_threshold: 3.0
    high_risk: 6

# 缓存配置
cache:
  enabled: true
  directory: "../cache"
  memory_cache: true
  file_cache: true
  expire_hours: 24

# 文件路径配置
paths:
  export_dir: "/Users/<USER>/Documents/测试"
  cache_dir: "../cache"
  log_dir: "../logs"
  prompts_file: "prompts.yaml"  # 新增提示词配置文件路径

# AI分析配置
# AI分析配置
ai_analysis:
  model: "gemma3n:e2b"
  timeout: 90
  max_retries: 2
  temperature: 0.7
  num_ctx: 4096
  options:
    trend_analysis:
      name: "趋势分析"
      description: "分析案件数量变化趋势"
    risk_assessment:
      name: "风险评估"
      description: "评估当前风险等级"
    anomaly_detection:
      name: "异常检测"
      description: "检测数据异常情况"
    case_pattern_analysis:
      name: "案情特征分析"
      description: "分析案件特征模式"
    comprehensive_analysis:
      name: "综合分析"
      description: "全面综合分析"

# SQL查询模板
sql_templates:
  main_query: |
    SELECT 
        DATE(insert_day) as date_col,
        SUM(CASE WHEN suspect_phone_info LIKE '{telecom_pattern}' THEN 1 ELSE 0 END) as 电信,
        SUM(CASE WHEN suspect_phone_info LIKE '{unicom_pattern}' THEN 1 ELSE 0 END) as 联通,
        SUM(CASE WHEN suspect_phone_info LIKE '{mobile_pattern}' THEN 1 ELSE 0 END) as 移动
    FROM anti_fraud_case_new 
    WHERE DATE(insert_day) BETWEEN '{start_date}' AND '{end_date}'
      AND involved_amount >= {amount_threshold}
    GROUP BY DATE(insert_day)
    ORDER BY DATE(insert_day)

  case_details_query: |
    SELECT 
        DATE(insert_day) as case_date,
        brief_case_description as 简要案情,
        event_sequence as 事件顺序,
        victim_age as 年龄,
        case_sub_type as 案件子类,
        involved_amount,
        suspect_phone_info
    FROM anti_fraud_case_new 
    WHERE DATE(insert_day) BETWEEN '{start_date}' AND '{end_date}'
      AND involved_amount >= {amount_threshold}
      AND (suspect_phone_info LIKE '{telecom_pattern}' 
           OR suspect_phone_info LIKE '{unicom_pattern}' 
           OR suspect_phone_info LIKE '{mobile_pattern}')
    ORDER BY insert_day DESC
    LIMIT {limit}

# 业务规则配置
business_rules:
  amount_threshold: 500000  # 涉案金额阈值（50万）
  complete_week_days: [0, 1, 2, 3, 4, 5, 6]  # 完整周必须包含的工作日
  max_case_details: 200  # 最大案情详情数量

# 图表配置
charts:
  daily:
    title: "电信诈骗案件日维度统计"
    figsize: [15, 8]
    dpi: 300
    style: "seaborn-v0_8"
  weekly:
    title: "电信诈骗案件周维度统计"
    figsize: [15, 8]
    dpi: 300
    style: "seaborn-v0_8"

# 颜色主题
colors:
  primary: "#0769B6"
  danger: "#E34234"
  warning: "#F0BE38"
  success: "#28A745"
  neutral: "#6C757D"
  background: "#F8F9FA"
  grid: "#E9ECEF"
  text: "#2C3E50"