"""
AI分析服务
集成提示词管理和AI模型调用
"""
import time
import sys
import threading
import ollama
from typing import Dict, Any, Optional, List
from config_manager import config
from prompt_manager import prompt_manager



class AIAnalysisProgressBar:
    """AI智能分析专用进度条 - 4个阶段同时显示，完成后保持显示"""

    def __init__(self):
        self.stages = [
            {"name": "提示词已生成", "icon": "📝", "duration": 3, "progress": 0, "completed": False},
            {"name": "正在准备数据", "icon": "📃", "duration": 2, "progress": 0, "completed": False},
            {"name": "正在启动模型", "icon": "🤖", "duration": 5, "progress": 0, "completed": False},
            {"name": "智能分析处理", "icon": "🤖", "duration": 0, "progress": 0, "completed": False}
        ]
        self.current_stage = 0
        self.start_time = None
        self.stage_start_time = None
        self.prompt_length = 0
        self.bar_length = 25  # 增加25%：20 * 1.25 = 25
        self.running = False
        self._initialized = False
        self._last_stage_completed = False  # 防止重复打印最后阶段

    def start(self, prompt_length=0, analysis_name="AI分析"):
        """开始进度显示"""
        self.start_time = time.time()
        self.stage_start_time = time.time()
        self.prompt_length = prompt_length
        self.current_stage = 0
        self.running = True
        self._initialized = False
        self._last_stage_completed = False
        self.analysis_name = analysis_name

    def next_stage(self):
        """进入下一个阶段"""
        if self.current_stage < len(self.stages) - 1:
            # 标记当前阶段为完成并显示
            self.stages[self.current_stage]["completed"] = True
            self.stages[self.current_stage]["progress"] = 100

            # 显示完成的阶段（带颜色）
            bar = "█" * self.bar_length
            stage_name = self.stages[self.current_stage]["name"]
            icon = "✅"
            elapsed_time = self.stages[self.current_stage]["duration"]

            extra_info = ""
            if self.current_stage == 0 and self.prompt_length > 0:
                extra_info = f"（提示词长度{self.prompt_length}字符）"
            elif self.current_stage == 2:
                extra_info = "（启动Ollama本地模型）"

            elapsed_str = f"{elapsed_time:05.1f}s"
            # 添加绿色颜色和统一对齐
            color = "\033[32m"  # 绿色
            reset = "\033[0m"   # 重置颜色
            progress_line = f"\r{icon} {stage_name.ljust(18)} {color}|{bar}|{reset} 100% 用时> {elapsed_str}{extra_info}"
            print(progress_line + " " * 30)  # 完成当前行并换行

            # 进入下一阶段
            self.current_stage += 1
            self.stage_start_time = time.time()

    def display_progress(self):
        """显示所有阶段的进度状态 - 不使用ANSI控制，避免滚屏"""
        if not self.running:
            return

        current_time = time.time()

        # 只在第一次显示标题和初始状态
        if not self._initialized:
            print("\n" + "-" * 50)
            print(f"🤖 正在执行{self.analysis_name}...")
            self._initialized = True
            # 不显示初始状态，等待第一次更新

        # 计算当前阶段的进度
        stage_elapsed = current_time - self.stage_start_time

        if self.current_stage < len(self.stages) - 1:
            # 非最后阶段，根据时间计算进度
            stage_duration = self.stages[self.current_stage]["duration"]
            current_progress = min(int((stage_elapsed / stage_duration) * 100), 99)
        else:
            # 最后阶段，模拟进度
            if stage_elapsed < 10:
                current_progress = int(stage_elapsed * 3)  # 前10秒到30%
            elif stage_elapsed < 30:
                current_progress = 30 + int((stage_elapsed - 10) * 2)  # 10-30秒到70%
            else:
                current_progress = min(70 + int((stage_elapsed - 30) * 0.5), 99)  # 30秒后缓慢增长

        # 更新当前阶段的进度
        self.stages[self.current_stage]["progress"] = current_progress

        # 构建当前阶段的进度条
        filled = int(self.bar_length * current_progress / 100)
        bar = "█" * filled + "░" * (self.bar_length - filled)

        # 额外信息
        extra_info = ""
        if self.current_stage == 0 and self.prompt_length > 0:
            extra_info = f"（提示词长度{self.prompt_length}字符）"
        elif self.current_stage == 2:
            extra_info = "（启动Ollama本地模型）"

        # 只显示当前阶段的进度，使用\r覆盖，添加颜色
        elapsed_str = f"{stage_elapsed:05.1f}s"
        stage_name = self.stages[self.current_stage]["name"]
        icon = self.stages[self.current_stage]["icon"]

        # 根据进度选择颜色
        if current_progress < 30:
            color = "\033[33m"  # 黄色
        elif current_progress < 70:
            color = "\033[36m"  # 青色
        else:
            color = "\033[32m"  # 绿色
        reset = "\033[0m"

        progress_line = f"\r{icon} {stage_name.ljust(18)} {color}|{bar}|{reset} {current_progress:3d}% 用时> {elapsed_str}{extra_info}"
        print(progress_line + " " * 30, end="", flush=True)

    def complete(self):
        """完成所有阶段 - 直接在当前行完成，不重新打印"""
        if not self.running:  # 如果已经停止，直接返回，避免重复调用
            return

        self.running = False
        total_time = time.time() - self.start_time

        # 直接在当前显示的进度条上更新为100%，不重新打印
        if not self._last_stage_completed:
            bar = "█" * self.bar_length
            stage_name = self.stages[self.current_stage]["name"]
            icon = "✅"
            last_stage_time = total_time - sum(s["duration"] for s in self.stages[:-1])

            elapsed_str = f"{last_stage_time:05.1f}s"
            color = "\033[32m"  # 绿色
            reset = "\033[0m"
            # 直接覆盖当前行，显示100%完成状态
            progress_line = f"\r{icon} {stage_name.ljust(18)} {color}|{bar}|{reset} 100% 用时> {elapsed_str}"
            print(progress_line + " " * 30)  # 换行，完成进度条显示
            self._last_stage_completed = True

        # 显示完成信息
        print(f"\n✅ AI分析完成，总耗时: {total_time:.1f}秒")
        print("-" * 50)


class AIService:
    """AI分析服务类"""

    def __init__(self):
        self.ai_config = config.get_ai_config()
        self.model = self.ai_config.get('model', 'gemma3n:e2b')
        self.timeout = self.ai_config.get('timeout', 90)
        self.max_retries = self.ai_config.get('max_retries', 2)
        self.temperature = self.ai_config.get('temperature', 0.7)
        self.num_ctx = self.ai_config.get('num_ctx', 4096)
        self._progress_running = False
        self._prompt_length = 0

        print(f"🤖 AI服务初始化完成，使用模型: {self.model}")

    def check_service(self) -> tuple[bool, str]:
        """检查AI服务状态"""
        try:
            ollama.list()
            return True, f"✅ AI服务正常，使用模型: {self.model}"
        except Exception as e:
            return False, f"❌ AI服务异常: {str(e)}"

    def analyze(self, analysis_type: str, data: Dict[str, Any]) -> str:
        """执行AI分析"""
        try:
            safe_data = self._ensure_data_types(data)
            prompt = prompt_manager.format_prompt(analysis_type, **safe_data)

            if prompt.startswith("❌"):
                return prompt

            # 获取分析类型的中文名称
            analysis_name = self._get_analysis_name(analysis_type)
            result = self._call_model_with_progress(prompt, analysis_name)
            return result

        except Exception as e:
            return f"❌ AI分析失败: {str(e)}"

    def _get_analysis_name(self, analysis_type: str) -> str:
        """获取分析类型的中文名称"""
        name_mapping = {
            'trend_analysis': '趋势分析',
            'case_pattern_analysis': '案情特征分析',
            'comprehensive_analysis': '综合分析',
            'risk_assessment': '风险评估',
            'anomaly_detection': '异常检测'
        }
        return name_mapping.get(analysis_type, 'AI分析')

    def _call_model_with_progress(self, prompt: str, analysis_name: str = "AI分析") -> str:
        """调用AI模型 - 美观的多阶段进度显示"""
        import threading

        for attempt in range(self.max_retries + 1):
            try:
                # 开始计时
                start_time = time.time()
                self._progress_running = True
                self._current_stage = 0
                self._prompt_length = len(prompt)

                # 启动多阶段进度条线程
                progress_thread = threading.Thread(target=self._display_ai_progress, args=(start_time, analysis_name))
                progress_thread.daemon = True
                progress_thread.start()

                # 添加超时控制的AI调用
                response = None
                ai_call_timeout = 300  # 5分钟超时

                def ai_call():
                    nonlocal response
                    response = ollama.chat(
                        model=self.model,
                        messages=[
                            {
                                'role': 'system',
                                'content': '你是专业的反诈数据分析师，请基于数据分析提供专业、完整的分析结果。'
                            },
                            {
                                'role': 'user',
                                'content': prompt
                            }
                        ],
                        options={
                            'temperature': self.temperature,
                            'num_ctx': self.num_ctx
                        }
                    )

                # 启动AI调用线程
                ai_thread = threading.Thread(target=ai_call)
                ai_thread.daemon = True
                ai_thread.start()
                ai_thread.join(timeout=ai_call_timeout)

                # 检查是否超时
                if ai_thread.is_alive():
                    self._progress_running = False
                    raise TimeoutError(f"AI调用超时 ({ai_call_timeout}秒)")

                if response is None:
                    raise Exception("AI调用失败，未获得响应")

                # 停止进度条并等待线程完成
                self._progress_running = False
                progress_thread.join(timeout=2)  # 等待进度条线程完全结束

                # 处理响应...
                if hasattr(response, 'message') and hasattr(response.message, 'content'):
                    return response.message.content
                elif isinstance(response, dict) and 'message' in response:
                    message = response['message']
                    if isinstance(message, dict) and 'content' in message:
                        return message['content']
                    elif hasattr(message, 'content'):
                        return message.content
                elif isinstance(response, str):
                    return response

                return str(response)

            except TimeoutError as e:
                self._progress_running = False
                print(f"⚠️ AI调用超时 (第{attempt + 1}次尝试): {e}")
                if attempt < self.max_retries:
                    print(f"⚠️ 正在重试... ({attempt + 1}/{self.max_retries})")
                    time.sleep(5)  # 超时后等待更长时间
                    continue
                else:
                    return f"❌ AI分析失败：调用超时，已重试{self.max_retries}次"
            except ConnectionError as e:
                self._progress_running = False
                print(f"⚠️ 网络连接错误 (第{attempt + 1}次尝试): {e}")
                if attempt < self.max_retries:
                    print(f"⚠️ 正在重试... ({attempt + 1}/{self.max_retries})")
                    time.sleep(3)
                    continue
                else:
                    return f"❌ AI分析失败：网络连接错误，已重试{self.max_retries}次"
            except Exception as e:
                self._progress_running = False
                print(f"⚠️ AI调用失败 (第{attempt + 1}次尝试): {e}")
                if attempt < self.max_retries:
                    print(f"⚠️ 正在重试... ({attempt + 1}/{self.max_retries})")
                    time.sleep(2)
                    continue
                else:
                    return f"❌ 分析失败 (尝试{attempt + 1}次): {str(e)}"

        return "❌ 分析失败：超过最大重试次数"


    def _ensure_data_types(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """确保数据类型正确"""
        safe_data = {}

        for key, value in data.items():
            if key in ['days', 'total_cases', 'current_cases', 'telecom_total', 'unicom_total', 'mobile_total',
                       'current_week_total', 'last_week_total', 'current_month_total', 'last_month_same_period_total']:
                # 整数字段
                safe_data[key] = int(value) if value is not None else 0
            elif key in ['daily_avg', 'telecom_ratio', 'unicom_ratio', 'mobile_ratio',
                         'recent_7_avg', 'vs_prev_7_days', 'week_change', 'month_change']:
                # 浮点数字段
                safe_data[key] = float(value) if value is not None else 0.0
            else:
                # 其他字段保持原样
                safe_data[key] = value

        return safe_data

    def _display_ai_progress(self, start_time, analysis_name="AI分析"):
        """显示AI处理进度条 - 新版多阶段进度条"""
        progress_bar = AIAnalysisProgressBar()
        progress_bar.start(self._prompt_length, analysis_name)

        stage_timings = [3, 2, 5]  # 各阶段预计时长
        last_stage = -1

        while self._progress_running:
            elapsed = time.time() - start_time

            # 根据时间推进阶段
            if elapsed < stage_timings[0]:
                # 第一阶段：提示词生成
                current_stage = 0
            elif elapsed < sum(stage_timings[:2]):
                # 第二阶段：准备数据
                current_stage = 1
            elif elapsed < sum(stage_timings):
                # 第三阶段：启动模型
                current_stage = 2
            else:
                # 第四阶段：AI分析处理
                current_stage = 3

            # 只在阶段变化时切换，避免重复切换
            if current_stage != last_stage and current_stage != progress_bar.current_stage:
                while progress_bar.current_stage < current_stage:
                    progress_bar.next_stage()
                last_stage = current_stage

            progress_bar.display_progress()
            time.sleep(0.5)  # 更新频率

        # 完成进度条
        progress_bar.complete()

    def get_available_analyses(self) -> Dict[str, str]:
        """获取可用的分析类型"""
        templates = prompt_manager.get_all_templates()
        return {key: template.name for key, template in templates.items()}

    def update_prompt_template(self, analysis_type: str, new_template: str) -> bool:
        """更新提示词模板"""
        return prompt_manager.update_template(analysis_type, new_template)

    def show_prompt_info(self):
        """显示提示词信息"""
        prompt_manager.show_template_info()