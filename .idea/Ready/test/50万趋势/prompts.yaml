# 提示词模板配置文件
prompts:
  # 趋势分析提示词
  trend_analysis:
    name: "趋势分析"
    description: "分析电信诈骗案件数量变化趋势"
    template: |
      作为反诈数据分析专家，请基于真实数据进行电信诈骗案件趋势分析。

      **严格要求：所有分析和结论必须严格基于提供的数据进行真实输出，严禁编纂、臆造数据或得出无数据支撑的结论。**

      **数据基础**：
      - 分析周期：{start_date} 至 {end_date} ({days}天)
      - 案件总量：{total_cases}起，日均{daily_avg:.1f}起
      - 最新单日：{current_cases}起
      - 电信用户案件：{telecom_total}起
      - 联通用户案件：{unicom_total}起
      - 移动用户案件：{mobile_total}起

      **近期趋势**：
      - 近7天案件：{recent_7_days}
      - 近7天日均：{recent_7_avg:.1f}起
      - 环比前7天：{vs_prev_7_days:+.1%}

      **周维度对比**：
      - 本周案件数：{current_week_total}起
      - 上周案件数：{last_week_total}起
      - 周环比变化：{week_change:+.1%}

      **月维度对比**：
      - 本月累计：{current_month_total}起
      - 上月同期：{last_month_same_period_total}起
      - 月度变化：{month_change:+.1%}

      **运营商分布**：
      - 电信：{telecom_total}起（{telecom_ratio:.1%}）
      - 联通：{unicom_total}起（{unicom_ratio:.1%}）
      - 移动：{mobile_total}起（{mobile_ratio:.1%}）

      **重要提醒**：请严格按照上述数据进行分析，移动用户案件数量为{mobile_total}起，占比{mobile_ratio:.1%}；电信用户案件数量为{telecom_total}起，占比{telecom_ratio:.1%}。请正确识别哪个运营商的案件数量和占比最高。

      **分析要求**：
      请从以下4个维度进行简要分析（每个维度2-3句话）：
      **1. 近30天三大运营商对比：**
      客观分析电信、移动、联通三大运营商的案件数量和占比情况，明确指出哪个运营商案件数量最多、占比最高
      **2. 近期电信案件趋势：**
      先分析近7天电信案件数据趋势（基于近7天{telecom_recent_7_total}起案件，对比前7天{telecom_prev_7_total}起的变化情况和日均变化），再分析周维度趋势（本周{telecom_current_week_total}起与上周{telecom_last_week_total}起的对比变化）
      **3. 月维度度电信趋势：**
      本月与上月同期案件数量变化趋势分析
      **4. 综合趋势判断：**
      电信vs其他运营商等角度分析整体趋势规律，需要关注的趋势变化等等
      **输出要求**：200-300字，分析简洁专业，必须基于真实数据，突出突出关键趋势和关键结论，无需建议和免责声明。

  # 案情特征分析提示词
  case_pattern_analysis:
    name: "案情特征分析"
    description: "分析不同运营商用户的案情特征差异"
    template: |
      作为中国电信反诈策略专家，请基于真实案情数据进行运营商交叉案情对比分析。

      **严格要求：所有分析和结论必须严格基于提供的数据进行真实输出，严禁编纂、臆造数据或得出无数据支撑的结论，以电信案件为主，同时分析移动和联通的案件情况。**

      **核心数据输入**：
      - 分析周期：{last_week_start_date} 至 {last_week_end_date}（上周7天）
      - 筛选条件：金额大于50万元
      - 总案件数：{last_week_total_cases}起，日均{last_week_daily_avg:.1f}起
      - 案件样本详情：{case_samples}
      - 电信案件数：{last_week_telecom_total}起（{last_week_telecom_ratio:.1%}）
      - 联通案件数：{last_week_unicom_total}起（{last_week_unicom_ratio:.1%}）
      - 移动案件数：{last_week_mobile_total}起（{last_week_mobile_ratio:.1%}）

      **分析要点**：
      1. 各运营商案情特征：深入分析电信、联通、移动三类运营商用户所涉诈骗案件在诈骗手法、受害人画像、作案时间、涉案金额分布及技术手段上的各自主要特点
      2. 各运营商案情共性：深入分析电信、联通、移动三类运营商用户所涉诈骗案件在案情序列、诈骗流程、涉案APP有什么共性，挖掘可以形成的特点和预警
      3. 跨运营商预警机制：重点对比分析发生在联通、移动用户身上，但目前在电信用户中尚未大量出现或刚有苗头的新型诈骗手法

      **输出要求**：400-600字，必须基于真实案情数据，严禁编造内容，突出运营商间差异对比和预警价值。

  # 综合分析提示词
  comprehensive_analysis:
    name: "综合分析"
    description: "多维度综合分析当前诈骗态势"
    template: |
      作为资深业务数据分析师 & 反诈策略专家，请基于多维度数据对当前电信诈骗案件趋势进行综合分析。

      **严格要求：所有分析和结论必须严格基于提供的数据进行真实输出，严禁编纂、臆造数据或得出无数据支撑的结论。**

      **数据基础**：
      - 分析周期：{start_date} 至 {end_date}
      - 案件总量：{total_cases}起
      - 日均水平：{daily_avg:.1f}起
      - 最新单日：{current_cases}起

      **趋势数据**：
      - 近7天序列：{recent_7_days}
      - 近7天均值：{recent_7_avg:.1f}起
      - 对比前7天：{vs_prev_7_days:+.1%}
      - 本周vs上周：{current_vs_last_week:+.1%}
      - 本月vs上月同期：{current_vs_last_month:+.1%}

      **运营商分布**：
      - 电信：{telecom_total}起（{telecom_ratio:.1%}）
      - 联通：{unicom_total}起（{unicom_ratio:.1%}）
      - 移动：{mobile_total}起（{mobile_ratio:.1%}）

      **分析框架**：
      1. 案件趋势量化分析与预警：
         - 近7天的趋势与波动：结合近7天的数量序列分析近期案件总量变化、日均水平及增长/下降趋势
         - 周维度的数据趋势与波动：取自然周的数据序列分析近期案件总量的变化、日均水平及增长/下降趋势，对比上一个自然周同期数量，分析得出变化趋势
         - 月维度的数据趋势与波动：取自然月的数据序列分析近期案件总量的变化、日均水平及增长/下降趋势，对比上一个自然月同期数量，分析得出变化趋势
         - 环比与同比洞察：详细解读近7天与前7天、本周与上周同期、本月与上月同期的案件量变化率
         - 运营商分布特征：基于近3个月数据分析电信、联通、移动案件数量变化趋势

      2. 运营商交叉案情对比与提前布防：
         - 各运营商案情特征：深入分析电信、联通、移动三类运营商用户所涉诈骗案件的各自主要特点，突出典型性、差异性
         - 跨运营商预警机制：重点对比分析发生在联通、移动用户身上，但目前在电信用户中尚未大量出现或刚有苗头的新型诈骗手法、技术工具或受害群体特征
         - 应对建议：针对潜在风险，提出具体的业务应对建议，实现提前发现、提前防范

      **输出要求**：300-650字，严格根据提供的数据进行分析，严禁编纂或臆造任何数据和结论。结构清晰，逻辑严密，具有较强的决策支持价值。

# 提示词变量配置
prompt_variables:
  # 数据变量
  data_vars:
    - start_date
    - end_date
    - total_cases
    - daily_avg
    - telecom_total
    - unicom_total
    - mobile_total
    - telecom_ratio
    - unicom_ratio
    - mobile_ratio
    - current_cases
    - recent_7_days
    - recent_7_avg
    - vs_prev_7_days
    - current_week_total
    - last_week_total
    - week_change
    - current_month_total
    - last_month_same_period_total
    - last_month_same_period
    - month_change
    - current_vs_last_week
    - current_vs_last_month
    - change_rate_vs_last_month
    - case_samples
    - operator_distribution_table
    - three_month_operator_table
    # 电信专项统计变量
    - telecom_recent_7_total
    - telecom_recent_7_avg
    - telecom_prev_7_total
    - telecom_prev_7_avg
    - telecom_7days_change
    - telecom_7days_avg_change
    - telecom_current_week_total
    - telecom_last_week_total
    - telecom_week_change
    - telecom_current_month_total
    - telecom_last_month_total
    - telecom_month_change

  # 格式化规则
  formatting_rules:
    percentage_vars:
      - telecom_ratio
      - unicom_ratio
      - mobile_ratio
      - vs_prev_7_days
      - week_change
      - month_change
      - current_vs_last_week
      - current_vs_last_month
      - telecom_7days_change
      - telecom_week_change
      - telecom_month_change

    number_vars:
      - total_cases
      - telecom_total
      - unicom_total
      - mobile_total
      - current_cases
      - current_week_total
      - last_week_total
      - current_month_total
      - last_month_same_period_total
      - telecom_recent_7_total
      - telecom_prev_7_total
      - telecom_current_week_total
      - telecom_last_week_total
      - telecom_current_month_total
      - telecom_last_month_total

    float_vars:
      - daily_avg
      - recent_7_avg
      - telecom_recent_7_avg
      - telecom_prev_7_avg
      - telecom_7days_avg_change

# 提示词模板管理配置
template_management:
  backup_enabled: true
  backup_dir: "../backups/prompts"
  version_control: true
  max_versions: 10

  # 模板验证规则
  validation:
    min_length: 100
    max_length: 5000
    required_variables:
      trend_analysis:
        - start_date
        - end_date
        - total_cases
        - daily_avg
      case_pattern_analysis:
        - last_week_start_date
        - last_week_end_date
        - last_week_total_cases
        - last_week_daily_avg
        - case_samples
        - last_week_telecom_total
        - last_week_unicom_total
        - last_week_mobile_total
        - last_week_telecom_ratio
        - last_week_unicom_ratio
        - last_week_mobile_ratio
      comprehensive_analysis:
        - start_date
        - end_date
        - total_cases
        - case_samples
        - three_month_operator_table