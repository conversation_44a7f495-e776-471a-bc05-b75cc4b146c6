"""
用户界面服务
负责处理用户交互和菜单显示
"""
from typing import List, Dict, Any
from config_manager import config


class UIService:
    """用户界面服务类"""

    def __init__(self):
        self.system_config = config.get('system', {})


    def display_main_menu(self):
        """显示主菜单"""
        system_name = self.system_config.get('name', '监控系统')
        version = self.system_config.get('version', '1.0')

        print("\n" + "=" * 60)
        print(f"🚀 {system_name} v{version}")
        print("=" * 60)
        print("📊 【1】日维度图表分析")
        print("📈 【2】周维度图表分析")
        print("🤖 【3】AI智能分析")
        print("📋 【4】统计数据概览")
        print("🗑️ 【5】清除缓存")
        print("⚙️ 【6】系统设置")
        print("🙈 【0】退出系统")
        print("=" * 60)

    def display_analysis_menu(self, ai_service):
        """显示AI分析菜单 - 简化版本"""
        print("\n" + "=" * 50)
        print("🤖 AI智能分析")
        print("=" * 50)

        # 获取可用的分析类型
        available_analyses = ai_service.get_available_analyses()

        # 简化的描述映射
        descriptions = {
            'trend_analysis': '分析案件数量变化趋势',
            'case_pattern_analysis': '分析案件特征模式',
            'comprehensive_analysis': '全面综合分析'
        }

        # 显示选项
        for i, (key, name) in enumerate(available_analyses.items(), 1):
            desc = descriptions.get(key, '')
            print(f"【{i}】{name} - {desc}")

        print("【0】返回主菜单")
        print("=" * 50)

    def get_analysis_selection(self, ai_service) -> List[str]:
        """获取用户选择的分析选项"""
        selections = []
        available_analyses = ai_service.get_available_analyses()
        option_keys = list(available_analyses.keys())

        while True:
            try:
                choice = input("\n请选择分析类型 (多选用逗号分隔，如: 1,2,3): ").strip()

                if choice == '0':
                    break

                if not choice:
                    print("❌ 请输入有效选项")
                    continue

                # 解析选择
                choices = [c.strip() for c in choice.split(',')]

                for c in choices:
                    try:
                        index = int(c) - 1
                        if 0 <= index < len(option_keys):
                            key = option_keys[index]
                            if key not in selections:
                                selections.append(key)
                        else:
                            print(f"❌ 无效选项: {c}")
                    except ValueError:
                        print(f"❌ 无效输入: {c}")

                if selections:
                    # 显示选择的分析类型
                    selected_names = [available_analyses[key] for key in selections]
                    print(f"✅ 已选择: {', '.join(selected_names)}")
                    break
                else:
                    print("❌ 请至少选择一个分析类型")

            except KeyboardInterrupt:
                print("\n❌ 用户取消选择")
                break
            except Exception as e:
                print(f"❌ 输入处理异常: {e}")

        return selections

    def display_statistics(self, stats: Dict[str, Any]):
        """显示统计数据 - 修复版本"""
        print("\n" + "=" * 60)
        print("📊 统计数据概览")
        print("=" * 60)

        # 1. 统计时间周期、统计周期案件数量、日均案件数
        print(f"📅 统计周期：{stats.get('start_date', 'N/A')} 至 {stats.get('end_date', 'N/A')}")
        print(f"📈 案件总数：{stats.get('total_cases', 0):,}起（包含电信、联通、移动）")
        print(f"📊 日均案件：{stats.get('daily_avg', 0):.1f}起")

        # 2. 运营商分布（统计周期内三大运营商分别的案件数）
        print(f"\n📱 运营商分布：")
        operators = config.get_operators()
        for operator_key, operator_config in operators.items():
            total_key = f"{operator_key}_total"
            ratio_key = f"{operator_key}_ratio"
            total = stats.get(total_key, 0)
            ratio = stats.get(ratio_key, 0)
            print(f"   {operator_config.name}：{total:,}起 ({ratio:.1%})")

        # 3. 电信近7天统计
        print(f"\n📈 电信近7天统计：")
        print(f"   电信近7天案件数：{stats.get('telecom_recent_7_total', 0):,}起")
        print(f"   电信上一个7天案件数：{stats.get('telecom_prev_7_total', 0):,}起")
        print(f"   电信近7天对比上一个7天变化率：{stats.get('telecom_7days_change', 0):+.1%}")
        print(f"   电信近7天日均案件数：{stats.get('telecom_recent_7_avg', 0):.1f}起")
        print(f"   电信上一个7天日均案件数：{stats.get('telecom_prev_7_avg', 0):.1f}起")
        print(f"   电信近7天日均对比上一个7天日均变化率：{stats.get('telecom_7days_avg_change', 0):+.1%}")

        # 4. 电信自然周统计
        print(f"\n📅 电信自然周统计：")
        print(f"   电信本周案件数：{stats.get('telecom_current_week_total', 0):,}起")
        print(f"   电信上周案件数：{stats.get('telecom_last_week_total', 0):,}起")
        print(f"   电信本周对比上周变化率：{stats.get('telecom_week_change', 0):+.1%}")
        if stats.get('current_week_start') and stats.get('current_week_end'):
            print(f"   本周时间范围：{stats.get('current_week_start')} 至 {stats.get('current_week_end')}")
        if stats.get('last_week_start') and stats.get('last_week_end'):
            print(f"   上周时间范围：{stats.get('last_week_start')} 至 {stats.get('last_week_end')}")

        # 5. 电信自然月统计
        print(f"\n📅 电信自然月统计：")
        print(f"   电信本月案件数：{stats.get('telecom_current_month_total', 0):,}起")
        print(f"   电信上月同期案件数：{stats.get('telecom_last_month_total', 0):,}起")
        print(f"   电信本月对比上月同期变化率：{stats.get('telecom_month_change', 0):+.1%}")
        if stats.get('current_month_start') and stats.get('current_month_end'):
            print(f"   本月时间范围：{stats.get('current_month_start')} 至 {stats.get('current_month_end')}")
        if stats.get('last_month_start') and stats.get('last_month_end'):
            print(f"   上月同期时间范围：{stats.get('last_month_start')} 至 {stats.get('last_month_end')}")

    def display_analysis_data_overview(self, stats: Dict[str, Any]):
        """显示AI分析前的核心数据概况"""
        print("\n## 核心数据概况")
        print(f"- 分析周期：{stats.get('start_date', 'N/A')} 至 {stats.get('end_date', 'N/A')} ({stats.get('days', 0)}天)")
        print(f"- 案件总量：{stats.get('total_cases', 0)}起")
        print(f"- 日均案件：{stats.get('daily_avg', 0):.1f}起")
        print(f"- 最新单日：{stats.get('current_cases', 0)}起")
        print(f"- 电信案件：{stats.get('telecom_total', 0)}起（{stats.get('telecom_ratio', 0):.1%}）")
        print(f"- 联通案件：{stats.get('unicom_total', 0)}起（{stats.get('unicom_ratio', 0):.1%}）")
        print(f"- 移动案件：{stats.get('mobile_total', 0)}起（{stats.get('mobile_ratio', 0):.1%}）")

        print(f"\n**近期趋势**：")
        # 显示近7天总数，不显示序列
        recent_7_total = stats.get('telecom_recent_7_total', 0)
        print(f"- 近7天案件：{recent_7_total}起")
        print(f"- 近7天日均：{stats.get('recent_7_avg', 0):.1f}起")
        print(f"- 环比前7天：{stats.get('vs_prev_7_days', 0):+.1%}")

        print(f"\n**周维度对比**：")
        print(f"- 本周案件数：{stats.get('current_week_total', 0)}起")
        print(f"- 上周案件数：{stats.get('last_week_total', 0)}起")
        print(f"- 周环比变化：{stats.get('week_change', 0):+.1%}")

        print(f"\n**月维度对比**：")
        print(f"- 本月累计：{stats.get('current_month_total', 0)}起")
        print(f"- 上月同期：{stats.get('last_month_same_period_total', 0)}起")
        print(f"- 月度变化：{stats.get('month_change', 0):+.1%}")

        print("-" * 60)

    def display_settings_menu(self):
        """显示设置菜单"""
        print("\n" + "=" * 50)
        print("⚙️ 系统设置")
        print("=" * 50)
        print("【1】查看当前配置")
        print("【2】查看缓存状态")
        print("【3】查看AI服务状态")
        print("【4】查看提示词模板")
        print("【5】编辑提示词模板")
        print("【0】返回主菜单")
        print("=" * 50)

    def display_current_config(self):
        """显示当前配置"""
        print("\n" + "=" * 50)
        print("📋 当前系统配置")
        print("=" * 50)

        # 系统信息
        system_config = config.get('system', {})
        print(f"系统名称: {system_config.get('name', 'N/A')}")
        print(f"系统版本: {system_config.get('version', 'N/A')}")

        # 数据库配置
        db_config = config.get_database_config()
        print(f"数据库: {db_config.host}:{db_config.port}/{db_config.database}")

        # AI配置
        ai_config = config.get_ai_config()
        print(f"AI模型: {ai_config.get('model', 'N/A')}")
        print(f"超时时间: {ai_config.get('timeout', 'N/A')}秒")

        # 缓存配置
        cache_config = config.get_cache_config()
        print(f"缓存状态: {'启用' if cache_config.get('enabled', False) else '禁用'}")

    def display_ai_service_status(self, ai_service):
        """显示AI服务状态"""
        print("\n" + "=" * 50)
        print("🤖 AI服务状态")
        print("=" * 50)

        is_available, message = ai_service.check_service()
        print(message)

        if is_available:
            available_analyses = ai_service.get_available_analyses()
            print(f"\n可用分析类型: {len(available_analyses)}个")
            for key, name in available_analyses.items():
                print(f"  - {name} ({key})")

    def show_prompt_templates(self, ai_service):
        """显示提示词模板"""
        ai_service.show_prompt_info()

    def edit_prompt_template(self, ai_service):
        """编辑提示词模板"""
        available_analyses = ai_service.get_available_analyses()

        print("\n可编辑的提示词模板:")
        for i, (_, name) in enumerate(available_analyses.items(), 1):
            print(f"【{i}】{name}")

        try:
            choice = input("\n请选择要编辑的模板 (输入序号): ").strip()
            index = int(choice) - 1

            if 0 <= index < len(available_analyses):
                template_key = list(available_analyses.keys())[index]
                template_name = available_analyses[template_key]

                print(f"\n正在编辑: {template_name}")
                print("请输入新的提示词模板 (输入 'END' 结束):")

                lines = []
                while True:
                    line = input()
                    if line.strip() == 'END':
                        break
                    lines.append(line)

                new_template = '\n'.join(lines)
                if new_template.strip():
                    success = ai_service.update_prompt_template(template_key, new_template)
                    if success:
                        print(f"✅ 提示词模板 '{template_name}' 更新成功")
                    else:
                        print(f"❌ 提示词模板 '{template_name}' 更新失败")
                else:
                    print("❌ 提示词不能为空")
            else:
                print("❌ 无效选择")

        except ValueError:
            print("❌ 请输入有效的数字")
        except Exception as e:
            print(f"❌ 编辑失败: {e}")


