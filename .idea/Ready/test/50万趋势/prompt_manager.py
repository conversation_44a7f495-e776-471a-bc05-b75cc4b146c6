"""
提示词管理器
负责加载、管理和格式化提示词模板
"""
import yaml
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Optional
from dataclasses import dataclass


@dataclass
class PromptTemplate:
    name: str
    description: str
    template: str
    required_vars: List[str]


class PromptManager:
    """提示词管理器"""

    def __init__(self, prompts_file: str = "prompts.yaml"):
        self.prompts_file = Path(prompts_file)
        self._prompts = {}
        self._variables_config = {}
        self._management_config = {}
        self.load_prompts()

    def load_prompts(self):
        """加载提示词配置"""
        try:
            if not self.prompts_file.exists():
                raise FileNotFoundError(f"提示词配置文件不存在: {self.prompts_file}")

            with open(self.prompts_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            # 加载提示词模板
            prompts_config = config.get('prompts', {})
            for key, prompt_data in prompts_config.items():
                self._prompts[key] = PromptTemplate(
                    name=prompt_data.get('name', key),
                    description=prompt_data.get('description', ''),
                    template=prompt_data.get('template', ''),
                    required_vars=self._extract_variables(prompt_data.get('template', ''))
                )

            # 加载变量配置
            self._variables_config = config.get('prompt_variables', {})

            # 加载管理配置
            self._management_config = config.get('template_management', {})

            print(f"✅ 提示词配置加载成功: {len(self._prompts)} 个模板")

        except Exception as e:
            print(f"❌ 提示词配置加载失败: {e}")
            raise

    def get_prompt_template(self, template_key: str) -> Optional[PromptTemplate]:
        """获取提示词模板"""
        return self._prompts.get(template_key)

    def get_all_templates(self) -> Dict[str, PromptTemplate]:
        """获取所有提示词模板"""
        return self._prompts.copy()

    def format_prompt(self, template_key: str, **kwargs) -> str:
        """格式化提示词"""
        template = self.get_prompt_template(template_key)
        if not template:
            return f"❌ 提示词模板不存在: {template_key}"

        try:
            # 格式化数据
            formatted_kwargs = self._format_variables(kwargs)

            # 验证必需变量
            missing_vars = self._check_required_variables(template, formatted_kwargs)
            if missing_vars:
                return f"❌ 缺少必需变量: {', '.join(missing_vars)}"

            # 格式化模板
            formatted_prompt = template.template.format(**formatted_kwargs)

            return formatted_prompt

        except KeyError as e:
            return f"❌ 提示词模板缺少参数: {e}"
        except Exception as e:
            return f"❌ 提示词格式化失败: {e}"

    def _extract_variables(self, template: str) -> List[str]:
        """从模板中提取变量名"""
        import re
        pattern = r'\{([^}]+)\}'
        variables = re.findall(pattern, template)
        # 去除格式化规则，只保留变量名
        clean_vars = []
        for var in variables:
            if ':' in var:
                var = var.split(':')[0]
            clean_vars.append(var)
        return list(set(clean_vars))

    def _format_variables(self, kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """格式化变量值"""
        formatted = kwargs.copy()

        formatting_rules = self._variables_config.get('formatting_rules', {})

        # 格式化百分比 - 确保数据类型正确
        percentage_vars = formatting_rules.get('percentage_vars', [])
        for var in percentage_vars:
            if var in formatted:
                value = formatted[var]
                try:
                    # 确保是数字类型再进行百分比格式化
                    if isinstance(value, (int, float)):
                        formatted[var] = value  # 保持数字类型，让模板自己格式化
                    elif isinstance(value, str):
                        # 尝试转换字符串为数字
                        formatted[var] = float(value)
                    else:
                        formatted[var] = 0.0
                except (ValueError, TypeError):
                    formatted[var] = 0.0

        # 格式化数字（添加千分位分隔符）
        number_vars = formatting_rules.get('number_vars', [])
        for var in number_vars:
            if var in formatted:
                value = formatted[var]
                try:
                    if isinstance(value, (int, float)):
                        formatted[var] = int(value)  # 确保是整数类型
                    elif isinstance(value, str):
                        formatted[var] = int(float(value))
                    else:
                        formatted[var] = 0
                except (ValueError, TypeError):
                    formatted[var] = 0

        # 格式化浮点数
        float_vars = formatting_rules.get('float_vars', [])
        for var in float_vars:
            if var in formatted:
                value = formatted[var]
                try:
                    if isinstance(value, (int, float)):
                        formatted[var] = float(value)
                    elif isinstance(value, str):
                        formatted[var] = float(value)
                    else:
                        formatted[var] = 0.0
                except (ValueError, TypeError):
                    formatted[var] = 0.0

        return formatted

    def _check_required_variables(self, template: PromptTemplate, kwargs: Dict[str, Any]) -> List[str]:
        """检查必需变量"""
        validation_config = self._management_config.get('validation', {})
        required_vars = validation_config.get('required_variables', {}).get(template.name.lower().replace(' ', '_'), [])

        missing_vars = []
        for var in required_vars:
            if var not in kwargs:
                missing_vars.append(var)

        return missing_vars

    def update_template(self, template_key: str, new_template: str) -> bool:
        """更新提示词模板"""
        try:
            # 备份当前模板
            if self._management_config.get('backup_enabled', True):
                self._backup_template(template_key)

            # 验证新模板
            if not self._validate_template(new_template):
                return False

            # 更新模板
            if template_key in self._prompts:
                old_template = self._prompts[template_key]
                self._prompts[template_key] = PromptTemplate(
                    name=old_template.name,
                    description=old_template.description,
                    template=new_template,
                    required_vars=self._extract_variables(new_template)
                )
            else:
                self._prompts[template_key] = PromptTemplate(
                    name=template_key,
                    description="用户自定义模板",
                    template=new_template,
                    required_vars=self._extract_variables(new_template)
                )

            # 保存到文件
            self._save_templates()

            print(f"✅ 提示词模板 '{template_key}' 更新成功")
            return True

        except Exception as e:
            print(f"❌ 更新提示词模板失败: {e}")
            return False

    def _validate_template(self, template: str) -> bool:
        """验证模板格式"""
        validation = self._management_config.get('validation', {})

        min_length = validation.get('min_length', 100)
        max_length = validation.get('max_length', 5000)

        if len(template) < min_length:
            print(f"❌ 模板长度不足，最少需要 {min_length} 字符")
            return False

        if len(template) > max_length:
            print(f"❌ 模板长度超限，最多允许 {max_length} 字符")
            return False

        return True

    def _backup_template(self, template_key: str):
        """备份模板"""
        try:
            backup_dir = Path(self._management_config.get('backup_dir', '../backups/prompts'))
            backup_dir.mkdir(parents=True, exist_ok=True)

            if template_key in self._prompts:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_file = backup_dir / f"{template_key}_{timestamp}.txt"

                with open(backup_file, 'w', encoding='utf-8') as f:
                    f.write(self._prompts[template_key].template)

                print(f"📦 模板已备份: {backup_file}")
        except Exception as e:
            print(f"⚠️ 模板备份失败: {e}")

    def _save_templates(self):
        """保存模板到配置文件"""
        try:
            # 读取当前配置
            with open(self.prompts_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            # 更新提示词部分
            prompts_config = {}
            for key, template in self._prompts.items():
                prompts_config[key] = {
                    'name': template.name,
                    'description': template.description,
                    'template': template.template
                }

            config['prompts'] = prompts_config

            # 保存配置
            with open(self.prompts_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, ensure_ascii=False, indent=2, default_flow_style=False)

        except Exception as e:
            print(f"❌ 保存模板配置失败: {e}")

    def show_template_info(self):
        """显示模板信息"""
        print("\n" + "=" * 60)
        print("📝 提示词模板信息")
        print("=" * 60)

        for key, template in self._prompts.items():
            print(f"\n🔍 {template.name} ({key}):")
            print(f"   描述: {template.description}")
            print(f"   长度: {len(template.template)} 字符")
            print(f"   变量: {', '.join(template.required_vars)}")
            print(f"   预览: {template.template[:100]}...")


# 全局提示词管理器实例
prompt_manager = PromptManager()