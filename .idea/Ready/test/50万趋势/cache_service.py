"""
缓存服务
统一的缓存管理
"""
import json
import threading
import time
import sys
from pathlib import Path
from datetime import datetime, timedelta
from typing import Optional, Tuple, Dict, Any
import pandas as pd
from config_manager import config


class CacheService:
    """缓存服务类"""

    def __init__(self):
        cache_config = config.get_cache_config()
        paths_config = config.get_paths()

        self.enabled = cache_config.get('enabled', True)
        self.cache_dir = Path(paths_config.get('cache_dir', '../cache'))
        self.cache_dir.mkdir(exist_ok=True)

        self._memory_cache = {}
        self._cache_access_time = {}  # 记录缓存访问时间
        self._lock = threading.Lock()
        self.max_memory_items = 10  # 最大内存缓存项数
        self.cache_ttl_hours = 24   # 缓存生存时间（小时）

        print(f"📦 缓存服务初始化：{'启用' if self.enabled else '禁用'}")

        # 启动时清理过期缓存
        self._cleanup_expired_cache()

    def get(self, cache_type: str) -> Optional[Tuple[pd.DataFrame, Optional[pd.DataFrame]]]:
        """获取缓存数据"""
        if not self.enabled:
            return None

        # 检查内存缓存
        if cache_type in self._memory_cache:
            # 更新访问时间
            self._cache_access_time[cache_type] = datetime.now()
            print(f"📦 使用{cache_type}内存缓存")
            return self._memory_cache[cache_type]

        # 检查文件缓存
        cache_file = self.cache_dir / f"{cache_type}_cache.json"
        if cache_file.exists():
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)

                # 检查缓存是否过期
                cache_date = cache_data.get('date')
                today = datetime.now().strftime('%Y-%m-%d')

                if cache_date == today:
                    print(f"📦 使用{cache_type}文件缓存")

                    # 重建DataFrame
                    df = pd.DataFrame(cache_data['data'])
                    case_details = None

                    if cache_data.get('case_details'):
                        case_details = pd.DataFrame(cache_data['case_details'])

                    if not df.empty:
                        df['日期'] = pd.to_datetime(df['日期'])

                    # 存入内存缓存
                    result = (df, case_details)
                    with self._lock:
                        self._memory_cache[cache_type] = result

                    return result
                else:
                    print(f"⚠️ {cache_type}缓存已过期，删除旧缓存")
                    cache_file.unlink()

            except Exception as e:
                print(f"⚠️ {cache_type}缓存读取失败: {e}")

        return None

    def set(self, cache_type: str, df: pd.DataFrame, case_details: Optional[pd.DataFrame] = None):
        """设置缓存数据"""
        if not self.enabled:
            return

        try:
            with self._lock:
                # 检查内存缓存大小，如果超过限制则清理最旧的
                if len(self._memory_cache) >= self.max_memory_items:
                    self._cleanup_old_memory_cache()

                # 内存缓存
                self._memory_cache[cache_type] = (df, case_details)
                self._cache_access_time[cache_type] = datetime.now()

                # 文件缓存
                cache_data = {
                    'date': datetime.now().strftime('%Y-%m-%d'),
                    'cache_type': cache_type,
                    'data': df.to_dict('records'),
                    'case_details': case_details.to_dict('records') if case_details is not None else []
                }

                cache_file = self.cache_dir / f"{cache_type}_cache.json"
                with open(cache_file, 'w', encoding='utf-8') as f:
                    json.dump(cache_data, f, ensure_ascii=False, indent=2, default=str)

                print(f"💾 {cache_type}数据已缓存")

        except Exception as e:
            print(f"⚠️ {cache_type}缓存保存失败: {e}")

    def clear(self):
        """清除所有缓存"""
        try:
            with self._lock:
                self._memory_cache.clear()

            # 清除文件缓存
            cache_files = list(self.cache_dir.glob("*_cache.json"))
            for cache_file in cache_files:
                cache_file.unlink()

            print(f"✅ 已清除 {len(cache_files)} 个缓存文件")

        except Exception as e:
            print(f"❌ 清除缓存失败: {e}")

    def get_cache_info(self):
        """获取缓存信息"""
        memory_count = len(self._memory_cache)
        file_count = len(list(self.cache_dir.glob("*_cache.json")))

        print(f"📊 缓存状态：内存缓存 {memory_count} 项，文件缓存 {file_count} 项")
        return {
            'memory_count': memory_count,
            'file_count': file_count,
            'enabled': self.enabled
        }

    def _cleanup_old_memory_cache(self):
        """清理最旧的内存缓存项"""
        if not self._cache_access_time:
            return

        # 找到最旧的缓存项
        oldest_key = min(self._cache_access_time.keys(),
                        key=lambda k: self._cache_access_time[k])

        # 删除最旧的缓存项
        if oldest_key in self._memory_cache:
            del self._memory_cache[oldest_key]
            del self._cache_access_time[oldest_key]
            print(f"🗑️ 清理旧内存缓存: {oldest_key}")

    def _cleanup_expired_cache(self):
        """清理过期的文件缓存"""
        try:
            current_time = datetime.now()
            for cache_file in self.cache_dir.glob("*_cache.json"):
                try:
                    # 检查文件修改时间
                    file_time = datetime.fromtimestamp(cache_file.stat().st_mtime)
                    if current_time - file_time > timedelta(hours=self.cache_ttl_hours):
                        cache_file.unlink()
                        print(f"🗑️ 清理过期缓存文件: {cache_file.name}")
                except Exception as e:
                    print(f"⚠️ 清理缓存文件失败 {cache_file.name}: {e}")
        except Exception as e:
            print(f"⚠️ 缓存清理过程失败: {e}")


# 全局缓存服务实例
cache_service = CacheService()