"""
配置管理器
负责加载和管理所有配置项
"""
import yaml
import os
from pathlib import Path
from dataclasses import dataclass
from typing import Dict, Any, Optional


@dataclass
class DatabaseConfig:
    host: str
    port: int
    user: str
    password: str
    database: str
    charset: str
    timeout: int


@dataclass
class OperatorConfig:
    name: str
    color: str
    sql_pattern: str
    risk_threshold: float
    high_risk: int


class ConfigManager:
    """配置管理器"""

    def __init__(self, config_file: str = "config.yaml"):
        self.config_file = Path(config_file)
        self._config = {}
        self.load_config()

    def load_config(self):
        """加载配置文件"""
        try:
            if not self.config_file.exists():
                raise FileNotFoundError(f"配置文件不存在: {self.config_file}")

            with open(self.config_file, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f)

            print(f"✅ 配置文件加载成功: {self.config_file}")
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            raise

    def get(self, key: str, default=None):
        """获取配置项"""
        keys = key.split('.')
        value = self._config

        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default

        return value

    def get_database_config(self) -> DatabaseConfig:
        """获取数据库配置"""
        db_config = self.get('database', {})
        return DatabaseConfig(**db_config)

    def get_operators(self) -> Dict[str, OperatorConfig]:
        """获取运营商配置"""
        operators = {}
        operators_config = self.get('operators', {})

        for key, config in operators_config.items():
            operators[key] = OperatorConfig(**config)

        return operators

    def get_sql_template(self, template_name: str) -> str:
        """获取SQL模板"""
        return self.get(f'sql_templates.{template_name}', '')

    def get_data_query_config(self, query_type: str) -> Dict[str, Any]:
        """获取数据查询配置"""
        return self.get(f'data_query.{query_type}', {})

    def get_chart_config(self, chart_type: str) -> Dict[str, Any]:
        """获取图表配置"""
        return self.get(f'charts.{chart_type}', {})

    def get_ai_config(self) -> Dict[str, Any]:
        """获取AI配置"""
        return self.get('ai_analysis', {})

    def get_cache_config(self) -> Dict[str, Any]:
        """获取缓存配置"""
        return self.get('cache', {})

    def get_paths(self) -> Dict[str, str]:
        """获取路径配置"""
        return self.get('paths', {})

    def get_colors(self) -> Dict[str, str]:
        """获取颜色配置"""
        return self.get('colors', {})

    def get_business_rules(self) -> Dict[str, Any]:
        """获取业务规则"""
        return self.get('business_rules', {})


# 全局配置实例
config = ConfigManager()