from util import engine,text,file_path,operator_key,date_range,word_name,excel_statistics_name,date_query,intensive_monitoring
from word_report_utils import WordReportUtils
from excel_statistics import set_excel_data,group_excel_num,get_phone_nums_day,query_sql
from re_106_95_96 import get_106_df,get_106_df_month
from app_statistics import app_name_statistics
from datetime import datetime
import re

def get_data():
    print("文档生成开始")
    # 生成的word的文档存放地址
    word_file = file_path + '\\' + word_name
    # excel_file = file_path + '\\' + excel_statistics_name
    # # 获取运营商占比
    # excel_data = set_excel_data(excel_file)
    # datas = group_excel_num(excel_data,operator_key,len(excel_data))
    # #print(datas)
    # data_amount = {}
    # for das in datas:
    #     data_amount[das[operator_key]] = das
    #print(data_amount)
    # 获取运营商为电信的所有号码
    phone_list = get_phone_nums_day(date_query)
    # print(phone_list)
    dx_phone = phone_list.get("电信"," ")
    # print(phone)
    lt_phone = phone_list.get("联通"," ")
    yd_phone = phone_list.get("移动"," ")
    gd_phone = phone_list.get("广电"," ")
    xs_phone = phone_list.get("虚商"," ")
    # print(phone)

    #替换中间监测数据中间的，替换成|
    int_mon = intensive_monitoring.replace(",", "|")

    queries = [
        # 0
        text(
            " select count(*) as case_count,ROUND(SUM(involved_amount)/100000000,2) as total_amout from anti_fraud_case_new where insert_day = '"+date_query+"'"),
        # 查询总数量
        # 1
        text(
            " SELECT COUNT(*) as total_count, ROUND(COUNT(*)/ (select count(1) from anti_fraud_case_new where insert_day = '"+date_query+"') *100,2) as percentage FROM anti_fraud_case_new  where url!='无' and  insert_day = '"+date_query+"'"),
        # url数据
        # 2
        text(
            " select COUNT(*) as total_count, ROUND(COUNT(*)/  (select count(1) from anti_fraud_case_new where insert_day = '"+date_query+"') *100,2) as percentage from anti_fraud_case_new where app_name!='无' and app_name!='' and  insert_day  = '"+date_query+"'"),
        # app数据
        # 3
        text(
            " SELECT  COUNT(*) as total_count,ROUND(COUNT(*)/  (select count(1) from anti_fraud_case_new where insert_day = '"+date_query+"') *100,2) as percentage  FROM anti_fraud_case_new  where  insert_day = '"+date_query+"' and (connection_relationship!='无' or suspect_phone_number !='')"),
        # sms数据
        # 4
        text(
            " select count(*) as tel_count from anti_fraud_case_new where suspect_phone_number!='' and  suspect_phone_number REGEXP '^(00|\\\+)' AND suspect_phone_number NOT REGEXP '^(0086|\\\+86)' and  insert_day  = '"+date_query+"' "),
        # 国际号码涉案
        # 5
        text(
            "SELECT @row_number:=@row_number + 1 AS id,sorted.* FROM (SELECT  case_main_type as type,COUNT(*) as total_count,ROUND(SUM(involved_amount)/10000,2) as amount, ROUND(COUNT(*)/ ( SELECT COUNT(*) as total_count  FROM anti_fraud_case_new where insert_day = '"+date_query+"') *100,2) as percentage FROM anti_fraud_case_new where    insert_day  = '"+date_query+"' GROUP BY case_main_type ORDER BY total_count DESC ) AS sorted,(SELECT @row_number:=0) AS init "),
        # 涉案量类型排行
        # 6
        text(
            "SELECT @row_number:=@row_number + 1 AS id,  sorted.* FROM (   SELECT   app_name, COUNT(*) as total_count, ROUND(COUNT(*)/(select COUNT(*) as total_count from anti_fraud_case_new where insert_day = '"+date_query+"' and app_name not in ('','-','无','韩文','符号','未命名','日文','不详','？？') ) *100,2) as percentage FROM anti_fraud_case_new  where app_name not in ('','-','无','韩文','符号','未命名','日文','不详','？？') and  insert_day  = '"+date_query+"' GROUP BY app_name ORDER BY total_count DESC limit 20  ) AS sorted,(SELECT @row_number:=0) AS init"),
        # APP排行


        #其中中国电信号码涉案金额1.83万，50万以上的案件0个,100万以上的案件0个,1000万以上的案件0个。 判断金额
        # 7
        text(
            "select count(*) as total_count,sum(involved_amount) from anti_fraud_case_new where involved_amount>=500000 and involved_amount<1000000 and  insert_day  = '"+date_query+"' and suspect_phone_info like '%电信%' "),
        # 8
        text(
            "select count(*) as total_count,sum(involved_amount) from anti_fraud_case_new where involved_amount>=1000000 and involved_amount<10000000 and  insert_day  = '"+date_query+"' and suspect_phone_info like '%电信%'"),
        # 9
        text(
            "select count(*) as total_count,sum(involved_amount) from anti_fraud_case_new where involved_amount>=10000000   and  insert_day  = '"+date_query+"' and suspect_phone_info like '%电信%'"),

        # 联通
        # 10
        text(
            "select count(*) as total_count,sum(involved_amount) from anti_fraud_case_new where involved_amount>=500000 and involved_amount<1000000 and  insert_day  = '"+date_query+"' and suspect_phone_info like '%联通%'"),
        # 11
        text(
            "select count(*) as total_count,sum(involved_amount) from anti_fraud_case_new where involved_amount>=1000000 and involved_amount<10000000 and  insert_day  = '"+date_query+"' and suspect_phone_info like '%联通%'"),
        # 12
        text(
            "select count(*) as total_count,sum(involved_amount) from anti_fraud_case_new where involved_amount>=10000000 and  insert_day  = '"+date_query+"' and suspect_phone_info like '%联通%'"),
        # 移动
        # 13
        text(
            "select count(*) as total_count,sum(involved_amount) from anti_fraud_case_new where involved_amount>=500000 and involved_amount<1000000 and  insert_day  = '"+date_query+"' and suspect_phone_info like '%移动%'"),
        # 14
        text(
            "select count(*) as total_count,sum(involved_amount) from anti_fraud_case_new where involved_amount>=1000000 and involved_amount<10000000 and  insert_day  = '"+date_query+"' and suspect_phone_info like '%移动%'"),
        # 15
        text(
            "select count(*) as total_count,sum(involved_amount) from anti_fraud_case_new where involved_amount>=10000000 and  insert_day  = '"+date_query+"' and suspect_phone_info like '%移动%'"),

        # 广电
        # 16
        text(
            "select count(*) as total_count,sum(involved_amount) from anti_fraud_case_new where involved_amount>=500000 and involved_amount<1000000 and  insert_day  = '"+date_query+"' and suspect_phone_info like '%广电%'"),
        # 17
        text(
            "select count(*) as total_count,sum(involved_amount) from anti_fraud_case_new where involved_amount>=1000000 and involved_amount<10000000 and  insert_day  = '"+date_query+"' and suspect_phone_info like '%广电%'"),
        # 18
        text(
            "select count(*) as total_count,sum(involved_amount) from anti_fraud_case_new where involved_amount>=10000000 and  insert_day  = '"+date_query+"' and suspect_phone_info like '%广电%'"),

        # 虚商
        # 19
        text(
            "select count(*) as total_count,sum(involved_amount) from anti_fraud_case_new where involved_amount>=500000 and involved_amount<1000000 and  insert_day  = '"+date_query+"' and suspect_phone_info like '%虚商%'"),
        # 20
        text(
            "select count(*) as total_count,sum(involved_amount) from anti_fraud_case_new where involved_amount>=1000000 and involved_amount<10000000 and  insert_day  = '"+date_query+"' and suspect_phone_info like '%虚商%'"),
        # 21
        text(
            "select count(*) as total_count,sum(involved_amount) from anti_fraud_case_new where involved_amount>=10000000 and  insert_day  = '"+date_query+"' and suspect_phone_info like '%虚商%'"),
        # 22
        text(
            "select count(1) as total_count , IFNULL(ROUND(sum( involved_amount ) /10000,2),0) as total_money from anti_fraud_case_new where  suspect_phone_info like '%电信%' and  insert_day  = '"+date_query+"'"),
        # 23
        text(
            "select count(1) as total_count, IFNULL(ROUND(sum( involved_amount ) /10000,2),0) as total_money from anti_fraud_case_new where  suspect_phone_info like '%联通%' and  insert_day  = '"+date_query+"'"),
        # 24
        text(
            "select count(1) as total_count, IFNULL(ROUND(sum( involved_amount ) /10000,2),0) as total_money from anti_fraud_case_new where   suspect_phone_info like '%移动%' and  insert_day  = '"+date_query+"' "),
        # 25
        text(
            "select count(1) as total_count, IFNULL(ROUND(sum( involved_amount ) /10000,2),0) as total_money from anti_fraud_case_new where  suspect_phone_info like '%广电%' and  insert_day  = '"+date_query+"'"),
        # 26
        text(
            "select count(1) as total_count, IFNULL(ROUND(sum( involved_amount ) /10000,2),0) as total_money from anti_fraud_case_new where   suspect_phone_info like '%虚商%' and  insert_day  = '"+date_query+"'"),
        #27
        text(" SELECT   count(1) as app_total FROM anti_fraud_case_new  where app_name not in ('','-','无','韩文','符号','未命名','日文') and  insert_day  = '"+date_query+"'"),
        #28 本月案情总数
        text("select count(1) total_count FROM anti_fraud_case_new where DATE_FORMAT(STR_TO_DATE(insert_day, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT('"+date_query+"', '%Y-%m')"),
        #29 本月电信案情总数
        text("select count(1) total_count FROM anti_fraud_case_new where DATE_FORMAT(STR_TO_DATE(insert_day, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT('" + date_query + "', '%Y-%m') and suspect_phone_info like '%电信%'"),
    ]


    #匹配金额高的手机号归属地内容sql
    text_queries = [
        # 电信
        # 1
        text(
            "select suspect_phone_info from anti_fraud_case_new where involved_amount>=500000 and involved_amount<1000000 and  insert_day  = '"+date_query+"' and suspect_phone_number REGEXP'" + dx_phone + "'"),
        # 2
        text(
            "select suspect_phone_info from anti_fraud_case_new where involved_amount>=1000000 and involved_amount<10000000 and  insert_day  = '"+date_query+"' and suspect_phone_number REGEXP'" + dx_phone + "'"),
        # 3
        text(
            "select suspect_phone_info from anti_fraud_case_new where involved_amount>=10000000 and  insert_day  = '"+date_query+"' and suspect_phone_number REGEXP'" + dx_phone + "'"),
        # 联通
        # 4
        text(
            "select suspect_phone_info from anti_fraud_case_new where involved_amount>=500000 and involved_amount<1000000 and  insert_day  = '"+date_query+"' and suspect_phone_number REGEXP'" + lt_phone + "'"),
        # 5
        text(
            "select suspect_phone_info from anti_fraud_case_new where involved_amount>=1000000 and involved_amount<10000000 and  insert_day  = '"+date_query+"' and suspect_phone_number REGEXP'" + lt_phone + "'"),
        # 6
        text(
            "select suspect_phone_info from anti_fraud_case_new where involved_amount>=10000000 and  insert_day  = '"+date_query+"' and suspect_phone_number REGEXP'" + lt_phone + "'"),
        # 移动
        # 7
        text(
            "select suspect_phone_info from anti_fraud_case_new where involved_amount>=500000 and involved_amount<1000000 and  insert_day  = '"+date_query+"' and suspect_phone_number REGEXP'" + yd_phone + "'"),
        # 8
        text(
            "select suspect_phone_info from anti_fraud_case_new where involved_amount>=1000000 and involved_amount<10000000 and  insert_day  = '"+date_query+"' and suspect_phone_number REGEXP'" + yd_phone + "'"),
        # 9
        text(
            "select suspect_phone_info from anti_fraud_case_new where involved_amount>=10000000 and  insert_day  = '"+date_query+"' and suspect_phone_number REGEXP'" + yd_phone + "'"),

        # 广电
        # 10
        text(
            "select suspect_phone_info from anti_fraud_case_new where involved_amount>=500000 and involved_amount<1000000 and  insert_day  = '"+date_query+"' and suspect_phone_number REGEXP'" + gd_phone + "'"),
        # 11
        text(
            "select suspect_phone_info from anti_fraud_case_new where involved_amount>=1000000 and involved_amount<10000000 and  insert_day  = '"+date_query+"' and suspect_phone_number REGEXP'" + gd_phone + "'"),
        # 12
        text(
            "select suspect_phone_info from anti_fraud_case_new where involved_amount>=10000000 and  insert_day  = '"+date_query+"' and suspect_phone_number REGEXP'" + gd_phone + "'"),

        # 虚商
        # 13
        text(
            "select suspect_phone_info from anti_fraud_case_new where involved_amount>=500000 and involved_amount<1000000 and  insert_day  = '"+date_query+"' and suspect_phone_number REGEXP'" + xs_phone + "'"),
        # 14
        text(
            "select suspect_phone_info from anti_fraud_case_new where involved_amount>=1000000 and involved_amount<10000000 and  insert_day  = '"+date_query+"' and suspect_phone_number REGEXP'" + xs_phone + "'"),
        # 15
        text(
            "select suspect_phone_info from anti_fraud_case_new where involved_amount>=10000000 and  insert_day  = '"+date_query+"' and suspect_phone_number REGEXP'" + xs_phone + "'"),

        text("select  case_number from anti_fraud_case_new where involved_amount>=10000000 and  insert_day  = '"+date_query+"' ")
    ]

    hb_queries = [
        # 1
        text(
            "select count(*) as case_count from anti_fraud_case_new where STR_TO_DATE(insert_day, '%Y-%m-%d') = DATE_FORMAT('"+date_query+"', '%Y-%m-%d')"),
        # 2
        text("select count(*) as case_count from anti_fraud_case_new where STR_TO_DATE(insert_day, '%Y-%m-%d') = DATE_FORMAT(DATE_SUB('"+date_query+"', INTERVAL 1 DAY), '%Y-%m-%d')"),

        text("select count(*) as case_count from anti_fraud_case_new where DATE_FORMAT(STR_TO_DATE(insert_day, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT('"+date_query+"', '%Y-%m')"),

        text("select count(*) as case_count from anti_fraud_case_new where DATE_FORMAT(STR_TO_DATE(insert_day, '%Y-%m-%d'), '%Y-%m') =  DATE_FORMAT(DATE_SUB('"+date_query+"', INTERVAL 1 MONTH), '%Y-%m')"),

    ]

    #2025-06-04新增内容sql
    tz_queries = [
        #0  获取昨天中国电信手机号涉案数量
        text("select count(1) as total_count  from anti_fraud_case_new where  suspect_phone_info like '%电信%' and  STR_TO_DATE(insert_day, '%Y-%m-%d') = DATE_FORMAT(DATE_SUB('"+date_query+"', INTERVAL 1 DAY), '%Y-%m-%d')"),

        #1  获取重点监测的涉诈案件数量
        text("SELECT count(*) as total_count from anti_fraud_case_new where insert_day  = '"+date_query+"' and  suspect_phone_number REGEXP'" + int_mon + "'"),

        #2  获取50万以上的所有案情数量
        text("select count(*) as total_count from anti_fraud_case_new where involved_amount>=500000  and  insert_day  = '" + date_query + "'"),

        #3  获取50万以上的所有运营商案情数量
        text("select count(*) as total_count from anti_fraud_case_new where involved_amount>=500000  and  insert_day  = '" + date_query + "'  AND suspect_phone_info is not NULL"),

        #4  获取50万以上的电信案情数量
        text("select count(*) as total_count from anti_fraud_case_new where involved_amount>=500000  and  insert_day  = '" + date_query + "'  AND suspect_phone_info like '%电信%'"),
    ]

    #2025-06-09新增内容sql
    app_queries = [
        #1  获取每日app数量以及总金额
        text("select IFNULL(ROUND(sum( amount ) /10000,2),0) as amount,COUNT(1) as total_count from (SELECT SUM( involved_amount) as amount,COUNT(1) as appnum FROM anti_fraud_case_new  where app_name not in ('','-','无','韩文','符号','未命名','日文') and  insert_day  = '" + date_query + "' GROUP BY app_name ) as a"),
        #2 获取每日app名称以及金额
        text("SELECT IFNULL(ROUND(sum( involved_amount ) /10000,2),0) as amount,app_name,COUNT(1) as appnum FROM anti_fraud_case_new  where app_name not in ('','-','无','韩文','符号','未命名','日文') and  insert_day  = '" + date_query + "' GROUP BY app_name"),

    ]
    # 2025-06-12折线图sql
    line_chart_month_queries = [
        # 0 获取折线图本月电信数量取数数据
        text("select insert_day as x_date,COUNT(1) as total_count from anti_fraud_case_new where suspect_phone_info like '%电信%'  and DATE_FORMAT(insert_day, '%Y-%m') = DATE_FORMAT('" + date_query + "', '%Y-%m') GROUP BY insert_day ORDER BY insert_day asc"),
        # 1 获取折线图本月移动数量取数数据
        text("select insert_day as x_date,COUNT(1) as total_count from anti_fraud_case_new where suspect_phone_info like '%移动%'  and DATE_FORMAT(insert_day, '%Y-%m') = DATE_FORMAT('" + date_query + "', '%Y-%m') GROUP BY insert_day ORDER BY insert_day asc"),

        # 2 获取折线图本月联通数量取数数据
        text("select insert_day as x_date,COUNT(1) as total_count from anti_fraud_case_new where suspect_phone_info like '%联通%'  and DATE_FORMAT(insert_day, '%Y-%m') = DATE_FORMAT('" + date_query + "', '%Y-%m') GROUP BY insert_day ORDER BY insert_day asc"),

        # 3 获取折线图本月广电数量取数数据
        text("select insert_day as x_date,COUNT(1) as total_count from anti_fraud_case_new where suspect_phone_info like '%广电%'  and DATE_FORMAT(insert_day, '%Y-%m') = DATE_FORMAT('" + date_query + "', '%Y-%m') GROUP BY insert_day ORDER BY insert_day asc"),

        # 4 获取折线图本月虚商数量取数数据
        text("select insert_day as x_date,COUNT(1) as total_count from anti_fraud_case_new where suspect_phone_info like '%虚商%'  and DATE_FORMAT(insert_day, '%Y-%m') = DATE_FORMAT('" + date_query + "', '%Y-%m') GROUP BY insert_day ORDER BY insert_day asc"),
    ]
    line_chart_day_queries = [
        # 0 获取折线图当前时间往前7天电信数量取数数据
        text(
            "select insert_day as x_date,COUNT(1) as total_count from anti_fraud_case_new where suspect_phone_info like '%电信%'  and STR_TO_DATE(insert_day, '%Y-%m-%d') > DATE_FORMAT(DATE_SUB('"+date_query+"', INTERVAL 7 DAY), '%Y-%m-%d') GROUP BY insert_day ORDER BY insert_day asc"),
        # 1 获取折线图当前时间往前7天移动数量取数数据
        text(
            "select insert_day as x_date,COUNT(1) as total_count from anti_fraud_case_new where suspect_phone_info like '%移动%'  and STR_TO_DATE(insert_day, '%Y-%m-%d') > DATE_FORMAT(DATE_SUB('"+date_query+"', INTERVAL 7 DAY), '%Y-%m-%d') GROUP BY insert_day ORDER BY insert_day asc"),

        # 2 获取折线图当前时间往前7天联通数量取数数据
        text(
            "select insert_day as x_date,COUNT(1) as total_count from anti_fraud_case_new where suspect_phone_info like '%联通%'  and STR_TO_DATE(insert_day, '%Y-%m-%d') > DATE_FORMAT(DATE_SUB('"+date_query+"', INTERVAL 7 DAY), '%Y-%m-%d') GROUP BY insert_day ORDER BY insert_day asc"),

        # 3 获取折线图当前时间往前7天广电数量取数数据
        text(
            "select insert_day as x_date,COUNT(1) as total_count from anti_fraud_case_new where suspect_phone_info like '%广电%'  and STR_TO_DATE(insert_day, '%Y-%m-%d') > DATE_FORMAT(DATE_SUB('"+date_query+"', INTERVAL 7 DAY), '%Y-%m-%d') GROUP BY insert_day ORDER BY insert_day asc"),

        # 4 获取折线图当前时间往前7天虚商数量取数数据
        text(
            "select insert_day as x_date,COUNT(1) as total_count from anti_fraud_case_new where suspect_phone_info like '%虚商%'  and STR_TO_DATE(insert_day, '%Y-%m-%d') > DATE_FORMAT(DATE_SUB('"+date_query+"', INTERVAL 7 DAY), '%Y-%m-%d') GROUP BY insert_day ORDER BY insert_day asc"),
    ]
    #查询sql
    results = execute_queries(queries)
    text_results = execute_queries(text_queries)
    hb_results = execute_queries(hb_queries)
    tz_results = execute_queries(tz_queries)

    #APP查询内容
    app_result =  execute_queries(app_queries)

    # 每日app金额,数量
    app_name_result = app_result[1]
    app_top_list = results[6]
    app_dict_amount = {}
    for app in app_name_result:
        app_name = str(app['app_name'])
        app_name = re.sub(r"\s+", "", app_name)
        split = app_name.split(',')
        app_amount = app['amount']
        app_num = app['appnum']
        for sq in split:
            amount = app_dict_amount.get(sq,0)
            amount += app_amount
            app_dict_amount.update({sq:amount})
    app_list_top10_str = "top10APP包括：{}".format("、".join(
        [f"{item['app_name']}" for item in
         app_top_list[:10]])) if app_top_list[:10] else ""
    # print(app_name_result)
    app_list = app_name_statistics(date_query)
    sorted_app_list = sorted(app_list, key=lambda x: x.get("num", 0), reverse=True)
    add_app_dict = {}
    for app in sorted_app_list:
        add_app_dict.update({app['app_name']: app['num']})
    # print(app_dict)
    # 新增APP涉案金额
    add_app_list = []
    app_total = int(results[27][0]["app_total"])
    for key,value in add_app_dict.items():
        if key in app_dict_amount.keys():
            amount = app_dict_amount[key]
            num = int(value)
            app_round = round((num / app_total) * 100, 2)
            add_app_list.append({'amount': amount, 'num': value, 'app_name': key, 'round': app_round})
    # print(add_app_list)
    sorted_add_app_list = sorted(add_app_list, key=lambda x: x.get("num", 0), reverse=True)
    # deduplicated = {item['app_name']: item for item in sorted_add_app_list}.values()
    # sorted_add_app_list = list(deduplicated)
    sorted_add_app_list = sorted_add_app_list[:10]
    add_app_str = "其中新增涉诈APP：{}".format("；".join([f"“{item['app_name']}”，涉案{item['num']}起(占比{item['round']}%)，涉案金额{item['amount']}万元" for item in sorted_add_app_list])) if sorted_add_app_list else ""
    app_list_num = len(sorted_app_list)
    # print(sorted_app_list)
    app_str_list =  "新增top10APP，包括：{}".format("、".join([f"{item['app_name']}" for item in sorted_add_app_list])) if sorted_add_app_list else ""
    #涉案APP TOP20
    app_top_name_list = []
    for it in app_top_list:
        app_top_name_list.append("'"+it['app_name']+"'")
    app_top_name_sql = "select app_name, COUNT(*) as total_count from anti_fraud_case_new where DATE_FORMAT(insert_day, '%Y-%m-%d') BETWEEN DATE_FORMAT(DATE_SUB('"+date_query+"', INTERVAL 30 DAY), '%Y-%m-%d') and '"+date_query+"' and app_name in ("+",".join(app_top_name_list)+") GROUP BY app_name"
    # print(app_top_name_sql)
    app_top_name_result = query_sql(app_top_name_sql)
    # print(app_top_name_result)
    app_top_name_dict = {}
    for it in app_top_name_result:
        app_top_name_dict.update({it['app_name']: it['total_count']})
    for it in app_top_list:
        app_name = it['app_name']
        if app_name in add_app_dict.keys():
            it['is_addition'] = '是'
        else:
            it['is_addition'] = '否'
        it['accumulate'] = app_top_name_dict.get(app_name,0)
    #日环比数据计算
    now_count =  hb_results[0][0]["case_count"]
    last_count = hb_results[1][0]["case_count"]
    day_hb = round((now_count - last_count) / last_count * 100, 2) if last_count != 0 else 100.00
    if day_hb < 0:
        day_hb_str = f"降低{day_hb:.2f}"
    else:
        day_hb_str = f"增加{day_hb:.2f}"
    # print("日环比：{}".format(day_hb)+"%")

    #月环比数据计算
    now_mon_count = hb_results[2][0]["case_count"]
    last_mon_count = hb_results[3][0]["case_count"]
    mon_hb = round((now_mon_count - last_mon_count) / last_mon_count * 100, 2) if last_mon_count != 0 else 100.00
    if mon_hb < 0:
        mon_hb_str = f"降低{mon_hb:.2f}"
    else:
        mon_hb_str = f"增加{mon_hb:.2f}"


    top_5 = results[5][:5]
    # 使用列表推导式拼接字符串
    result_str = "、".join([f"{item['type']}占比{item['percentage']}%" for item in top_5])
    app_top_5 = results[6][:5]
    # 使用列表推导式拼接字符串
    app_result_str = "、".join([f"{item['app_name']}" for item in app_top_5])


    #计算中国电信手机号码涉案环比
    dx_day_count = results[22][0]["total_count"]
    dx_yesterday_count = tz_results[0][0]["total_count"]
    dx_day_hb = round((dx_day_count - dx_yesterday_count) / dx_yesterday_count * 100, 2) if dx_yesterday_count != 0 else 100.00
    if dx_day_hb < 0:
        dx_day_hb_str = f"降低{dx_day_hb:.2f}"
    else:
        dx_day_hb_str = f"增加{dx_day_hb:.2f}"

    #拼装折线图对应数据格式
    date_obj = datetime.strptime(date_query, "%Y-%m-%d")
    day = date_obj.day
    line_chart_result = []
    if day <= 6:
        line_chart_result = execute_queries(line_chart_day_queries)
    else:
        line_chart_result = execute_queries(line_chart_month_queries)
    zx_data = line_chart_result[0]
    dx_x_data = [item['x_date'] for item in zx_data]  # X轴：日期
    dx_y_data = [item['total_count'] for item in zx_data]
    yd_zx_data = line_chart_result[1]
    yd_x_data = [item['x_date'] for item in yd_zx_data]  # X轴：日期
    yd_y_data = [item['total_count'] for item in yd_zx_data]
    lt_zx_data = line_chart_result[2]
    lt_x_data = [item['x_date'] for item in lt_zx_data]  # X轴：日期
    lt_y_data = [item['total_count'] for item in lt_zx_data]
    gd_zx_data = line_chart_result[3]
    gd_x_data = [item['x_date'] for item in gd_zx_data]  # X轴：日期
    gd_y_data = [item['total_count'] for item in gd_zx_data]
    xs_zx_data = line_chart_result[4]
    xs_x_data = [item['x_date'] for item in xs_zx_data]  # X轴：日期
    xs_y_data = [item['total_count'] for item in xs_zx_data]

    #手机号码占比
    dx_count = results[22][0]["total_count"]
    lt_count = results[23][0]["total_count"]
    yd_count = results[24][0]["total_count"]
    gd_count = results[25][0]["total_count"]
    xs_count = results[26][0]["total_count"]
    total_count = dx_count + lt_count + yd_count + gd_count + xs_count

    #电信本月累计
    total_month_count = results[28][0]['total_count']
    dx_month_count = results[29][0]['total_count']
    dx_month_round = round((dx_month_count / total_month_count) * 100, 2)
    # 106
    df_106 = get_106_df(date_query)
    df_106_mon = get_106_df_month(date_query[:7])
    report_data = {
        "date_range": date_range,
        "case_count": str(results[0][0]["case_count"]),
        "url_count": str(results[1][0]["total_count"]),
        "url_prop": str(results[1][0]["percentage"]),
        "app_count": str(results[2][0]["total_count"]),
        "app_prop": str(results[2][0]["percentage"]),
        "sms_count":str(results[3][0]["total_count"]),
        "sms_prop": str(results[3][0]["percentage"]),
        "telephone_count": str(results[22][0]["total_count"]),
        "intel_count":str(results[4][0]["tel_count"]),
        "international_count": str(df_106),
        "international_count_mon": str(df_106_mon),
        "international_prop": "XX",
        # "overall_situation": "本日案情4226条，95106未出现涉诈案件。其中url类涉案XX起，占比XX%；APP类涉案1032起；占比24.42%；电话、短信引流515起，占比12.19%（中国电信手机号码62起）；国际号码涉案125起，106开头行短端口涉案XX起，占比0.39%。",
        "case_distribution_desc": result_str,
        "case_types": results[5],
        "app_case_desc": app_result_str,
        "app_top_list": app_top_list,
        "app_count_list":str(len(app_list)),
        "app_str_list":app_str_list,
        "app_total":str(results[27][0]["app_total"]),
        #"loss_distribution": "本日案情涉案金额2.35亿元，其中中国电信号码涉案金额944万，50万以上的案件2个，100万以上1个，未发现1000万以上金额涉案。",
        "loss_amount": str(results[0][0]["total_amout"]),
        "telephone_amount": str(results[22][0]["total_money"]),
        "high_amount_5":str(results[7][0]["total_count"]),
        "high_amount_10": str(results[8][0]["total_count"]),
        "high_amount_100": str(results[9][0]["total_count"]),

        "lt_telephone_amount": str(results[23][0]["total_money"]),
        "lt_high_amount_5": str(results[10][0]["total_count"]),
        "lt_high_amount_10": str(results[11][0]["total_count"]),
        "lt_high_amount_100": str(results[12][0]["total_count"]),

        "yd_telephone_amount": str(results[24][0]["total_money"]),
        "yd_high_amount_5": str(results[13][0]["total_count"]),
        "yd_high_amount_10": str(results[14][0]["total_count"]),
        "yd_high_amount_100": str(results[15][0]["total_count"]),

        "gd_telephone_amount":str(results[25][0]["total_money"]),
        "gd_high_amount_5": str(results[16][0]["total_count"]),
        "gd_high_amount_10": str(results[17][0]["total_count"]),
        "gd_high_amount_100": str(results[18][0]["total_count"]),

        "xs_telephone_amount": str(results[26][0]["total_money"]),
        "xs_high_amount_5": str(results[19][0]["total_count"]),
        "xs_high_amount_10": str(results[20][0]["total_count"]),
        "xs_high_amount_100": str(results[21][0]["total_count"]),


        "dx_phone_list_50": text_results[0],
        "dx_phone_list_100": text_results[1],
        "dx_phone_list_1000": text_results[2],

        "lt_phone_list_50": text_results[3],
        "lt_phone_list_100": text_results[4],
        "lt_phone_list_1000": text_results[5],

        "yd_phone_list_50": text_results[6],
        "yd_phone_list_100": text_results[7],
        "yd_phone_list_1000": text_results[8],

        "gd_phone_list_50": text_results[9],
        "gd_phone_list_100": text_results[10],
        "gd_phone_list_1000": text_results[11],

        "xs_phone_list_50": text_results[12],
        "xs_phone_list_100": text_results[13],
        "xs_phone_list_1000": text_results[14],

        "phone_height_1000": text_results[15],


        "day_hb":str(day_hb_str),
        "mon_count":str(now_mon_count),
        "mon_hb":str(mon_hb_str),

        "phone_total": str(total_count),
        "tele_phone_total": str(dx_count),
        "tele_phone_proportion": "{:.2f}".format(dx_count / total_count * 100) if total_count != 0 else "0.00%",
        "move_phone_total": str(yd_count),
        "move_phone_proportion": "{:.2f}".format(yd_count / total_count * 100) if total_count != 0 else "0.00%",
        "unicom_phone_total": str(lt_count),
        "unicom_phone_proportion": "{:.2f}".format(lt_count / total_count * 100) if total_count != 0 else "0.00%",
        "sva_phone_total": str(gd_count),
        "sva_phone_proportion": "{:.2f}".format(gd_count / total_count * 100) if total_count != 0 else "0.00%",
        "vc_phone_total": str(xs_count),
        "vc_phone_proportion": "{:.2f}".format(xs_count / total_count * 100) if total_count != 0 else "0.00%",


        #2025-06-04新增内容参数

        #电信手机号涉案数量
        "dx_sa_count": str(results[22][0]["total_count"]),
        #电信环比
        "dx_hb_count": str(dx_day_hb_str),

        #重点监测数据
        "key_monitor_data": intensive_monitoring,
        #重点监测涉案数量
        "key_monitor_count": str(tz_results[1][0]["total_count"]),

        #折线图数据
        "dx_x_data": dx_x_data,
        "dx_y_data": dx_y_data,
        "yd_x_data": yd_x_data,
        "yd_y_data": yd_y_data,
        "lt_x_data": lt_x_data,
        "lt_y_data": lt_y_data,
        "gd_x_data": gd_x_data,
        "gd_y_data": gd_y_data,
        "xs_x_data": xs_x_data,
        "xs_y_data": xs_y_data,

        #50万以上案情数量
        "case_50_up": str(tz_results[2][0]["total_count"]),
        #50万以上运营商案情数量
        "operator_50_up": str(tz_results[3][0]["total_count"]),
        #50万以上电信案情数量
        "operator_dx_50_up":  str(tz_results[4][0]["total_count"]),


        #2025-06-09新增参数内容

        #新增APP增加个数
        "app_list_num":str(app_list_num),

        #今日APP案情金额
        "app_now_money":str(app_result[0][0]["amount"]),

        #今日APP新增案数
        "app_now_count":str(app_result[0][0]["total_count"]),
        #今日APP新增情况
        "add_app_str":add_app_str,
        #每日top10情况
        "app_list_top10_str": app_list_top10_str,
        "app_list_top10": sorted_add_app_list[:10],
        #电信本月累计
        "dx_month_count": str(dx_month_count),
        "dx_month_round": str(dx_month_round),
    }
    file_stream = WordReportUtils.generate_case_analysis_report(report_data,word_file)
    # 返回文件下载
    # return WordReportUtils.send_word_report(file_stream, "案情分析日报.docx")
    print("文档生成结束")


def safe_get_result(data, index, key=None, default=""):
    """
    安全获取 text_results 中的数据

    :param data: 查询结果列表，如 text_results[index]
    :param index: 外层索引
    :param key: 字段名，例如 'suspect_phone_info'
    :param default: 默认值
    :return: 提取的字符串或默认值
    """
    try:
        if isinstance(data, list) and len(data) > index:
            item = data[index]
            if isinstance(item, list) and len(item) > 0:
                value = item[0].get(key) if key else item[0]
                return str(value) if value is not None else default
        return default
    except Exception:
        return default


def execute_queries(queries):
    """
    执行多个SQL查询并返回结果数组
    :param queries: SQL查询语句列表
    :return: 查询结果的字典列表
    """
    results = []
    for query in queries:
        # print(query)
        with engine.connect() as connection:
            result = connection.execute(query)
            rows = result.fetchall()
            columns = result.keys()
            result_dict = [dict(zip(columns, row)) for row in rows]
            results.append(result_dict)
    return results
if __name__ == '__main__':
    get_data()