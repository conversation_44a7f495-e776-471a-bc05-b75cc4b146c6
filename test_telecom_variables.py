#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试电信专项变量配置
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.idea/Ready/test/50万趋势'))

def test_telecom_variables():
    """测试电信专项变量配置"""
    print("🧪 测试电信专项变量配置")
    print("=" * 60)
    
    try:
        from prompt_manager import prompt_manager
        from statistics_service import StatisticsService
        from data_service import DataService
        
        # 获取真实数据
        print("📊 获取真实数据...")
        data_service = DataService()
        df, case_details = data_service.get_comprehensive_data(30, include_details=True)
        
        if df is None or df.empty:
            print("❌ 无法获取数据，使用模拟数据测试")
            # 使用模拟数据
            test_data = {
                'start_date': '2024-01-01',
                'end_date': '2024-01-30',
                'days': 30,
                'total_cases': 1000,
                'daily_avg': 33.3,
                'telecom_total': 297,
                'unicom_total': 184,
                'mobile_total': 519,
                'telecom_ratio': 0.297,
                'unicom_ratio': 0.184,
                'mobile_ratio': 0.519,
                # 电信专项变量
                'telecom_recent_7_total': 69,
                'telecom_prev_7_total': 65,
                'telecom_current_week_total': 69,
                'telecom_last_week_total': 65,
                'telecom_recent_7_avg': 9.9,
                'telecom_prev_7_avg': 9.3,
                'telecom_7days_change': 0.062,
                'telecom_week_change': 0.062,
                'recent_7_days': '[8, 10, 9, 11, 12, 9, 10]',
                'recent_7_avg': 9.9,
                'vs_prev_7_days': 0.062,
                'current_week_total': 233,
                'last_week_total': 221,
                'week_change': 0.054,
                'current_month_total': 1000,
                'last_month_same_period_total': 950,
                'month_change': 0.053
            }
        else:
            print("✅ 获取到真实数据，计算统计信息...")
            statistics_service = StatisticsService()
            test_data = statistics_service.calculate_comprehensive_stats(df, case_details)
        
        # 生成提示词
        print("\n📝 生成提示词...")
        prompt = prompt_manager.format_prompt('trend_analysis', **test_data)
        
        # 检查电信专项变量是否正确替换
        print("\n🔍 检查电信专项变量替换情况：")
        print("-" * 60)
        
        # 提取第2部分的内容
        lines = prompt.split('\n')
        in_telecom_section = False
        for line in lines:
            if '**2. 近期电信案件趋势：**' in line:
                in_telecom_section = True
                print(line)
            elif in_telecom_section:
                if line.startswith('      **3.') or line.startswith('      **4.'):
                    break
                print(line)
        
        print("\n" + "-" * 60)
        print("✅ 变量配置检查：")
        
        # 检查关键变量
        telecom_vars = [
            'telecom_recent_7_total',
            'telecom_prev_7_total', 
            'telecom_current_week_total',
            'telecom_last_week_total'
        ]
        
        for var in telecom_vars:
            if f'{{{var}}}' in prompt:
                print(f"❌ 变量 {var} 未被替换")
            else:
                value = test_data.get(var, 'N/A')
                print(f"✅ 变量 {var}: {value}")
        
        print(f"\n📋 数据验证：")
        print(f"   近7天电信案件：{test_data.get('telecom_recent_7_total', 'N/A')}起")
        print(f"   前7天电信案件：{test_data.get('telecom_prev_7_total', 'N/A')}起")
        print(f"   本周电信案件：{test_data.get('telecom_current_week_total', 'N/A')}起")
        print(f"   上周电信案件：{test_data.get('telecom_last_week_total', 'N/A')}起")
        
        # 检查是否还有未替换的变量
        import re
        unresolved_vars = re.findall(r'\{([^}]+)\}', prompt)
        if unresolved_vars:
            print(f"\n⚠️ 发现未解析的变量: {unresolved_vars}")
        else:
            print(f"\n✅ 所有变量都已正确解析")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_telecom_variables()
