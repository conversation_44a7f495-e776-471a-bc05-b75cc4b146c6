#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试APP过滤功能
"""

import pandas as pd
import re
from datetime import datetime, timedelta

def debug_filter_invalid_app_names(df):
    """调试版本的过滤函数"""
    if df is None or df.empty:
        return df

    print(f"🔍 调试开始 - 原始APP数量: {df['app_name'].nunique()}")
    print(f"📋 原始APP列表: {sorted(df['app_name'].unique())}")

    # 创建APP名称的清理副本
    df_work = df.copy()
    df_work['app_name_clean'] = df_work['app_name'].str.strip()

    # 过滤条件
    mask = pd.Series(True, index=df_work.index)
    original_mask = mask.copy()

    # 1. 过滤空值和空字符串
    mask &= df_work['app_name_clean'].notna()
    mask &= df_work['app_name_clean'] != ''
    print(f"步骤1 - 过滤空值后剩余: {mask.sum()}")

    # 2. 过滤纯数字（1-9999）
    digit_mask = ~df_work['app_name_clean'].str.match(r'^\d+$', na=False)
    removed_digits = df_work[~digit_mask]['app_name_clean'].unique()
    if len(removed_digits) > 0:
        print(f"步骤2 - 过滤纯数字: {removed_digits}")
    mask &= digit_mask

    # 3. 过滤问号模式
    question_mask = ~df_work['app_name_clean'].str.match(r'^[?？]+$', na=False)
    removed_questions = df_work[~question_mask]['app_name_clean'].unique()
    if len(removed_questions) > 0:
        print(f"步骤3 - 过滤问号: {removed_questions}")
    mask &= question_mask

    # 4. 过滤星号模式
    star_mask = ~df_work['app_name_clean'].str.match(r'^\*+$', na=False)
    removed_stars = df_work[~star_mask]['app_name_clean'].unique()
    if len(removed_stars) > 0:
        print(f"步骤4 - 过滤星号: {removed_stars}")
    mask &= star_mask

    # 9. 过滤无意义词汇（不区分大小写）
    meaningless_patterns = [
        r'^无$', r'^wu$', r'^未知$', r'^unknown$', r'^null$',
        r'^其他$', r'^其它$', r'^不详$', r'^不明$', r'^暂无$', r'^待定$',
        r'^test$', r'^demo$', r'^temp$', r'^临时$', r'^测试$', r'^特殊字符$',
        r'^韩文$', r'^日文$', r'^符号$', r'^特殊符号$', r'^已删除$',
        r'^受害人无法提供$', r'^teams?$', r'^app$', r'^APP$'
    ]
    
    for pattern in meaningless_patterns:
        pattern_mask = ~df_work['app_name_clean'].str.match(pattern, case=False, na=False)
        removed_by_pattern = df_work[~pattern_mask]['app_name_clean'].unique()
        if len(removed_by_pattern) > 0:
            print(f"步骤9 - 模式 {pattern} 过滤: {removed_by_pattern}")
        mask &= pattern_mask

    # 9.5. 过滤带引号的特殊模式
    quote_patterns = [
        (r'"韩文APP"', '韩文APP'),
        (r'"特殊符号"', '特殊符号'),
        (r'"APP"', 'APP'),
        (r'"app"', 'app')
    ]
    
    for pattern, desc in quote_patterns:
        quote_mask = ~df_work['app_name_clean'].str.contains(pattern, case=False, na=False, regex=False)
        removed_quotes = df_work[~quote_mask]['app_name_clean'].unique()
        if len(removed_quotes) > 0:
            print(f"步骤9.5 - 过滤引号模式 {desc}: {removed_quotes}")
        mask &= quote_mask

    # 11. 过滤长度过短的名称
    known_short_apps = ['QQ', 'UC', '58', '12306', '360', 'WPS']
    short_mask = (df_work['app_name_clean'].str.len() > 2) | df_work['app_name_clean'].str.upper().isin([app.upper() for app in known_short_apps])
    removed_short = df_work[~short_mask]['app_name_clean'].unique()
    if len(removed_short) > 0:
        print(f"步骤11 - 过滤短名称: {removed_short}")
    mask &= short_mask

    # 应用过滤条件
    filtered_df = df_work[mask].copy()
    
    # 移除工作列
    filtered_df = filtered_df.drop('app_name_clean', axis=1)

    print(f"✅ 过滤完成 - 剩余APP数量: {filtered_df['app_name'].nunique()}")
    print(f"📋 过滤后APP列表: {sorted(filtered_df['app_name'].unique())}")

    # 显示被过滤的APP
    removed_apps = set(df['app_name'].unique()) - set(filtered_df['app_name'].unique())
    if removed_apps:
        print(f"🗑️ 被过滤的APP: {sorted(list(removed_apps))}")

    return filtered_df

def create_test_data():
    """创建包含问题APP的测试数据"""
    test_data = []
    
    # 包含您提到的问题APP
    problem_apps = [
        'app', 'APP', '韩文APP', '"韩文APP"', '"特殊符号"', '"APP"', '"app"',
        '微信', '支付宝', '抖音', '淘宝', 'QQ',  # 有效APP
        '无', '未知', '???', '***', '1', '2'  # 无效APP
    ]
    
    # 生成测试数据
    for i, app in enumerate(problem_apps):
        test_data.append({
            'date_col': datetime.now() - timedelta(days=1),
            'app_name': app,
            'case_count': i + 1
        })
    
    return pd.DataFrame(test_data)

def main():
    """主函数"""
    print("🐛 APP过滤功能调试")
    print("=" * 60)
    
    # 创建测试数据
    df = create_test_data()
    print(f"📊 测试数据创建完成，共 {len(df)} 条记录")
    
    # 执行调试过滤
    print("\n🔍 开始调试过滤...")
    filtered_df = debug_filter_invalid_app_names(df)
    
    print(f"\n📈 调试结果:")
    print(f"  - 原始记录数: {len(df)}")
    print(f"  - 过滤后记录数: {len(filtered_df)}")
    print(f"  - 过滤效率: {(len(df) - len(filtered_df)) / len(df) * 100:.1f}%")

if __name__ == "__main__":
    main()
