#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的API接口测试脚本
快速测试 API 接口是否可用
"""

import requests
import json

def simple_api_test():
    """简单快速的API测试"""
    api_url = "http://182.43.100.249:8050/generate"

    print("🔍 测试API接口...")
    print(f"地址: {api_url}")
    print("-" * 40)

    # 简单的测试数据
    test_data = {
        "prompt": "你好，请回复：API测试成功",
        "temperature": 0.1,
        "max_tokens": 50
    }

    try:
        # 发送请求
        response = requests.post(
            api_url,
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )

        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            print("✅ API接口通畅!")
            try:
                result = response.json()
                print("响应内容:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
            except:
                print("响应内容(文本):")
                print(response.text)
        else:
            print(f"❌ 接口返回错误: {response.status_code}")
            print(f"错误信息: {response.text}")

    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        print("请检查:")
        print("  - 网络连接")
        print("  - API地址是否正确")
        print("  - 服务器是否运行")

    except requests.exceptions.Timeout:
        print("❌ 请求超时")

    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    simple_api_test()
