#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试进度条功能
"""

import sys
import os
import time
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.idea/Ready'))

# 导入主类
from 测试_copy import TelecomMonitor

def test_progress_bar():
    """测试进度条功能"""
    print("🧪 测试进度条功能")
    print("=" * 50)
    
    monitor = TelecomMonitor()
    
    # 测试基础进度条
    print("\n1. 测试基础进度条:")
    total_steps = 10
    for i in range(total_steps + 1):
        monitor.display_progress_bar(i, total_steps, f"处理步骤 {i}")
        time.sleep(0.3)
    
    # 测试带ETA的进度条
    print("\n2. 测试带ETA的进度条:")
    start_time = time.time()
    total_steps = 8
    for i in range(total_steps + 1):
        monitor.display_progress_bar(i, total_steps, f"AI分析步骤 {i}", 
                                   show_eta=True, start_time=start_time)
        time.sleep(0.5)
    
    # 测试状态显示
    print("\n3. 测试状态显示:")
    monitor.display_analysis_status("数据预处理", "正在加载数据库连接...")
    time.sleep(1)
    monitor.display_analysis_status("模型调用", "正在连接Ollama服务...")
    time.sleep(1)
    monitor.display_step_summary("数据预处理", 2.3, True)
    monitor.display_step_summary("模型调用", 15.7, True)
    
    print("\n✅ 进度条功能测试完成!")

def test_analysis_flow():
    """测试完整的分析流程（模拟）"""
    print("\n🔬 测试完整分析流程")
    print("=" * 50)
    
    monitor = TelecomMonitor()
    
    # 模拟分析选项
    selections = ['1', '2']  # 趋势分析 + 案情特征分析
    
    print("模拟AI分析流程...")
    
    # 模拟数据
    import pandas as pd
    from datetime import timedelta
    
    # 创建模拟数据
    dates = pd.date_range(start='2024-01-01', periods=30, freq='D')
    df = pd.DataFrame({
        '日期': dates,
        '电信': [2, 1, 3, 0, 2, 4, 1, 2, 3, 1, 2, 0, 1, 3, 2, 4, 1, 2, 0, 3, 2, 1, 4, 2, 1, 3, 0, 2, 1, 3],
        '联通': [1, 2, 1, 1, 0, 2, 3, 1, 2, 0, 1, 2, 3, 1, 0, 2, 1, 3, 2, 1, 0, 2, 1, 3, 2, 1, 0, 1, 2, 1],
        '移动': [0, 1, 2, 1, 1, 1, 0, 2, 1, 2, 0, 1, 2, 0, 1, 1, 2, 0, 1, 2, 1, 0, 2, 1, 0, 1, 2, 1, 0, 2]
    })
    
    case_details = pd.DataFrame({
        'suspect_phone_info': ['电信', '联通', '移动', '电信', '联通'] * 10,
        '简要案情': ['网络诈骗'] * 50,
        'involved_amount': [600000, 700000, 800000, 550000, 650000] * 10,
        '年龄': [25, 30, 35, 40, 45] * 10,
        '案件子类': ['网络购物诈骗', '投资理财诈骗', '冒充客服诈骗', '刷单诈骗', '贷款诈骗'] * 10
    })
    
    # 运行带进度条的分析
    try:
        results = monitor.run_analysis_with_progress(selections, df, case_details)
        
        if results and not results.get("error"):
            print("\n📊 分析结果预览:")
            for key, result_data in results.items():
                name = result_data.get('name', key)
                result = result_data.get('result', '无结果')
                print(f"- {name}: {result[:100]}..." if len(str(result)) > 100 else f"- {name}: {result}")
        else:
            print(f"❌ 分析失败: {results.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {str(e)}")
    
    print("\n✅ 分析流程测试完成!")

if __name__ == "__main__":
    print("🚀 开始测试新的进度条功能")
    print("=" * 60)
    
    # 测试进度条
    test_progress_bar()
    
    # 测试分析流程
    test_analysis_flow()
    
    print("\n🎉 所有测试完成!")
