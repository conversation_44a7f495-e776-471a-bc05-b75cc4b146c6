#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试光标稳定性
"""

import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.idea/Ready/test/50万趋势'))

def test_cursor_stability():
    """测试光标稳定性"""
    print("🧪 测试光标稳定性")
    print("=" * 60)
    
    try:
        from ai_service import AIAnalysisProgressBar
        
        # 创建进度条实例
        progress_bar = AIAnalysisProgressBar()
        progress_bar.start(772, "趋势分析")
        
        print("观察光标是否稳定，不跳动...")
        
        # 模拟进度条运行，观察光标行为
        total_time = 8  # 总演示时间8秒
        update_interval = 0.1  # 更频繁的更新来测试光标稳定性
        
        for i in range(int(total_time / update_interval)):
            elapsed = i * update_interval
            
            # 根据时间自动推进阶段
            if elapsed >= 2 and progress_bar.current_stage == 0:
                progress_bar.next_stage()
            elif elapsed >= 3 and progress_bar.current_stage == 1:
                progress_bar.next_stage()
            elif elapsed >= 4 and progress_bar.current_stage == 2:
                progress_bar.next_stage()
                
            progress_bar.display_progress()
            time.sleep(update_interval)
        
        # 完成进度条
        progress_bar.complete()
        
        print("\n✅ 光标稳定性测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 光标稳定性优化测试")
    print("=" * 60)
    
    print("\n🎯 优化方案:")
    print("1. ❌ 不使用复杂的ANSI光标控制（避免兼容性问题）")
    print("2. ✅ 使用固定长度的输出行（ljust(100)）")
    print("3. ✅ 减少光标位置变化")
    print("4. ✅ 保持简单可靠的实现")
    
    test_cursor_stability()
    
    print("\n💡 优化原理:")
    print("- 每次输出都是固定100字符长度")
    print("- 光标始终停在行尾的固定位置")
    print("- 避免了复杂的ANSI转义序列")
    print("- 兼容性更好，不会增加滚屏风险")
    
    print("\n🎉 光标稳定性优化完成！")
    print("现在光标应该不会跳动了，同时保持了兼容性。")
